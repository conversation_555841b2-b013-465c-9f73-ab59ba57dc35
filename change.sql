-- 旅客补偿银行信息表
create table et_compensate_bank_info
(
    id                bigint auto_increment primary key,
    card_no           varchar(30),
    card_holder       varchar(50),
    bank_name         varchar(100),
    mobile_no         varchar(12) not null,
    refund_no         varchar(30),
    flt_refund_id     bigint,
    compensate_no     varchar(30) not null,
    compensate_type   varchar(30) not null,
    department        varchar(30) not null,
    openid            varchar(30),
    compensate_reason varchar(30)
);


-- 旅客补偿附件表
create table et_compensate_img
(
    id              bigint auto_increment primary key,
    flt_refund_id   bigint      not null,
    img_url         varchar(500),
    refund_no       varchar(32) not null,
    source          varchar(10) not null,
    status          varchar(10) not null,
    operator        varchar(50) not null,
    create_time     date        not null,
    refund_audit_id bigint
);
