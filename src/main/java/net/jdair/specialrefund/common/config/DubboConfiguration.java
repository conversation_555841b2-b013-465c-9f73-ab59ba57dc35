package net.jdair.specialrefund.common.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

//@Configuration
public class DubboConfiguration {

//	@Autowired
//	private ESBConfig esbConfig;

//	public DubboConfiguration() {
//		System.out.println("DubboConfiguration容器启动初始化。。。");
//	}

//	@Bean
//	public RegistryConfig registry() {
//		RegistryConfig registryConfig = new RegistryConfig();
//		registryConfig.setAddress(esbConfig.getRegistryAddress());
//		registryConfig.setProtocol(esbConfig.getRegistryProtocol());
//		return registryConfig;
//	}
//
//	@Bean
//	public ApplicationConfig applicationConfig() {
//		ApplicationConfig applicationConfig = new ApplicationConfig();
//		applicationConfig.setName(esbConfig.getApplicationName());
//		applicationConfig.setOwner(esbConfig.getApplicationOwner());
//		return applicationConfig;
//	}
//
//	@Bean
//	public ProtocolConfig protocol() {
//		ProtocolConfig protocolConfig = new ProtocolConfig();
//		protocolConfig.setPort(Integer.valueOf(esbConfig.getProtocolPort()));
//		return protocolConfig;
//	}
}
