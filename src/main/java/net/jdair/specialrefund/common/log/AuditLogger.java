package net.jdair.specialrefund.common.log;

import com.alibaba.fastjson.JSON;
import net.jdair.specialrefund.common.util.ClientIpUtils;
import net.jdair.specialrefund.common.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.servlet.http.HttpServletRequest;

/**
 * Created by la<PERSON><PERSON> on 20/11/4.
 */
@Component
public class AuditLogger {

    private static final Logger log = LoggerFactory.getLogger(AuditLogger.class);

    private static HttpServletRequest servletRequest;

    public static void info(AuditLogVO auditLogVO) {
        log.info(JSON.toJSONString(auditLogVO));
    }

    public static void info(BizModuleType bizModule, OperationType operation, String operator, String operateTime) {
        info(bizModule, operation, operator, operateTime, "", "");
    }

    public static void info(BizModuleType bizModule, OperationType operation, String operator, String operateTime, String tag,  String content) {
        // 时间戳
        MDC.put("timestamp", DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss.SSS"));

        // 业务模块。 eg. ORDER
        MDC.put("bizModule", bizModule.name());

        // 操作动作。 eg. MODIFY_PASSWORD
        MDC.put("operation", operation.name());

        // 自定义信息(建议存放json便于解析)。 eg. {\"name\": \"bob\", \"password\": \"123456\"}
        MDC.put("content", content);

        // 客户端请求IP。 eg. ************
        MDC.put("ip", ClientIpUtils.getClientIp(servletRequest));

        // 操作人。 eg. 张三
        MDC.put("operator", operator);

        // 操作时间。eg. 2022-02-15 20:00:05
        MDC.put("operateTime", operateTime);

        // 自定义标签(可用于检索)。 eg. user
        MDC.put("tag", tag);

        log.info("audit log：" + MDC.getCopyOfContextMap());
    }


    public HttpServletRequest getServletRequest() {
        return servletRequest;
    }

    @Autowired
    public void setServletRequest(HttpServletRequest servletRequest) {
        AuditLogger.servletRequest = servletRequest;
    }
}
