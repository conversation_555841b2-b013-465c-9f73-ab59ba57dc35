package net.jdair.specialrefund.common.log;


import lombok.Data;

/**
 * 审计日志打印 VO
 */
@Data
public class AuditLogVO {

    /**
     * 操作动作。 eg. MODIFY_PASSWORD
     */
    private OperationType operation;

    /**
     * 业务模块。 eg. ORDER
     */
    private BizModuleType bizModule;

    /**
     * 客户端请求IP。 eg. ************
     */
    private String ip;

    /**
     * 操作人。 eg. 张三
     */
    private String operator;

    /**
     * 操作时间。eg. 2022-02-15 20:00:05
     */
    private String operateTime;

    /**
     * 自定义标签(可用于检索)。 eg. user
     */
    private String tag;

    /**
     * 自定义信息(建议存放json便于解析)。 eg. {\"name\": \"bob\", \"password\": \"123456\"}
     */
    private String content;
}
