
package net.jdair.specialrefund.common.exception;

import net.jdair.specialrefund.bizz.vo.SpecialRefundException;
import net.jdair.specialrefund.common.response.CodeDefault;
import net.jdair.specialrefund.common.response.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理
 *
 */
@RestControllerAdvice
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 系统自定义全局异常
     *
     * @param req
     * @param e
     *
     * @return
     *
     * @throws Exception
     */
    @ExceptionHandler(value = GlobalException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, GlobalException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(e.getCodeEnum());
        log.error("GlobalException: {}", exception, e);

        return exception;
    }

    /**
     * controller 参数转化时, 主要从这里捕获错误信息
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, HttpMessageNotReadableException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
        log.error("HttpMessageNotReadableException: {}", exception, e);

        return exception;
    }

    /**
     *
     *
     * @param req
     * @param e
     *
     * @return
     *
     * @throws Exception
     */
    @ExceptionHandler(value = SpecialRefundException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, SpecialRefundException e) throws Exception {
        RestResponse exception = RestResponse.exception(-1000l, e.getMessage(), null);
        log.error("RuntimeException: {}", exception, e);

        return exception;
    }

    /**
     * 这个兜底
     *
     * @param req
     * @param e
     *
     * @return
     *
     * @throws Exception
     */
    @ExceptionHandler(value = RuntimeException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, RuntimeException e) throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.INTERNAL_SERVER_ERROR);
        log.error("RuntimeException: {}", exception, e);

        return exception;
    }
}
