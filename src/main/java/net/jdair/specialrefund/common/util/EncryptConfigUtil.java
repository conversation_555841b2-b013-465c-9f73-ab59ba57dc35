package net.jdair.specialrefund.common.util;

import org.apache.commons.io.IOUtils;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;

/**
 * 生成秘钥工具
 *
 * @Date 2020/11/24 10:40
 * @Version 1.0
 **/
public class EncryptConfigUtil {
    /**
     * Jasypt生成加密结果
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    加密值
     * @return
     */
    public static String encyptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(cryptor(password));
        String result = encryptor.encrypt(value);
        return result;
    }

    /**
     * 解密
     *
     * @param password 配置文件中设定的加密盐值
     * @param value    解密密文
     * @return
     */
    public static String decyptPwd(String password, String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        encryptor.setConfig(cryptor(password));
        String result = encryptor.decrypt(value);
        return result;
    }

    public static SimpleStringPBEConfig cryptor(String password) {
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        return config;
    }


    public static void main(String[] args) {

        // 加密
        String encPwd = encyptPwd("jasypt", "demo");
        // 解密
        String decPwd = decyptPwd("jasypt", encPwd);


        // 加密
        String encPwd1 = encyptPwd("jasypt", "Demo@123");
        // 解密
        String decPwd1 = decyptPwd("jasypt", "N8avqx0vdTzJD6fiJ9CtlBp3qisv5pIx");
        System.out.println(encPwd);
        System.out.println(decPwd);

        System.out.println(encPwd1);
        System.out.println(decPwd1);

        String uploadUrl = "https://ai.eking-tech.com:8081/cci_ai/service/v1/id_card";
        String imagePath = "C:\\Users\\<USER>\\Downloads\\e3c2d916-c8ff-490e-9307-da27dea1edcd.jpg";
        String fileType = "image";

        try {
            uploadFile(uploadUrl, imagePath, fileType);
            System.out.println("上传成功！");
        } catch (Exception e) {
            System.err.println("上传失败: " + e.getMessage());
        }
    }



        public static void uploadFile(String uploadUrl, String filePath, String fileType) throws IOException {
            File file = new File(filePath);
            OutputStream out = null;
            BufferedReader in = null;
            StringBuilder result = new StringBuilder();

            FileInputStream is = new FileInputStream(file);
            try {
                URL realUrl = new URL(uploadUrl);
                URLConnection conn = realUrl.openConnection();
                conn.setRequestProperty("accept", "*/*");
                conn.setRequestProperty("connection", "Keep-Alive");
                conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
                conn.setRequestProperty("Accept-Charset", "utf-8");
                conn.setRequestProperty("content-type", "application/x-www-form-urlencoded");
                conn.setDoOutput(true);
                conn.setDoInput(true);
                out = conn.getOutputStream();
                IOUtils.copy(is, out);
                out.flush();
                in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
                System.out.println(result);
            } catch (ConnectException e) {
            } catch (SocketTimeoutException e) {
            } catch (IOException e) {
            } catch (Exception e) {
            } finally {
                try {
                    if (out != null) {
                        out.close();
                    }
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException ex) {
                }
            }
        }

}
