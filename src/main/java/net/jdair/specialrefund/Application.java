package net.jdair.specialrefund;

import com.alibaba.dubbo.config.spring.context.annotation.DubboComponentScan;
import com.hnair.opcnet.rpc.annotation.EnableOdsApi;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableEurekaClient              // 启动 eureka 服务注册
@DubboComponentScan(basePackages= {"com.hnair.opcnet.api","net.jdair.specialrefund.bizz"})
@MapperScan("net.jdair.specialrefund.*.mapper")
@EnableFeignClients
@EnableAsync
@ComponentScan(basePackages = {"com.hna.eking.util.oss", "net.jdair.specialrefund"})
@EnableTransactionManagement
@EnableOdsApi
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}