package net.jdair.specialrefund.bizz.controller.backend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.controller.backend.domain.FirstAuditReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.RefundAmountReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SecondAuditReq;
import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import net.jdair.specialrefund.bizz.controller.backend.page.TableDataInfo;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg;
import net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper;
import net.jdair.specialrefund.bizz.mapper.EtTsMultiRefundTagMapper;
import net.jdair.specialrefund.bizz.service.EtSpecialRefundImgService;
import net.jdair.specialrefund.bizz.utils.PageUtil;
import net.jdair.specialrefund.bizz.utils.ValidateUtils;
import net.jdair.specialrefund.bizz.utils.file.service.FileService;
import net.jdair.specialrefund.bizz.vo.*;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 特殊退票--后台管理
 */
@RestController
@Api(value = "后台管理--特殊退票")
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundBackendController extends BaseController {

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    @Autowired
    private EtSpecialRefundImgService etSpecialRefundImgService;

    @Autowired
    private FileService fileService;

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtTsMultiRefundTagMapper etTsMultiRefundTagMapper;


    /**
     * 条件查询退票单列表
     */
    @PostMapping("/list")
    @ResponseBody
    public RestResponse<TableDataInfo> list(@RequestBody SearchRefundOrderListReq req) {
        Page page = new Page();
        page.setCurrent(req.getPageNum());
        page.setSize(req.getPageSize());
        IPage<EtFltRefund> list = flightRefundServiceHelper.findRefundByParamsForPage(page, req);

        TableDataInfo result = new TableDataInfo();
        result.setCode(0);
        result.setRows(list.getRecords());
        result.setTotal(list.getTotal());
        return RestResponse.ok(result);
    }

    /**
     * 根据ID查询退票单明细
     */
    @GetMapping("/get")
    @ResponseBody
    public RestResponse<EtFltRefund> get(@RequestParam(name = "id") Long id) {
        EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(id);
        return RestResponse.ok(etFltRefund);
    }

    /**
     * 按退票单号查询附件图片
     */
    @GetMapping("/findImgByRefundNo")
    @ResponseBody
    public RestResponse<List<EtSpecialRefundImg>> findImgByRefundNo(@RequestParam(name = "refundNo") String refundNo) {
        String bucketName = "special-refund";
        List<EtSpecialRefundImg> etSpecialRefundImgList = etSpecialRefundImgService.getBaseMapper().selectList(new QueryWrapper<EtSpecialRefundImg>().eq("refund_no", refundNo));
        int i = 1;
        try {
            for (EtSpecialRefundImg etSpecialRefundImg : etSpecialRefundImgList) {
                if (!StringUtils.hasText(etSpecialRefundImg.getImgOriginName())) {
                    String[] tmp = etSpecialRefundImg.getImgUrl().split("-");
                    String name = tmp[tmp.length - 1];
                    etSpecialRefundImg.setImgOriginName(name);
                }
                etSpecialRefundImg.setImgUrl(fileService.getPreviewUrl(etSpecialRefundImg.getImgUrl(), bucketName));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RestResponse.ok(etSpecialRefundImgList);
    }

    /**
     * 一审通过
     */
    @PostMapping("/firstAuditPass")
    @ResponseBody
    public RestResponse<String> firstAuditPass(
            @RequestParam(value = "refundId", required = true) Long refundId,
            @RequestParam(value = "auditRemark", required = false) String auditRemark,
            @RequestParam(value = "constraintReason", required = false) String constraintReason,
            @RequestParam(value = "refundType", required = false) String refundType,
            @RequestParam(value = "amountIndex", required = false) List<Long> amountIndex,
            @RequestParam(value = "actualAmountList", required = false) List<Double> actualAmountList,
            @RequestPart(value = "resources", required = false) MultipartFile[] resources,
            @RequestParam(value = "auditUserName", required = true) String auditUserName
    ) {
        FirstAuditReq req = new FirstAuditReq();
        req.setRefundId(refundId);
        req.setAuditRemark(auditRemark);
        req.setConstraintReason(constraintReason);
        req.setRefundType(refundType);
        req.setAmountIndex(amountIndex);
        req.setActualAmountList(actualAmountList);
        req.setResources(resources);
        req.setAuditUserName(auditUserName);
        flightRefundServiceHelper.firstAuditPass(req);
        return RestResponse.ok("success");
    }

    /**
     * 一审拒绝
     */
    @PostMapping("/firstAuditReject")
    @ResponseBody
    public RestResponse<String> firstAuditReject(
            @RequestParam(value = "refundId", required = true) Long refundId,
            @RequestParam(value = "auditRemark", required = false) String auditRemark,
            @RequestParam(value = "constraintReason", required = false) String constraintReason,
            @RequestParam(value = "refundType", required = false) String refundType,
            @RequestParam(value = "amountIndex", required = false) List<Long> amountIndex,
            @RequestParam(value = "actualAmountList", required = false) List<Double> actualAmountList,
            @RequestPart(value = "resources", required = false) MultipartFile[] resources,
            @RequestParam(value = "auditUserName", required = true) String auditUserName
    ) {
        FirstAuditReq req = new FirstAuditReq();
        req.setRefundId(refundId);
        req.setAuditRemark(auditRemark);
        req.setConstraintReason(constraintReason);
        req.setRefundType(refundType);
        req.setAmountIndex(amountIndex);
        req.setActualAmountList(actualAmountList);
        req.setResources(resources);
        req.setAuditUserName(auditUserName);
        flightRefundServiceHelper.firstAuditReject(req);
        return RestResponse.ok("success");
    }

    /**
     * 二审通过
     */
    @PostMapping("/secondAuditPass")
    @ResponseBody
    public RestResponse<String> secondAuditPass(
            @RequestParam(value = "refundId", required = true) Long refundId,
            @RequestParam(value = "auditRemark", required = false) String auditRemark,
            @RequestParam(value = "amountIndex", required = false) List<Long> amountIndex,
            @RequestParam(value = "actualAmountList", required = false) List<Double> actualAmountList,
            @RequestPart(value = "resources", required = false) MultipartFile[] resources,
            @RequestParam(value = "auditUserName", required = true) String auditUserName
    ) {
        SecondAuditReq req = new SecondAuditReq();
        req.setRefundId(refundId);
        req.setAuditRemark(auditRemark);
        req.setAmountIndex(amountIndex);
        req.setActualAmountList(actualAmountList);
        req.setResources(resources);
        req.setAuditUserName(auditUserName);
        flightRefundServiceHelper.secondAuditPass(req);
        return RestResponse.ok("success");
    }

    /**
     * 二审拒绝
     */
    @PostMapping("/secondAuditReject")
    @ResponseBody
    public RestResponse<String> secondAuditReject(
            @RequestParam(value = "refundId", required = true) Long refundId,
            @RequestParam(value = "auditRemark", required = false) String auditRemark,
            @RequestParam(value = "amountIndex", required = false) List<Long> amountIndex,
            @RequestParam(value = "actualAmountList", required = false) List<Double> actualAmountList,
            @RequestPart(value = "resources", required = false) MultipartFile[] resources,
            @RequestParam(value = "auditUserName", required = true) String auditUserName
    ) {
        SecondAuditReq req = new SecondAuditReq();
        req.setRefundId(refundId);
        req.setAuditRemark(auditRemark);
        req.setAmountIndex(amountIndex);
        req.setActualAmountList(actualAmountList);
        req.setResources(resources);
        req.setAuditUserName(auditUserName);
        flightRefundServiceHelper.secondAuditReject(req);
        return RestResponse.ok("success");
    }

    /**
     * 线下退款
     */
    @PostMapping("/refundAmountOffline")
    @ResponseBody
    public RestResponse<String> refundAmountOffline(@RequestBody RefundAmountReq req) {
        flightRefundSpecialServiceHelper.refundAmountOffline(req);
        return RestResponse.ok("success");
    }

    /**
     * 在线退款
     */
    @PostMapping("/refundAmountOnline")
    @ResponseBody
    public RestResponse<String> refundAmountOnline(@RequestBody RefundAmountReq req) {
        EtTsMultiRefundTag etTsMultiRefundTag = etTsMultiRefundTagMapper.selectById(1);
        if (!"ACTIVE".equals(etTsMultiRefundTag.getOnlineRefundStatus())) {
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getCode());
        }
        flightRefundServiceHelper.refundAmountOnline(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票,【审核中】、【一审拒绝】、【二审拒绝】状态的退单，增加 “关闭退票单” 的操作按钮
     */
    @PostMapping("/closeOrderForSpecialRefund")
    @ResponseBody
    public RestResponse<String> closeOrderForSpecialRefund(@RequestBody CloseSpecialOrderReq req) {
        flightRefundSpecialServiceHelper.closeOrderForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票,【已关闭】状态的退单，增加 “重新开启退票单” 的操作按钮
     */
    @PostMapping("/reOpenOrderForSpecialRefund")
    @ResponseBody
    public RestResponse<String> reOpenOrderForSpecialRefund(@RequestBody ReopenSpecialOrderReq req) {
        flightRefundSpecialServiceHelper.reOpenOrderForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票,【退款接口处理中】、【退款处理中】、【退款成功】状态的退单，增加 “修改退票单为二审通过” 的操作按钮
     */
    @PostMapping("/changeToPassForSpecialRefund")
    @ResponseBody
    public RestResponse<String> changeToPassForSpecialRefund(@RequestBody ChangeToPassReq req) {
        flightRefundSpecialServiceHelper.changeToPassForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 二审通过撤回为一审通过
     */
    @PostMapping("/changeToFPassForSpecialRefund")
    @ResponseBody
    public RestResponse<String> changeToFPassForSpecialRefund(@RequestBody ChangeToFPassReq req) {
        flightRefundSpecialServiceHelper.changeToFPassForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 驳回
     */
    @PostMapping("/refundFailRejectForSpecialRefund")
    @ResponseBody
    public RestResponse<String> refundFailRejectForSpecialRefund(@RequestBody RefundFailRejectReq req) {
        flightRefundSpecialServiceHelper.refundFailRejectForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 转线下银行付款
     */
    @PostMapping("/makeOfflineForSpecialRefund")
    @ResponseBody
    public RestResponse<String> makeOfflineForSpecialRefund(@RequestBody MakeOfflineReq req) {
        flightRefundSpecialServiceHelper.makeOfflineForSpecialRefund(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 修改银行卡信息
     */
    @PostMapping("/changeBankInfo")
    @ResponseBody
    public RestResponse<String> changeBankInfo(@RequestBody ChangeBankInfoReq req) {
        //查询参数合法性验证
        validateParamsForChange(req);
        flightRefundSpecialServiceHelper.changeBankInfo(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 批量线上退款
     */
    //@PostMapping("/batchRefundOnlineTS")
    //@ResponseBody
    public RestResponse<String> batchRefundOnlineTS(@RequestBody BatchRefundOnlineTSReq req) {
        flightRefundSpecialServiceHelper.batchRefundOnlineTS(req);
        return RestResponse.ok("success");
    }

    /**
     * 特殊退票, 修改退单类型
     */
    @PostMapping("/changeRefundTypeForSpecialRefund")
    @ResponseBody
    public RestResponse<String> changeRefundTypeForSpecialRefund(@RequestBody ChangeRefundTypeReq req) {
        flightRefundSpecialServiceHelper.changeRefundTypeForSpecialRefund(req);
        return RestResponse.ok("success");
    }


    /**
     * 特殊退票订单在线退款，线上退款结果查询
     */
    @PostMapping("/queryOnlineRefundStatus")
    @ResponseBody
    public RestResponse<String> queryOnlineRefundStatus(@RequestBody QueryOnlineRefundStatusReq req) {
        String result = flightRefundSpecialServiceHelper.queryOnlineRefundStatus(req);
        return RestResponse.ok(result);
    }


    /**
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private void validateParamsForChange(ChangeBankInfoReq req) {

        // 验证 newCardNo
        if(!StringUtils.hasText(req.getCardNo())) {
            throw new SpecialRefundException(StatusCode.SPECIAL_REFUND_CARDNO_NULL.getMsg());
        }

        // 验证 newCardHolder
        if(!StringUtils.hasText(req.getCardHolder())) {
            throw new SpecialRefundException(StatusCode.SPECIAL_REFUND_CARDHOLDER_NULL.getMsg());
        }

        // 验证 bankName
        if(!StringUtils.hasText(req.getBankName())) {
            throw new SpecialRefundException(StatusCode.SPECIAL_REFUND_BANKNAME_NULL.getMsg());
        }

        // 验证 mobileNo
        if(!StringUtils.hasText(req.getMobileNo())) {
            throw new SpecialRefundException(StatusCode.SPECIAL_REFUND_MOBILENO_NULL.getMsg());

        } else if (!ValidateUtils.isValidByRegex(req.getMobileNo(), ValidateUtils.MOBILE_NO_REGEX)) {
            throw new SpecialRefundException(StatusCode.SPECIAL_REFUND_MOBILENO_ERROR.getMsg());
        }

    }


}
