package net.jdair.specialrefund.bizz.controller.backend;

import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import net.jdair.specialrefund.bizz.controller.backend.page.TableDataInfo;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.utils.PageUtil;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundReport;
import net.jdair.specialrefund.bizz.vo.SearchRefundReportQueryReq;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 特殊退票--后台管理  报表
 */
@RestController
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundReportController extends BaseController {

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    /**
     * 条件查询
     */
    @PostMapping("/queryReport")
    @ResponseBody
    public RestResponse<TableDataInfo> queryReport(@RequestBody SearchRefundReportQueryReq req) {
        List<DTOSpecialRefundReport> list = flightRefundSpecialServiceHelper.queryReport(req);
        PageUtil<DTOSpecialRefundReport> util = new PageUtil<>();
        PageParam<DTOSpecialRefundReport> page = util.getPage(req.getPageNum(), req.getPageSize(), list);
        TableDataInfo result = new TableDataInfo();
        result.setCode(0);
        result.setRows(page.getData());
        result.setTotal(page.getTotal());
        return RestResponse.ok(result);
    }

}
