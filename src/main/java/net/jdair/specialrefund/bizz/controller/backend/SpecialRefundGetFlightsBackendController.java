package net.jdair.specialrefund.bizz.controller.backend;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.utils.*;
import net.jdair.specialrefund.bizz.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *  特殊退票--后台管理管理员代客提交
 */
@RestController
@Api(value = "后台管理员--航班信息查询")
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundGetFlightsBackendController extends BaseController {
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private BaseInfoUtils baseInfoUtils;

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    /**
     * @Fields jsonResult : API接口封装类
     */
    protected JSONResult jsonResult = new JSONResult();

    public static final int CACHE_DATE = CacheConstant.MEMC_TIME_MINUTES_30;
    private static final String SPECIAL_REFUND_KEY_PREFIX = CacheConstant.SPECIAL_REFUND_KEY_PREFIX;

    /**
     * 1. 根据【退票类型】、【证件号\票号】、【姓名】 提去可退客票航班信息接口
     * @param req
     * @return
     */
    @RequestMapping(value = "getAvailableRefundFlightInfo", method = RequestMethod.POST)
    public JSONResult getAvailableRefundFlightInfo(@RequestBody GetAvailableRefundFlightInfoBackendReq req) {
        jsonResult = new JSONResult();
        try {
            //查询参数合法性验证
            if (!validateParams(req)) {
                return jsonResult;
            }

            List<DTOOrder> dtoOrderList;
            if(StringUtils.hasText(req.getTicketNo())) {
                // 优先使用票号提取
                dtoOrderList = flightRefundSpecialServiceHelper.generateDTOOrderByTicketNo(req.getTicketNo(), req.getPassengerName(), req.getFlightDate());
                log.debug("getAvailableRefundFlightInfo by ticketNo: " + JSON.toJSONString(dtoOrderList));
            } else {
                // 使用身份证号提取
                dtoOrderList = flightRefundSpecialServiceHelper.generateDTOOrderByIdCardNo(req.getIdCardNo(), req.getPassengerName(), req.getFlightDate());
                log.debug("getAvailableRefundFlightInfo by idCardNo: " + JSON.toJSONString(dtoOrderList));
            }

            Map<String, Object> data = new HashMap<String, Object>();

            String newTicketNo = getNewTktNoByExchangedTktNo(req.getTicketNo(), req.getPassengerName(), req.getRefundType());

            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                // 按退票类型规则过滤
                filterByRefundType(req.getRefundType(), dtoOrderList, req);
                if (dtoOrderList.size() != 0) {
                    String uuid = UUID.randomUUID().toString();
                    ValueOperations<String, String> operations = redisTemplate.opsForValue();
                    operations.set(SPECIAL_REFUND_KEY_PREFIX + uuid, JSON.toJSONString(dtoOrderList), CACHE_DATE, TimeUnit.SECONDS);
                    data.put("uuid", uuid);
                    data.put("flights", generateDTOFlightInfo(dtoOrderList));
                } else {
                    if (StringUtils.hasText(newTicketNo)) {
                        jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                        return jsonResult;
                    }
                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR);
                    return jsonResult;
                }
            } else {
                if (StringUtils.hasText(newTicketNo)) {
                    jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                    return jsonResult;
                }
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR);
                return jsonResult;
            }

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException e) {
            jsonResult = getErrorJsonResult("", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 团队票批量查询。 根据【退票类型】、【票号】、【姓名】 提去可退客票航班信息接口
     * @return
     */
    @RequestMapping(value = "getAvailableRefundFlightInfoGT", method = RequestMethod.POST)
    public JSONResult getAvailableRefundFlightInfoGT(@RequestBody GetAvailableRefundFlightInfoBackendReq req) {
        jsonResult = new JSONResult();
        try {
            //查询参数合法性验证
            if (!validateParamsGT(req)) {
                return jsonResult;
            }

            List<DTOOrder> dtoOrderList = new ArrayList<DTOOrder>();

            req.setPassengerNameAndTicketNo(req.getPassengerNameAndTicketNo().replaceAll("，", ",").replaceAll(" ", ""));
            String[] list = req.getPassengerNameAndTicketNo().split("\\n");

            for (String s : list) {
                String passengerName = s.split(",")[0];
                String ticketNo = s.split(",")[1];
                req.setTicketNo(ticketNo);
                req.setPassengerName(passengerName);
                // 使用票号提取
                List<DTOOrder> orderList = flightRefundSpecialServiceHelper.generateDTOOrderByTicketNo(req.getTicketNo(), req.getPassengerName());

                log.debug("getAvailableRefundFlightInfoGT by ticketNo: " + JSON.toJSONString(orderList));

                String newTicketNo = getNewTktNoByExchangedTktNo(req.getTicketNo(), req.getPassengerName(), req.getRefundType());

                if (orderList != null && orderList.size() > 0) {
                    // 按退票类型规则过滤
                    filterByRefundType(req.getRefundType(), orderList, req);
                    if (orderList.size() == 0) {
                        if (StringUtils.hasText(newTicketNo)) {
                            jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                            return jsonResult;
                        }
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR.getCode(), StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR.getMsg() + "【" + ticketNo + "】");
                        return jsonResult;
                    }
                } else {
                    if (StringUtils.hasText(newTicketNo)) {
                        jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                        return jsonResult;
                    }
                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR.getCode(), StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR.getMsg() + "【" + ticketNo + "】");
                    return jsonResult;
                }

                dtoOrderList.addAll(orderList);
            }

            Map<String, Object> data = new HashMap<String, Object>();

            String uuid = UUID.randomUUID().toString();
            ValueOperations<String, String> operations = redisTemplate.opsForValue();
            operations.set(SPECIAL_REFUND_KEY_PREFIX + uuid, JSON.toJSONString(dtoOrderList), CACHE_DATE, TimeUnit.SECONDS);
            data.put("uuid", uuid);
            data.put("flights", generateDTOFlightInfo(dtoOrderList));

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException e) {
            jsonResult = getErrorJsonResult("", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 国际票代客提交
     * 1. 根据【退票类型】、【证件号\票号】、【姓名】 提去可退客票航班信息接口
     * @return
     */
    @RequestMapping(value = "getAvailableRefundFlightInfoGJ", method = RequestMethod.POST)
    public JSONResult getAvailableRefundFlightInfoGJ(@RequestBody GetAvailableRefundFlightInfoBackendReq req) {
        jsonResult = new JSONResult();
        try {
            //查询参数合法性验证
            if (!validateParamsGJ(req)) {
                return jsonResult;
            }

            List<DTOOrder> dtoOrderList;
            if(StringUtils.hasText(req.getTicketNo())) {
                // 优先使用票号提取
                dtoOrderList = flightRefundSpecialServiceHelper.generateDTOOrderByTicketNo(req.getTicketNo(), req.getPassengerName(), req.getFlightDate());
                log.info("getAvailableRefundFlightInfoGJ by ticketNo: " + JSON.toJSONString(dtoOrderList));
            } else {
                // 使用护照号提取
                dtoOrderList = flightRefundSpecialServiceHelper.generateDTOOrderByIdCardNo(req.getIdCardNo(), req.getPassengerName(), req.getFlightDate(), "PP");
                log.info("getAvailableRefundFlightInfoGJ by idCardNo: " + JSON.toJSONString(dtoOrderList));
            }

            Map<String, Object> data = new HashMap<String, Object>();

            String newTicketNo = getNewTktNoByExchangedTktNo(req.getTicketNo(), req.getPassengerName(), req.getRefundType());

            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                // 按退票类型规则过滤
                filterByRefundType(req.getRefundType(), dtoOrderList, req);
                if (dtoOrderList.size() != 0) {
                    String uuid = UUID.randomUUID().toString();
                    ValueOperations<String, String> operations = redisTemplate.opsForValue();
                    operations.set(SPECIAL_REFUND_KEY_PREFIX + uuid, JSON.toJSONString(dtoOrderList), CACHE_DATE, TimeUnit.SECONDS);
                    data.put("uuid", uuid);
                    data.put("flights", generateDTOFlightInfo(dtoOrderList));
                } else {
                    if (StringUtils.hasText(newTicketNo)) {
                        jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                        return jsonResult;
                    }
                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR);
                    return jsonResult;
                }
            } else {
                if (StringUtils.hasText(newTicketNo)) {
                    jsonResult = getErrorJsonResult("7035", "您的客票已变更为" + "898-" + newTicketNo.substring(3) + "，请用此票号提交改期后退票申请。");
                    return jsonResult;
                }
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NO_FLIGHT_ERROR);
                return jsonResult;
            }

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException e) {
            jsonResult = getErrorJsonResult("", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }



    /**
     * @Title: validateParams
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParams(GetAvailableRefundFlightInfoBackendReq req) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("passengerName", req.getPassengerName());
        params.put("ticketNo", req.getTicketNo());
        params.put("idCardNo", req.getIdCardNo());
        params.put("refundType", req.getRefundType());
        params.put("flightDate", req.getFlightDate());


        //验证 旅客姓名
        if(!StringUtils.hasText(req.getPassengerName())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NAME_NULL);
            return false;
        }

        // 验证 票号/身份证号
        if (!StringUtils.hasText(req.getTicketNo()) && !StringUtils.hasText(req.getIdCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CERTNO_TICKETNO_NULL);
            return false;
        } else if (StringUtils.hasText(req.getTicketNo()) && !ValidateUtils.isValidByRegex(req.getTicketNo(), ValidateUtils.JD_TICKET_NO_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CERTNO_TICKETNO_ERROR);
            return false;
        } else if (StringUtils.hasText(req.getIdCardNo()) && !IdentityUtil.checkIDCard(req.getIdCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CERTNO_TICKETNO_ERROR);
            return false;
        }

        // 验证 退票类型
        if(!StringUtils.hasText(req.getRefundType())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_NULL);
            return false;
        }else if(!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_TYPE_REGEX_AGENT)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
            return false;
        }

        //验证 航班日期
        if(!StringUtils.hasText(req.getFlightDate())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_FLIGHTDATE_NULL);
            return false;
        }else if(!ValidateUtils.isValidDate(req.getFlightDate())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_FLIGHTDATE_ERROR);
            return false;
        }

        return true;
    }

    /**
     * @Title: validateParams
     * @Description: 团队票退票验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsGT(GetAvailableRefundFlightInfoBackendReq req) {

        req.setPassengerNameAndTicketNo(req.getPassengerNameAndTicketNo().replaceAll("，", ",").replaceAll(" ", ""));

        String[] list = req.getPassengerNameAndTicketNo().split("\\n");

        if (list.length > 9) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_SIZE_ERROR);
            return false;
        }

        Set<String> passengerNameSet = new HashSet<String>();
        Set<String> ticketNoSet = new HashSet<String>();
        for (String s : list) {
            String passengerName = s.split(",")[0];
            String ticketNo = s.split(",").length > 1 ? s.split(",")[1] : "";

//            if (!passengerNameSet.add(passengerName)) {
//                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_SAME_NAME_ERROR.getCode(), StatusCode.SPECIAL_REFUND_GT_SAME_NAME_ERROR.getMsg() + "【" + passengerName + "】");
//                return false;
//            }
            if (!ticketNoSet.add(ticketNo)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_SAME_TICKET_ERROR.getCode(), StatusCode.SPECIAL_REFUND_GT_SAME_TICKET_ERROR.getMsg() + "【" + ticketNo + "】");
                return false;
            }

            //验证 旅客姓名
            if(!StringUtils.hasText(passengerName)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NAME_NULL);
                return false;
            }

            // 验证 票号
            if (!StringUtils.hasText(ticketNo)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETNO_NULL);
                return false;
            } else if (!ValidateUtils.isValidByRegex(ticketNo, ValidateUtils.JD_TICKET_NO_REGEX)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETNO_ERROR);
                return false;
            }
        }


        // 验证 退票类型
        if(!StringUtils.hasText(req.getRefundType())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_NULL);
            return false;
        }else if(!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_TYPE_REGEX_AGENT)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
            return false;
        }

        return true;
    }

    /**
     * @Title: validateParamsGJ
     * @Description: 验证国际票请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsGJ(GetAvailableRefundFlightInfoBackendReq req) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("passengerName", req.getPassengerName());
        params.put("ticketNo", req.getTicketNo());
        params.put("idCardNo", req.getIdCardNo());
        params.put("refundType", req.getRefundType());
        params.put("flightDate", req.getFlightDate());


        //验证 旅客姓名
        if(!StringUtils.hasText(req.getPassengerName())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NAME_NULL);
            return false;
        }

        // 验证 票号/身份证号
        if (!StringUtils.hasText(req.getTicketNo()) && !StringUtils.hasText(req.getIdCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CERTNO_TICKETNO_NULL);
            return false;
        } else if (StringUtils.hasText(req.getTicketNo()) && !ValidateUtils.isValidByRegex(req.getTicketNo(), ValidateUtils.JD_TICKET_NO_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CERTNO_TICKETNO_ERROR);
            return false;
        }

        // 验证 退票类型
        if(!StringUtils.hasText(req.getRefundType())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_NULL);
            return false;
        }else if(!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_BACKEND_GJ_TYPE_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
            return false;
        }

        //验证 航班日期
        if(!StringUtils.hasText(req.getFlightDate())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_FLIGHTDATE_NULL);
            return false;
        }else if(!ValidateUtils.isValidDate(req.getFlightDate())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_FLIGHTDATE_ERROR);
            return false;
        }

        return true;
    }

    /**
     * 退票类型过滤
     * @return
     */
    private void filterByRefundType(String refundType, List<DTOOrder> dtoOrderList, GetAvailableRefundFlightInfoBackendReq req) {
        Iterator<DTOOrder> iteratorOrder = dtoOrderList.iterator();
        while (iteratorOrder.hasNext()) {
            DTOOrder dtoOrder = iteratorOrder.next();
            Iterator<DTOPassenger> iteratorPassenger = dtoOrder.getPassengerList().iterator();
            while (iteratorPassenger.hasNext()) {
                DTOPassenger dtoPassenger = iteratorPassenger.next();
                Iterator<DTOPaxSegment> iteratorPaxSegment = dtoPassenger.getPassengerSegmentList().iterator();
                while (iteratorPaxSegment.hasNext()) {
                    DTOPaxSegment dtoPaxSegment = iteratorPaxSegment.next();

                    boolean valid = false;

                    if ("FLT_CHANGED".equals(refundType)) {
                        // 航班改期类退票。提取该证件号及航班日期下票为OPEN FOR USE状态的，且为航司本票。同时旧票为EXCHANGE状态的所有航段信息（新票面的OI项中会备注旧票的票号，以此进行判断客票是否进行过换开操作）
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.OPEN_FOR_USE
                                && dtoPaxSegment.getTicketIssued().getBspType() == 1
                                && dtoPaxSegment.getDtoSpecialRefundOriginTkt() != null) {
                            if ("XIY201".equals(dtoPaxSegment.getTicketIssued().getIssuedBy()) || "XIY315".equals(dtoPaxSegment.getTicketIssued().getIssuedBy())) {
                                valid = true;
                            } else {
                                // 同时满足原票和新票的officeNo不相同
                                if (!dtoPaxSegment.getTicketIssued().getIssuedBy().equals(dtoPaxSegment.getDtoSpecialRefundOriginTkt().getOfficeNo())) {
                                    valid = true;
                                }
                            }
                        }
                    } else if ("FEE_CHANGED".equals(refundType)) {
                        // 退补差价、手续费。 提取该证件下客票为 REFUNDED 状态的所有航段信息。
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.REFUNDED) {
                            valid = true;
                        }
                    } else if ("AIRLINE_REASON".equals(refundType)) {
                        // 降舱、备降/经停取消退费：客票状态USED
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.USED_OR_FLOWN) {
                            valid = true;
                        }
                    } else if ("EXPIRED".equals(refundType)) {
                        // 过期客票退税。 提取该证件下客票状态为OPEN FOR USE状态的且客票有效期超过一年的票航段信息（仅适用国内票）
                        // a、单程客票：计算标准为出票之日次日零时开始，一年有效期；
                        // b、往返、多程客票：如果全部未使用，按照出票之日次日零点开始，一年有效；如果第一段已使用，按照旅行开始之日计算，按照第一段航班日期次日零时开始，一年有效。
                        // c、如客票发生变更：有效期按照原客票。（涉及单程、往返程的不同计算方法）TODO 暂不处理
                        //  1）单程的，发生变更，新变更的客票有期效均为原客票出票之日次零时开始，一年有效。
                        //  2）往返的，全部未使用，去程变更 或回程变更，或同时变更，新变更的客票有期效均为原客票出票之日次零时开始，一年有效。
                        //  3）往返的，去程已使用，回程变更，新变更的客票有期效为原客票第一段航班日期次日零时开始，一年有效。
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.OPEN_FOR_USE && dtoOrder.getInternational() == 0) {
                            if (allUnUsed(dtoPassenger.getPassengerSegmentList()) && DateUtils.getDistanceBetweenDays(dtoPaxSegment.getTicketIssued().getIssuedDate(), new Date()) > 336) {
                                valid = true;
                            } else if (!allUnUsed(dtoPassenger.getPassengerSegmentList())) {
                                Date flightDate = null;
                                for (DTOPaxSegment paxSegment : dtoPassenger.getPassengerSegmentList()) {
                                    if (paxSegment.getTicketStatus() == TicketStatus.USED_OR_FLOWN) {
                                        flightDate = paxSegment.getDepTime();
                                        break;
                                    }
                                }
                                if (flightDate != null && DateUtils.getDistanceBetweenDays(flightDate, new Date()) > 336) {
                                    valid = true;
                                }
                            }
                        }
                    } else if ("AGENT_FAULT".equals(refundType)) {
                        // 代理人错误导致删票：客票状态须为 REFUNDED
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.REFUNDED) {
                            valid = true;
                        }
                    } else if ("OTHER_REASON".equals(refundType) && dtoOrder.getInternational() == 1) {  // 国际票代客提交新增【其他特殊退票】类型
                        if (dtoPaxSegment.getTicketStatus() == TicketStatus.OPEN_FOR_USE) {
                            valid = true;
                        }
                    }

                    if (!valid) {
                        iteratorPaxSegment.remove();
                    }
                }
            }


            // 非票号提的，过滤掉航班日期不匹配的航段
            if (!StringUtils.hasText(req.getTicketNo())) {
                Iterator<DTOPassenger> iteratorPassengerTmp = dtoOrder.getPassengerList().iterator();
                while (iteratorPassengerTmp.hasNext()) {
                    DTOPassenger dtoPassenger = iteratorPassengerTmp.next();
                    Iterator<DTOPaxSegment> iteratorPaxSegment = dtoPassenger.getPassengerSegmentList().iterator();
                    boolean exist = false;
                    while (iteratorPaxSegment.hasNext()) {
                        DTOPaxSegment dtoPaxSegment = iteratorPaxSegment.next();
                        if (DateUtils.dateToString(dtoPaxSegment.getDepTime()).equals(req.getFlightDate())) {
                            exist = true;
                            break;
                        }
                    }
                    if (!exist) {
                        dtoPassenger.setPassengerSegmentList(new ArrayList<DTOPaxSegment>());
                    }
                }
            }

            // 移除没有 paxSegment 的 order
            if (dtoOrder.getPassengerList().get(0).getPassengerSegmentList() == null
                    || dtoOrder.getPassengerList().get(0).getPassengerSegmentList().size() == 0) {
                iteratorOrder.remove();
            }
        }
    }

    /**
     * 是否所有航段都是未使用
     * @param dtoPaxSegmentList
     * @return
     */
    private boolean allUnUsed(List<DTOPaxSegment> dtoPaxSegmentList) {
        boolean allUnUsed = true;
        for (DTOPaxSegment dtoPaxSegment : dtoPaxSegmentList) {
            if (dtoPaxSegment.getTicketStatus() == TicketStatus.USED_OR_FLOWN) {
                allUnUsed = false;
                break;
            }
        }
        return allUnUsed;
    }

    /**
     * 组织小程序展示数据
     * @param dtoOrderList
     * @return
     */
    private List<DTOFlightInfo> generateDTOFlightInfo(List<DTOOrder> dtoOrderList) {
        List<DTOFlightInfo> dtoFlightInfoList = new ArrayList<DTOFlightInfo>();
        Map<String, List<DTOFlightInfo.Segment>> map = new HashMap<String, List<DTOFlightInfo.Segment>>();
        for (DTOOrder dtoOrder : dtoOrderList) {
            for (DTOPassenger dtoPassenger : dtoOrder.getPassengerList()) {
                for (DTOPaxSegment dtoPaxSegment : dtoPassenger.getPassengerSegmentList()) {
                    List<DTOFlightInfo.Segment> segmentList = new ArrayList<DTOFlightInfo.Segment>();
                    DTOFlightInfo.Segment segment = new DTOFlightInfo.Segment();
                    segment.setAirlineCode(dtoPaxSegment.getAirlineCode());
                    segment.setDepCode(dtoPaxSegment.getDepCode());
                    segment.setDepCityCN(baseInfoUtils.getCityChiName(dtoPaxSegment.getDepCode()));
                    segment.setArrCode(dtoPaxSegment.getArrCode());
                    segment.setArrCityCN(baseInfoUtils.getCityChiName(dtoPaxSegment.getArrCode()));
                    segment.setCabin(dtoPaxSegment.getCabinClass());
                    segment.setFlightNo(dtoPaxSegment.getFlightNo());
                    segment.setFlightDate(DateUtils.dateToString(dtoPaxSegment.getDepTime()));
                    segment.setPassengerName(dtoPassenger.getName());
                    segment.setPassengerType(dtoPassenger.getPassengerType());
                    segmentList.add(segment);

                    if (map.get(dtoPaxSegment.getTicketIssued().getTicketNo()) != null) {
                        List<DTOFlightInfo.Segment> tmpList = map.get(dtoPaxSegment.getTicketIssued().getTicketNo());
                        tmpList.add(segment);
                        map.put(dtoPaxSegment.getTicketIssued().getTicketNo(), tmpList);
                        for (DTOFlightInfo dtoFlightInfo : dtoFlightInfoList) {
                            if (dtoFlightInfo.getTicketNo().equals(dtoPaxSegment.getTicketIssued().getTicketNo())) {
                                dtoFlightInfo.setSegmentList(tmpList);
                            }
                        }
                    } else {
                        map.put(dtoPaxSegment.getTicketIssued().getTicketNo(), segmentList);
                        DTOFlightInfo dtoFlightInfo = new DTOFlightInfo();
                        dtoFlightInfo.setTicketNo(dtoPaxSegment.getTicketIssued().getTicketNo());
                        dtoFlightInfo.setInternational(dtoOrder.getInternational() == 1);
                        dtoFlightInfo.setSegmentList(segmentList);
                        dtoFlightInfoList.add(dtoFlightInfo);
                    }
                }
            }
        }

        return dtoFlightInfoList;
    }

    /**
     * 根据换开了的票号查询换开到的票号
     * @param ticketNo
     * @return
     */
    private String getNewTktNoByExchangedTktNo(String ticketNo, String passengerName, String refundType) {
        if (!StringUtils.hasText(ticketNo)) {
            return "";
        }
        // 改期类的，如果没查到结果，尝试获取新票票号，提示给用户
        if ("FLT_CHANGED".equals(refundType)) {
            return flightRefundSpecialServiceHelper.getNewTktNoByExchangedTktNo(ticketNo, passengerName);
        }
        return "";
    }

    /**
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param msg
     * @param: @return
     * @return: JSONResult
     * @throws
     */
    public JSONResult getErrorJsonResult(StatusCode statusCode) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(statusCode.getCode(), statusCode.getMsg()));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

    public JSONResult getErrorJsonResult(String code, String message) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(code, message));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

}
