package net.jdair.specialrefund.bizz.controller.backend.domain;

import lombok.Data;
import net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg;
import net.jdair.specialrefund.bizz.vo.DTOFltRefundPaxSeg;
import net.jdair.specialrefund.bizz.vo.DTOPayment;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundBankInfo;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundImg;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 退票单列表查询返回
 */
@Data
public class SearchRefundOrderListRes {

    private Long id;
    private Long orderId;
    private String refundNo;
    private String reserveRemark;  // 暂存国际票订单号
    private String userName;
    private Date createTime;
    private Date retrieveTime;//清位时间
    private String status;
    private String remark; //退票申请备注

    /**
     * 退票原因
     */
    private String constraintReason;
    private String payMode; //退款方式（线上：online 线下:offline）
    private Date payTime;    //退款时间
    private String refundType;//退票类型(当日作废、自愿退票、非自愿退票)
    private String officeNo;

    /**
     * 退票航段信息
     */
    private List<EtFltRefundPaxSeg> fltRefundPaxSegList = new ArrayList<EtFltRefundPaxSeg>();

    /**
     * 退票人
     */
    private String refundUser ;
    /**
     * 退款时间
     */
    private Date refundTime;
    /*
     * 用户类型
     */
    private String userType;
    /*
     * 订单来源
     */
    private String orderSource;

}