package net.jdair.specialrefund.bizz.controller.backend.domain;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 一审操作请求
 */
@Data
public class FirstAuditReq {
    private Long refundId;
    private String auditUserName;  //审核人
    private String auditRemark;  // 审核备注信息
    private String constraintReason;  // 支持修改退票原因
    private String refundType;  // 支持修改退票类型
    private List<Double> actualAmountList = new ArrayList<>();  // 实退金额列表
    private List<Long> amountIndex = new ArrayList<>();  // paxSegId 列表
    //审核人上传图片
    private MultipartFile[] resources;

}