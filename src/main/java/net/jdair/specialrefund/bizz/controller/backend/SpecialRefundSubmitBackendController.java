package net.jdair.specialrefund.bizz.controller.backend;

import cn.hutool.core.date.DateUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.hnair.opcnet.api.ews.ramsitf2Hu.RamsitfApi;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtPassengerSegment;
import net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo;
import net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialTicketNoBlackListServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundWithoutOrderServiceHelper;
import net.jdair.specialrefund.bizz.mapper.EtSpecialRefundBankInfoMapper;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.utils.PNRManage;
import net.jdair.specialrefund.bizz.utils.ValidateUtils;
import net.jdair.specialrefund.bizz.utils.file.service.FileService;
import net.jdair.specialrefund.bizz.utils.file.vo.OssFileInfo;
import net.jdair.specialrefund.bizz.vo.*;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static net.jdair.specialrefund.bizz.utils.CacheConstant.SPECIAL_REFUND_KEY_PREFIX;


/**
 * 特殊退票--后台管理管理员代客提交
 */
@RestController
@Api(value = "后台管理员--提交退票申请")
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundSubmitBackendController extends BaseController {

    @Reference(version = "1.0.0",retries=2,parameters = { "protocol", "dubbo" })
    private RamsitfApi ramsitfApi;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    @Autowired
    private FlightRefundWithoutOrderServiceHelper flightRefundWithoutOrderServiceHelper;

    @Autowired
    private FlightRefundSpecialTicketNoBlackListServiceHelper flightRefundSpecialTicketNoBlackListServiceHelper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private FileService fileService;


    /**
     * @Fields jsonResult : API接口封装类
     */
    protected JSONResult jsonResult = new JSONResult();

    /**
     * 退票申请提交接口
     *
     * @return
     */
    @RequestMapping(value = "submitRefund", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONResult submitRefundOrder(@ModelAttribute SpecialRefundSubmitBackendReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParams(req)) {
                return jsonResult;
            }
            ValueOperations<String, String> operations = redisTemplate.opsForValue();
            String jsonString = operations.get(SPECIAL_REFUND_KEY_PREFIX + req.getUuid());
            List<DTOOrder> dtoOrderList = JSON.parseObject(jsonString, new TypeReference<List<DTOOrder>>() {
            });

            String refundNo = "";
            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                // 生成退票单
                refundNo = processCreateFltRefund(dtoOrderList, req);
                if (refundNo.equals("")) {
                    return jsonResult;
                }
            } else {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_SUBMIT_ERROR);
                return jsonResult;
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("refundNo", refundNo);

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }

    /**
     * 退票申请提交接口 --- 团队票
     *
     * @return
     */
    @RequestMapping(value = "submitRefundGT", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONResult submitRefundOrderGT(@ModelAttribute SpecialRefundSubmitBackendReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParams(req)) {
                return jsonResult;
            }

            ValueOperations<String, String> operations = redisTemplate.opsForValue();
            String jsonString = operations.get(SPECIAL_REFUND_KEY_PREFIX + req.getUuid());
            List<DTOOrder> dtoOrderList = JSON.parseObject(jsonString, new TypeReference<List<DTOOrder>>() {
            });

            // 团队票退票判断所选航段是否满足要求
            if (!validateParams4GT(req, dtoOrderList)) {
                return jsonResult;
            }

            String refundNo = "";
            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                // 生成退票单
                refundNo = processCreateFltRefund(dtoOrderList, req);
                if (refundNo.equals("")) {
                    return jsonResult;
                }
            } else {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_SUBMIT_ERROR);
                return jsonResult;
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("refundNo", refundNo);

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }

    /**
     * 国际票退票申请提交接口
     *
     * @return
     */
    @RequestMapping(value = "submitRefundGJ", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONResult submitRefundOrderGJ(@ModelAttribute SpecialRefundSubmitBackendReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParams(req)) {
                return jsonResult;
            }

            if (!validateParams4GJ(req)) {
                return jsonResult;
            }

            ValueOperations<String, String> operations = redisTemplate.opsForValue();
            String jsonString = operations.get(SPECIAL_REFUND_KEY_PREFIX + req.getUuid());
            List<DTOOrder> dtoOrderList = JSON.parseObject(jsonString, new TypeReference<List<DTOOrder>>() {
            });

            String refundNo = "";
            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                // 生成退票单
                refundNo = processCreateFltRefund(dtoOrderList, req);
                if (refundNo.equals("")) {
                    return jsonResult;
                }
            } else {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_SUBMIT_ERROR);
                return jsonResult;
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("refundNo", refundNo);

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }

    /**
     * 校验是否重复提交
     *
     * @return
     */
    @RequestMapping(value = "checkMultipleSubmit", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONResult checkMultipleSubmit(@ModelAttribute SpecialRefundSubmitBackendReq req) {
        jsonResult = new JSONResult();

        try {
            boolean isMultipleSubmit = false;
            // 验证 客票信息
            if (!StringUtils.hasText(req.getTicketInfos())) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETINFOS_NULL);
                return jsonResult;
            } else if (!ValidateUtils.isValidByRegex(req.getTicketInfos(), ValidateUtils.JD_SPECIAL_REFUND_TICKETINFOS_REGEX)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETINFOS_ERROR);
                return jsonResult;
            }

            // 验证 退票类型
            if (!StringUtils.hasText(req.getRefundType())) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_NULL);
                return jsonResult;
            } else if (!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_TYPE_REGEX_AGENT)) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
                return jsonResult;
            }

            boolean inter = false;
            ValueOperations<String, String> operations = redisTemplate.opsForValue();
            String jsonString = operations.get(SPECIAL_REFUND_KEY_PREFIX + req.getUuid());
            List<DTOOrder> dtoOrderList = JSON.parseObject(jsonString, new TypeReference<List<DTOOrder>>() {
            });
            if (dtoOrderList != null && dtoOrderList.size() > 0) {
                if (dtoOrderList.get(0).getInternational() == 1) {
                    inter = true;
                }
            }

            String[] ticketInfoList = req.getTicketInfos().split(";");

            for (String ticketInfo : ticketInfoList) {
                String ticketNo = ticketInfo.split(",")[0].split("-")[1];
                String depCode = ticketInfo.split(",")[1].split("-")[0];
                String arrCode = ticketInfo.split(",")[1].split("-")[1];

                // 校验是否重复提交
                DTORefundCriteria dtoRefundCriteria = new DTORefundCriteria();
                dtoRefundCriteria.setTicketNo(ticketNo);
                dtoRefundCriteria.setRefundType(req.getRefundType());
                dtoRefundCriteria.setOrderSource("TS");
                if (inter) {
                    dtoRefundCriteria.setOrderSource("TSGJ");
                }
                dtoRefundCriteria.setPageSize(10);
                dtoRefundCriteria.setCurrentPageNum(1);
                List<EtFltRefund> etFltRefundList = flightRefundServiceHelper.findSpecialRefundByParams(null, ticketNo, req.getRefundType(), Lists.newArrayList(dtoRefundCriteria.getOrderSource()));

                if (etFltRefundList != null && etFltRefundList.size() > 0) {
                    for (EtFltRefund dtoFltRefund : etFltRefundList) {
                        for (EtPassengerSegment etPassengerSegment : dtoFltRefund.getEtPassengerSegmentList()) {
                            if (depCode.equals(etPassengerSegment.getEtSegment().getDepCode())
                                    && arrCode.equals(etPassengerSegment.getEtSegment().getArrCode())) {
                                isMultipleSubmit = true;
                                break;
                            }

                        }
                    }
                }
            }

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("isMultipleSubmit", isMultipleSubmit);

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 组织生成退票单
     *
     * @return
     */
    private String processCreateFltRefund(List<DTOOrder> dtoOrderList, SpecialRefundSubmitBackendReq req) {
        Boolean inter = dtoOrderList.get(0).getInternational() == 1 ? true : false;

        String imgUrls = "";

        // 处理附件url

        String refundNo = "";

        String remark = req.getRemark();
        String flightNos = "";
        String passengerNames = "";
        String passengerTypes = "";
        String depTimes = "";
        String arrTimes = "";
        String ticketNos = "";
        String pnrs = "";
        String cabins = "";
        String ticketPrices = "";
        String taxPrices = "";
        // String totalAmount = "10200";
        // String thirdPaymentNo = "2020071422001483631432069634";
        String lines = "";
        Object UserUtils;
        String user = req.getLoginUserName();

        String[] ticketInfoList = req.getTicketInfos().split(";");

        Map<String, String> netFareMap = new HashMap<String, String>(); // 票面
        Map<String, String> taxMap = new HashMap<String, String>(); // 税费
        Map<String, String> originNetFareMap = new HashMap<String, String>(); // 换开原票的票面
        Map<String, String> originTaxMap = new HashMap<String, String>(); // 换开原票的税费
        Map<String, String> originCurrencyMap = new HashMap<String, String>(); // 货币代码
        Map<String, String> currencyMap = new HashMap<String, String>(); // 换开原票的货币代码
        if (inter) {
            // 国际票，从 ods esb 接口获取票面、税费
            try {
                handleFee4GJ(req, dtoOrderList, netFareMap, taxMap, currencyMap, originNetFareMap, originTaxMap, originCurrencyMap);
            } catch (Exception e) {
                e.printStackTrace();
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_SUBMIT_GJ_ESB_ERROR);
                return "";
            }
        }

        for (DTOOrder dtoOrder : dtoOrderList) {
            for (DTOPassenger dtoPassenger : dtoOrder.getPassengerList()) {
                for (DTOPaxSegment dtoPaxSegment : dtoPassenger.getPassengerSegmentList()) {
                    for (String ticketInfo : ticketInfoList) {
                        String ticketNo = ticketInfo.split(",")[0];
                        String depCode = ticketInfo.split(",")[1].split("-")[0];
                        String arrCode = ticketInfo.split(",")[1].split("-")[1];

                        if (ticketNo.equals(dtoPaxSegment.getTicketIssued().getTicketNo())
                                && depCode.equals(dtoPaxSegment.getDepCode())
                                && arrCode.equals(dtoPaxSegment.getArrCode())) {
                            flightNos += dtoPaxSegment.getFlightNo() + ";";
                            passengerNames += dtoPassenger.getName() + ";";
                            passengerTypes += dtoPassenger.getPassengerType().name() + ";";
                            depTimes += DateUtils.dateToString(dtoPaxSegment.getDepTime(), "yyyy-MM-dd HH:mm") + ";";
                            arrTimes += DateUtils.dateToString(dtoPaxSegment.getDepTime(), "yyyy-MM-dd HH:mm") + ";";
                            ticketNos += ticketNo + ";";
                            pnrs += (dtoPassenger.getPnrValue() == null ? "" : dtoPassenger.getPnrValue().getPnrNo()) + ";";
                            cabins += dtoPaxSegment.getCabinClass() + ";";
                            ticketPrices += dtoPaxSegment.getNetFare() + ";";
                            taxPrices += dtoPaxSegment.getAirportTax() + ";";
                            lines += depCode + "-" + arrCode + ";";
                        }
                    }
                }
            }
        }

        flightNos = flightNos.substring(0, flightNos.length() - 1);
        passengerNames = passengerNames.substring(0, passengerNames.length() - 1);
        passengerTypes = passengerTypes.substring(0, passengerTypes.length() - 1);
        depTimes = depTimes.substring(0, depTimes.length() - 1);
        arrTimes = arrTimes.substring(0, arrTimes.length() - 1);
        pnrs = pnrs.substring(0, pnrs.length() - 1);
        cabins = cabins.substring(0, cabins.length() - 1);
        ticketPrices = ticketPrices.substring(0, ticketPrices.length() - 1);
        taxPrices = taxPrices.substring(0, taxPrices.length() - 1);
        lines = lines.substring(0, lines.length() - 1);

        // TODO 生成退票单
        String refundTypeRe = "CONSTRAINT";
        String constraintReason = req.getRefundType();

        //退票单信息
        DTOFltRefund fltRefund = new DTOFltRefund(0l, "SYSTEM");

        fltRefund.setRemark(remark);
        fltRefund.setConstraintReason(req.getRefundReason());
        fltRefund.setRefundType(req.getRefundType());
        fltRefund.setOrderSource("TS");
        if (inter) {
            fltRefund.setOrderSource("TSGJ");
        }
        fltRefund.setUserName(user);
        fltRefund.setRefundUser(user);
        fltRefund.setReserveRemark(user);
        fltRefund.setRefundTime(new Date());
        fltRefund.setFictitious("ADMIN");  // 记录退单类型

        //航段信息
        String[] flightNo = flightNos.split(";");
        String[] depTime = depTimes.split(";");
        String[] arrTime = arrTimes.split(";");
        String[] tickets = ticketNos.split(";");
        String[] pnr = pnrs.split(";");
        String[] cabin = cabins.split(";");
        String[] ticketPrice = ticketPrices.split(";");
        String[] taxPrice = taxPrices.split(";");


        List<DTOSegment> dtoSegmentList = new ArrayList<DTOSegment>();
        Set<String> lineSet = new HashSet<String>();
        int i = 0;
//		int j = 0; //单人所有航段是一个总价
        for (String line : lines.split(";")) {
            if (!StringUtils.hasText(line)) {
                break;
            }

//			if (lineSet.contains(line)) {
//			    j ++;
//                lineSet.clear();
//            }

            String[] code = line.split("-");
            DTOSegment segment = new DTOSegment();
            segment.setDepCode(code[0]);
            segment.setArrCode(code[1]);

            segment.setDepTime(DateUtil.parse(depTime[i], "yyyy-MM-dd HH:mm"));
            segment.setFlightNo(flightNo[i]);
            segment.setAirlineCode("JD");
            segment.setArrTime(DateUtil.parse(arrTime[i], "yyyy-MM-dd HH:mm"));
            segment.setCabinClass(cabin[i]);
            segment.setBaseFare(Double.parseDouble("0"));
            segment.setCabinPrice(Double.parseDouble("0"));
//			segment.setProductCode("GJP");
            dtoSegmentList.add(segment);
            i++;
        }

        List<BigDecimal> ticketPricesList = new ArrayList<BigDecimal>();
        for (String price : ticketPrice) {
            ticketPricesList.add(new BigDecimal(price));
        }

        List<BigDecimal> taxPricesList = new ArrayList<BigDecimal>();
        for (String price : taxPrice) {
            taxPricesList.add(new BigDecimal(price));
        }

        List<DTOTicket> dtoTicketList = new ArrayList<DTOTicket>();
        for (String ticket : tickets) {
            DTOTicket dtoTicket = new DTOTicket();
            dtoTicket.setIssCode(ticket.split("-")[0]);
            dtoTicket.setTicketNo(ticket.split("-")[1]);

            // 校验票号是否黑名单
            List<DTOSpecialRefundTicketNoBlackList> dtoSpecialRefundTicketNoBlackList = flightRefundSpecialTicketNoBlackListServiceHelper.findByTicketNoAndStatus(dtoTicket.getTicketNo(), SpecialRefundTicketNoBlackListStatus.ACTIVE);
            if (dtoSpecialRefundTicketNoBlackList != null && dtoSpecialRefundTicketNoBlackList.size() > 0) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETNO_IN_BLACKLIST);
                return "";
            }

            dtoTicketList.add(dtoTicket);
//            dtoTicket.setIssuedDate(new Date()); // TODO 票面历史中获取
//            dtoTicket.setBookTime(new Date()); // TODO 票面历史中获取
        }

        //旅客信息
        List<DTOPassenger> dtoPassengerList = new ArrayList<DTOPassenger>();
        String[] passengerNameTmp = passengerNames.split(";");
//        String[] cTypeArray = cTypes.split(";");
//        String[] cNoArray = cNos.split(";");
        String[] passengerTypeArray = passengerTypes.split(";");
        int index = 0;
        for (int j = 0; j < passengerNameTmp.length; j++) {
            if (!StringUtils.hasText(passengerNameTmp[j])) {
                break;
            }
            String pType = passengerTypeArray[index];

            // 成人持卡人姓名必须为本人
//            if (PassengerType.ADULT == PassengerType.valueOf(pType)) {
//                if (!this.cardHolder.equals(passengerNameTmp[j])) {
//                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDHOLDER_NOT_MATCH);
//                    return "";
//                }
//            }
            DTOPassenger dtoPassenger = new DTOPassenger();
            dtoPassenger.setName(passengerNameTmp[j]);
            dtoPassenger.setCertificateType("--");
            dtoPassenger.setCertificateNo("--");
            dtoPassenger.setPassengerType(PassengerType.valueOf(pType));

            if (pnr.length > 0 && StringUtils.hasText(pnr[j])) {
                DTOPnr dtoPnr = new DTOPnr();
                dtoPnr.setPnrNo(pnr[j]);
                dtoPassenger.setPnrValue(dtoPnr);
            }

            dtoPassengerList.add(dtoPassenger);
            index++;
        }

        DTOSpecialRefundBankInfo dtoSpecialRefundBankInfo = new DTOSpecialRefundBankInfo();
        dtoSpecialRefundBankInfo.setCardHolder(req.getCardHolder());
        dtoSpecialRefundBankInfo.setCardNo(req.getCardNo());
        dtoSpecialRefundBankInfo.setBankName(req.getBankName());
        dtoSpecialRefundBankInfo.setMobileNo(req.getMobileNo());
        dtoSpecialRefundBankInfo.setNbkno(req.getNbkno());
        dtoSpecialRefundBankInfo.setRemark(req.getEcardRemark());  // 代客提交，传给易生的备注信息

        List<DTOSpecialRefundImg> dtoSpecialRefundImgList = new ArrayList<DTOSpecialRefundImg>();

        // 保存图片
        dtoSpecialRefundImgList = this.dealImg(req);

        //支付信息
        DTOPayment dtoPayment = new DTOPayment();
        dtoPayment.setAmount(calFareFrice(dtoOrderList.get(0)));
        dtoPayment.setPayType(PayType.CHINA_EASY_CARD);  // 默认虚拟为 易生支付
        dtoPayment.setPayTime(new Date());
        dtoPayment.setCurrency("");  //货币
//		payment.setPayerName();
//		payment.setPayerContact();


        log.info("" + fltRefund
                + dtoSegmentList
                + dtoTicketList
                + dtoPassengerList
                + ticketPrices
                + taxPrices
                + dtoSpecialRefundBankInfo
        );

        try {
            if (inter) {
                refundNo = flightRefundWithoutOrderServiceHelper.createSpecialFltRefundGJ(fltRefund
                        , dtoPayment
                        , dtoSegmentList
                        , dtoTicketList
                        , dtoPassengerList
                        , netFareMap, taxMap, currencyMap, originNetFareMap, originTaxMap, originCurrencyMap
                        , dtoSpecialRefundBankInfo
                        , dtoSpecialRefundImgList);
            } else {
                refundNo = flightRefundWithoutOrderServiceHelper.createSpecialFltRefund(fltRefund
                        , dtoPayment
                        , dtoSegmentList
                        , dtoTicketList
                        , dtoPassengerList
                        , currencyMap, originCurrencyMap
                        , ticketPricesList
                        , taxPricesList
                        , dtoSpecialRefundBankInfo
                        , dtoSpecialRefundImgList);
            }

            if (StringUtils.hasText(refundNo)) {
                /**
                 * 只有【航班改期后退票】和【临期退票/过期退税】和【其他特殊退票】这3类需要走退票清位
                 */
                if ("FLT_CHANGED".equals(req.getRefundType())
                        || "EXPIRED".equals(req.getRefundType())
                        || "OTHER_REASON".equals(req.getRefundType())
                ) {
                    // 异步退票、清位
                    flightRefundSpecialServiceHelper.noPnrAndRefundTicket(refundNo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CREATE_ERROR);
            return "";
        }

        if (StringUtils.hasText(refundNo) && !refundNo.contains("ERROR")) {
            return refundNo;
        } else {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CREATE_ERROR);
            return "";
        }

    }


    /**
     * 从 ods esb 接口获取票面、机建燃油
     *
     * @param dtoOrderList
     */
    private void handleFee4GJ(SpecialRefundSubmitBackendReq req, List<DTOOrder> dtoOrderList,
                              Map<String, String> netFareMap, Map<String, String> taxMap, Map<String, String> currencyMap,
                              Map<String, String> originNetFareMap, Map<String, String> originTaxMap, Map<String, String> originCurrencyMap
    ) throws Exception {
        for (DTOOrder dtoOrder : dtoOrderList) {
            for (DTOPassenger dtoPassenger : dtoOrder.getPassengerList()) {
                int segmentCount = 0;
                for (DTOPaxSegment dtoPaxSegment : dtoPassenger.getPassengerSegmentList()) {
                    segmentCount++;
                    if (segmentCount == 1) {  // 税费设置在第一段
                        DTOTicket dtoTicket = dtoPaxSegment.getTicketIssued();
                        String issCode = dtoTicket.getTicketNo().split("-")[0];
                        String ticketNo = dtoTicket.getTicketNo().split("-")[1];
                        List<EsbPaxTaxDetailVO.PaxTaxDetail> paxTaxDetailList = getPriceAndFeeFromEsb(issCode, ticketNo);
                        String taxStr = "";
                        for (EsbPaxTaxDetailVO.PaxTaxDetail paxTaxDetail : paxTaxDetailList) {
                            if (StringUtils.hasText(paxTaxDetail.getTAX_CODE())) {
                                // 税费字符串格式，如： YQ:52.0,J9:15.0,YR:2400.0,CN:90.0,YP:150.0,PT:59.0
                                taxStr += paxTaxDetail.getTAX_CODE() + ":" + paxTaxDetail.getTAX_FEE() + ",";
                            }
                        }
                        taxStr = taxStr.length() > 0 ? taxStr.substring(0, taxStr.length() - 1) : taxStr;
                        netFareMap.put(dtoTicket.getTicketNo(), paxTaxDetailList.get(0).getD_FARE().toString());
                        taxMap.put(dtoTicket.getTicketNo(), taxStr);
                        currencyMap.put(dtoTicket.getTicketNo(), paxTaxDetailList.get(0).getCUR_CODE());

                        // 改期后退票，获取原票票面、税费
                        if ("FLT_CHANGED".equals(req.getRefundType())) {
                            DETR2F detr2f = new DETR2F();
                            PNRManage.configIBEClient(detr2f, "JD");
                            String secondFactorCode = "NM";
                            String secondFactorValue = dtoPassenger.getName();
                            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                            // 双因素处理无陪儿童
                            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                            DETRTKTResult detrtktResult = null;
                            try {
                                detrtktResult = detr2f.getTicketInfoByTktNo2F(issCode + "-" + ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
                            } catch (Exception e) {
                                // 当前库中没有，即客票已进历史
                                if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                                    detrtktResult = detr2f.getTicketInfoByTktNo2F(issCode + "-" + ticketNo, false, "", "H", secondFactorCode, secondFactorValue);
                                }
                            }
                            String exchangedTicketNo = detrtktResult.getExchangeInfo();
                            String originIssCode = exchangedTicketNo.split("-")[0];
                            String originTicketNo = exchangedTicketNo.split("-")[1];
                            List<EsbPaxTaxDetailVO.PaxTaxDetail> originPaxTaxDetailList = getPriceAndFeeFromEsb(originIssCode, originTicketNo);
                            String originTaxStr = "";
                            for (EsbPaxTaxDetailVO.PaxTaxDetail paxTaxDetail : originPaxTaxDetailList) {
                                if (StringUtils.hasText(paxTaxDetail.getTAX_CODE())) {
                                    // 税费字符串格式，如： YQ:52.0,J9:15.0,YR:2400.0,CN:90.0,YP:150.0,PT:59.0
                                    originTaxStr += paxTaxDetail.getTAX_CODE() + ":" + paxTaxDetail.getTAX_FEE() + ",";
                                }
                            }
                            originTaxStr = originTaxStr.length() > 0 ? originTaxStr.substring(0, originTaxStr.length() - 1) : originTaxStr;
                            originNetFareMap.put(exchangedTicketNo, originPaxTaxDetailList.get(0).getD_FARE().toString());
                            originTaxMap.put(exchangedTicketNo, originTaxStr);
                            originCurrencyMap.put(exchangedTicketNo, originPaxTaxDetailList.get(0).getCUR_CODE());
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据票号调用ESB接口获取票面、税费
     *
     * @param issCode
     * @param ticketNo
     * @return
     */
    private List<EsbPaxTaxDetailVO.PaxTaxDetail> getPriceAndFeeFromEsb(String issCode, String ticketNo) throws Exception {

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setOption("issCo", issCode);
        apiRequest.setOption("tno", ticketNo);

        ApiResponse apiResponse = null;
        try {
            log.info("ramsitfApi getPaxTaxDetail begin: " + issCode + "-" + ticketNo);
            apiResponse = ramsitfApi.getPaxTaxDetail(apiRequest);
        } catch (Exception e) {
            log.error("ramsitfApi getPaxTaxDetail error:" + e.getMessage());
            throw e;
        }
        log.info("ramsitfApi getPaxTaxDetail end: ");
        String responseStr = (String) apiResponse.getExtend().get("jsonStr");
        log.info(JSON.toJSONString(apiResponse));

//        String url = "http://esb.jdair.net/dubbo-consumer/getPaxTaxDetail";
//        SortedMap<Object, Object> params = new TreeMap<Object, Object>();
//        params.put("issCode", "898");
//        params.put("ticketNo", "5135502959");
//        String responseStr = HttpInvokeClient.doGet(url, params);
//        JSONObject jsonObject = (JSONObject) JSON.parseObject(responseStr).get("extend");
//        responseStr = (String)jsonObject.get("jsonStr");


        EsbPaxTaxDetailVO response = JSONObject.parseObject(responseStr, EsbPaxTaxDetailVO.class);
        log.info("ramsitfApi getPaxTaxDetail is Ok | System.currentTimeMillis = " + System.currentTimeMillis() + " | response = " + JSON.toJSONString(response));

        if ("0".equals(response.getErrorCode())) {
            return response.getResult();
        } else {
            throw new Exception("数据获取失败");
        }
    }

    /**
     * 保存图片
     *
     * @return
     */
    private List<DTOSpecialRefundImg> dealImg(SpecialRefundSubmitBackendReq req) {
        List<DTOSpecialRefundImg> dtoSpecialRefundImgList = new ArrayList<DTOSpecialRefundImg>();
        String bucketName = "special-refund";
        if (req.getResources() != null && req.getResources().size() > 0) {
            for (MultipartFile file : req.getResources()) {
                HashMap<String, Object> res = new HashMap<>();
                try {
                    String originalFileName = file.getOriginalFilename();
                    String fileType = originalFileName.substring(Objects.requireNonNull(originalFileName).lastIndexOf(".") + 1);
                    if (!"png,jpg,jpeg,bmp,gif".contains(fileType)) {
                        throw new SpecialRefundException("请上传jpg、jpeg、bmp、gif、png格式文件！");
                    }
                    String preName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                    if (org.apache.commons.lang3.StringUtils.isBlank(preName)) {
                        throw new SpecialRefundException("文件名不能为空。");
                    }
                    OssFileInfo ossFileInfo = fileService.ossUpload(file, bucketName);
                    DTOSpecialRefundImg refundImg = new DTOSpecialRefundImg();

                    refundImg.setFltRefundId(0L);
                    refundImg.setImgUrl(ossFileInfo.getFilePath());
                    refundImg.setImgOriginName(originalFileName);
                    refundImg.setStatus(SpecialRefundImgStatus.ACTIVE);
                    refundImg.setCreateTime(new Date());
                    refundImg.setOperator(req.getLoginUserName());
                    refundImg.setRefundNo(req.getFltRefundNo());
                    refundImg.setSource(SpecialRefundImgSource.AUDITOR);
                    dtoSpecialRefundImgList.add(refundImg);
                } catch (Exception e) {
                    log.error("附件上传处理异常：" + e);
                    e.printStackTrace();
                }
            }
        }
        return dtoSpecialRefundImgList;
    }


    private String generateFileName(String fileName) {
        DateFormat format = new SimpleDateFormat("yyMMddHHmmss");
        // 取得当前日期
        String formatDate = format.format(new Date());
        // 产生一个随机数
        Random rm = new Random();
        String random = String.valueOf(rm.nextDouble() * Math.pow(10, 4)).substring(0, 4);
        // 加符号："."
        int position = fileName.lastIndexOf(".");
//		String extension = fileName.substring(position);// 扩展名
//		String fileNameBegin = fileName.substring(0,position);
        // 返回附件名字
        return formatDate + random + fileName;
    }

    /**
     * 计算订单的金额
     *
     * @param dtoOrder
     * @return
     */
    private BigDecimal calFareFrice(DTOOrder dtoOrder) {
        BigDecimal amount = new BigDecimal(0);
        for (DTOPassenger dtoPassenger : dtoOrder.getPassengerList()) {
            for (DTOPaxSegment dtoPaxSegment : dtoPassenger.getPassengerSegmentList()) {
                amount = amount.add(new BigDecimal(dtoPaxSegment.getNetFare()))
                        .add(new BigDecimal(dtoPaxSegment.getAirportTax()))
                        .add(new BigDecimal(dtoPaxSegment.getFuelTax()));
            }
        }
        return amount;
    }

    /**
     * @throws
     * @Title: validateParams
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     */
    private boolean validateParams(SpecialRefundSubmitBackendReq req) {
        Map<String, Object> params = new HashMap<String, Object>();

        params.put("refundType", req.getRefundType());
        if (req.getRefundReason() != null) {
            params.put("refundReason", req.getRefundReason());
        }
        params.put("remark", req.getRemark());
        params.put("ticketInfos", req.getTicketInfos());
        params.put("cardHolder", req.getCardHolder());
        params.put("cardNo", req.getCardNo());
        params.put("bankName", req.getBankName());
        params.put("mobileNo", req.getMobileNo());

        // 验证 客票信息
        if (!StringUtils.hasText(req.getTicketInfos())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETINFOS_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getTicketInfos(), ValidateUtils.JD_SPECIAL_REFUND_TICKETINFOS_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TICKETINFOS_ERROR);
            return false;
        }


        // 验证 退票类型
        if (!StringUtils.hasText(req.getRefundType())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_TYPE_REGEX_AGENT)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
            return false;
        } else {
            if (req.getRefundReason() != null && !"".equals(req.getRefundReason())) {
                if ("FLT_CHANGED".equals(req.getRefundType())) {
                    if (!"VOLUNTARY".equals(req.getRefundReason())
                            && !"INVOLUNTARY".equals(req.getRefundReason())) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REASON_ERROR);
                        return false;
                    }
                }
                if ("AIRLINE_REASON".equals(req.getRefundType())) {
                    if (!"DOWNCABIN".equals(req.getRefundReason())
                            && !"ALTERNATE".equals(req.getRefundReason())) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REASON_ERROR);
                        return false;
                    }
                }
                if ("FEE_CHANGED".equals(req.getRefundType())) {
                    if (!"REFUNDFEE".equals(req.getRefundReason())) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REASON_ERROR);
                        return false;
                    }
                }
            }
        }

        // 验证 持卡人姓名
        if (!StringUtils.hasText(req.getCardHolder())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDHOLDER_NULL);
            return false;
        }

        // 验证 银行卡号
        if (!StringUtils.hasText(req.getCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDNO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getCardNo(), ValidateUtils.NUMBER_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDNO_ERROR);
            return false;
        }

        // 验证 开户行名称
        if (!StringUtils.hasText(req.getBankName())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_BANKNAME_NULL);
            return false;
        }

        // 验证 联系人手机号
        if (!StringUtils.hasText(req.getMobileNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_MOBILENO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getMobileNo(), ValidateUtils.MOBILE_NO_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_MOBILENO_NULL);
            return false;
        }

        // 验证 对公账号联行号
        if ("2".equals(req.getAccountType())) {
            if (!StringUtils.hasText(req.getNbkno())) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_NBKNO_NULL);
                return false;
            }
        }


        InputStream inout = null;
        try {
            int i = 0;
            if (req.getResources() != null && req.getResources().size() > 0) {
                for (MultipartFile file : req.getResources()) {
//                    inout = new FileInputStream(file);
//                    int size = inout.available();
//                    if (size > 1024 * 1024 * 4) {
//                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_ADMIN_FILE_TOO_LARGE);
//                        return false;
//                    }

//                    String fileName = req.getResourcesFileName().get(i);
//                    int position = fileName.lastIndexOf(".");
//                    String extension = fileName.substring(position).toLowerCase(Locale.CHINA);// 扩展名
//                    if (!extension.equals(".bmp")
//                            && !extension.equals(".jpg")
//                            && !extension.equals(".jpeg")
//                            && !extension.equals(".png")) {
//                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_ADMIN_FILE_FORMAT_ERROR);
//                        return false;
//                    }
                    i++;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (inout != null) {
                try {
                    inout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return true;
    }

    /**
     * 验证团退票退票时的所选航段。
     * 全去程、全回程、全往返
     *
     * @return
     */
    private boolean validateParams4GT(SpecialRefundSubmitBackendReq req, List<DTOOrder> dtoOrderList) {

        String[] ticketInfo = req.getTicketInfos().split(";");

        if (ticketInfo.length > 1) {  // 大于1段时处理
            Map<String, List<String>> map = new HashMap<String, List<String>>();
            for (int i = 0; i < ticketInfo.length; i++) {
                String ticketNo = ticketInfo[i].split(",")[0];
                String segmentPair = ticketInfo[i].split(",")[1];

                if (map.get(ticketNo) == null) {
                    map.put(ticketNo, new ArrayList<String>(Arrays.asList(segmentPair)));
                } else {
                    List<String> segList = map.get(ticketNo);
                    segList.add(segmentPair);
                    map.put(ticketNo, segList);
                }
            }

            boolean oneSeg = true;  // 标识是否都是单段
            boolean twoSeg = true;  // 标识是否都是往返
            for (int i = 0; i < ticketInfo.length; i++) {
                List<String> segList = map.get(ticketInfo[i].split(",")[0]);
                if (segList.size() == 2) {
                    oneSeg = false;
                }
                if (segList.size() == 1) {
                    twoSeg = false;
                }
            }

            // 单双段都有，不符合要求
            if (!oneSeg && !twoSeg) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + map.keySet().toArray()[0] + "】");
                return false;
            }

            // 单段的，航段必须一致
            if (oneSeg) {
                String firstSeg = map.get(ticketInfo[0].split(",")[0]).get(0);
                for (int i = 1; i < ticketInfo.length; i++) {
                    if (!firstSeg.equals(map.get(ticketInfo[i].split(",")[0]).get(0))) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + ticketInfo[i].split(",")[0] + "】");
                        return false;
                    }
                }

            }


            // 往返的
            if (twoSeg) {
                List<String> firstSegList = map.get(ticketInfo[0].split(",")[0]);

                // 同票号下的航段的出发地目的地必须互为往返
                for (int i = 0; i < ticketInfo.length; i++) {
                    List<String> segList = map.get(ticketInfo[i].split(",")[0]);

                    String goDepCode = segList.get(0).split("-")[0];
                    String goArrCode = segList.get(0).split("-")[1];

                    String backDepCode = segList.get(1).split("-")[0];
                    String backArrCode = segList.get(1).split("-")[1];

                    if (!goDepCode.equals(backArrCode) || !goArrCode.equals(backDepCode)) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + ticketInfo[i + 1].split(",")[0] + "】");
                        return false;
                    }
                }

                // 不同票号的航段本身需要完全匹配
                for (int i = 1; i < ticketInfo.length; i++) {
                    List<String> segList = map.get(ticketInfo[i].split(",")[0]);
                    boolean match = false;
                    if ((firstSegList.get(0).equals(segList.get(0)) && firstSegList.get(1).equals(segList.get(1)))
                            || (firstSegList.get(0).equals(segList.get(1)) && firstSegList.get(1).equals(segList.get(0)))
                    ) {
                        match = true;
                    }
                    if (!match) {
                        jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + ticketInfo[i + 1].split(",")[0] + "】");
                        return false;
                    }
                }
            }

            Set<String> flightNoSet = new HashSet<String>();
            Set<String> flightDateSet = new HashSet<String>();
            Set<String> cabinSet = new HashSet<String>();
            String ticketNoTag = "";

            // 航班号、航班日期、舱位一致性校验
            for (DTOOrder dtoOrder : dtoOrderList) {
                for (DTOPassenger dtoPassenger : dtoOrder.getPassengerList()) {
                    for (DTOPaxSegment dtoPaxSegment : dtoPassenger.getPassengerSegmentList()) {
                        for (String info : ticketInfo) {
                            String ticketNo = info.split(",")[0];
                            String depCode = info.split(",")[1].split("-")[0];
                            String arrCode = info.split(",")[1].split("-")[1];

                            if (ticketNo.equals(dtoPaxSegment.getTicketIssued().getTicketNo())
                                    && depCode.equals(dtoPaxSegment.getDepCode())
                                    && arrCode.equals(dtoPaxSegment.getArrCode())) {
                                if (flightNoSet.add(dtoPaxSegment.getFlightNo())) {
                                    ticketNoTag = ticketNo;
                                }
                                if (cabinSet.add(dtoPaxSegment.getCabinClass())) {
                                    ticketNoTag = ticketNo;
                                }
                                if (flightDateSet.add(DateUtils.dateToString(dtoPaxSegment.getDepTime()))) {
                                    ticketNoTag = ticketNo;
                                }
                            }
                        }
                    }
                }
            }

            if (oneSeg) {
                if (flightNoSet.size() > 1 || flightDateSet.size() > 1 || cabinSet.size() > 1) {
                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + ticketNoTag + "】");
                    return false;
                }
            }

            if (twoSeg) {
                if (flightNoSet.size() > 2 || flightDateSet.size() > 2 || cabinSet.size() > 2) {
                    jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_GT_INFO_MATCH_ERROR.getMsg() + "【" + ticketNoTag + "】");
                    return false;
                }
            }

        }

        return true;
    }

    /**
     * 国际票特殊退票代客提交参数校验
     *
     * @return
     */
    private boolean validateParams4GJ(SpecialRefundSubmitBackendReq req) {
        if (!ValidateUtils.isValidByRegex(req.getRefundType(), ValidateUtils.JD_SPECIAL_REFUND_BACKEND_GJ_TYPE_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_TYPE_ERROR);
            return false;
        }
        return true;
    }

    /**
     * @throws
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     */
    private boolean validateParamsForChange(SpecialRefundSubmitBackendReq req) {

        // 验证 refundNo
        if (!StringUtils.hasText(req.getFltRefundNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getFltRefundNo(), ValidateUtils.NUMBER_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_ERROR);
            return false;
        }

        // 验证 newCardNo
        if (!StringUtils.hasText(req.getCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDNO_NULL);
            return false;
        }

        // 验证 newCardHolder
        if (!StringUtils.hasText(req.getCardHolder())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDHOLDER_NULL);
            return false;
        }

        // 验证 bankName
        if (!StringUtils.hasText(req.getBankName())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_BANKNAME_NULL);
            return false;
        }

        // 验证 mobileNo
        if (!StringUtils.hasText(req.getMobileNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_MOBILENO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getMobileNo(), ValidateUtils.MOBILE_NO_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_MOBILENO_ERROR);
            return false;
        }

        return true;
    }


    /**
     * @throws
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param msg
     * @param: @return
     * @return: JSONResult
     */
    public JSONResult getErrorJsonResult(StatusCode statusCode) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(statusCode.getCode(), statusCode.getMsg()));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

    /**
     * @throws
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param errorMessage
     * @return: JSONResult
     */
    public JSONResult getErrorJsonResult(String errorMessage) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage("-1", errorMessage));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }
}
