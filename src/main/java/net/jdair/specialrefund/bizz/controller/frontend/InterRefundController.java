package net.jdair.specialrefund.bizz.controller.frontend;

import cn.hutool.core.date.DateUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hnair.opcnet.api.ews.ramsitf2Hu.RamsitfApi;
import com.hnair.opcnet.api.ods.de.VariFlightApi;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialTicketNoBlackListServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundWithoutOrderServiceHelper;
import net.jdair.specialrefund.bizz.utils.*;
import net.jdair.specialrefund.bizz.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 国际票/海外站退票服务接口
 */
@RestController
@Api(value = "国际票/海外站退票服务接口")
@RequestMapping("/b2c-flight/")
@Slf4j
public class InterRefundController {

    @Reference(version = "1.0.0",retries=2,parameters = { "protocol", "dubbo" })
    private RamsitfApi ramsitfApi;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    @Autowired
    private FlightRefundWithoutOrderServiceHelper flightRefundWithoutOrderServiceHelper;

    @Autowired
    private FlightRefundSpecialTicketNoBlackListServiceHelper flightRefundSpecialTicketNoBlackListServiceHelper;

    /**
     * @Fields jsonResult : API接口封装类
     */
    protected JSONResult jsonResult = new JSONResult();

    public static final int CACHE_DATE = CacheConstant.MEMC_TIME_MINUTES_30;


    /**
     * 退票申请提交接口
     *
     * @return
     */
    @PostMapping(path = "refundTicketNoOrder", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    public Map<String, String> submitRefundOrder(InterRefundReq req) {
        Map<String, String> res = new HashMap<>();
        if (!checkParams(req)) {
            res.put("result", "ERROR");
            res.put("errorMsg", "need check params");
            return res;
        }

        if (!compareSign(req)) {
            res.put("result", "ERROR");
            res.put("errorMsg", "sign error");
            return res;
        }

        //test
//        if ("1".equals(sign)) {
//			remark = "测试国际票退款";
//
//			refundType = "CONSTRAINT";
//			reason = "1";
//
//			flightNos = "JD123;JD456";
//			passengerNames = "测试来源";
//			depTimes = "2020-01-01 22:00;2020-02-02 22:00";
//			arrTimes = "2020-01-01 22:30;2020-02-02 22:30";
//			ticketNos = "898-32222222";
//
//			pnrs = "PNRPNR";
//			cabins = "C";
//			ticketPrices="10000";
//			taxPrices = "200";
//
//			totalAmount = "10200";
//			thirdPaymentNo = "2020071422001483631432069634";
//
//			lines = "PKX-PKX;PEK-PEK";
//
//			user = "laiyuan test 2line";
//        }

        String refundTypeRe = "";
        if ("1".equals(req.getRefundType())) {
            refundTypeRe = "UNCONSTRAINT";
        } else {
            refundTypeRe = "CONSTRAINT";
        }

        String constraintReason = "";
        // UNTK BINGTUI REPEATBUY OTHER
        if ("CONSTRAINT".equals(refundTypeRe)) { //非自愿退票才设置退票原因
            if ("1".equals(req.getReason())) {
                constraintReason = "UNTK";//"航班取消/延误";
            } else if ("2".equals(req.getReason())) {
                constraintReason = "BINGTUI";//"病退";
            } else if ("3".equals(req.getReason())) {
                constraintReason = "REPEATBUY";//"重复购票";
            } else if ("4".equals(req.getReason())) {
                constraintReason = "REFUSE";//"拒签";
            } else {
                constraintReason = "OTHER";//"其他";
            }
        }

        // 根据 officeNo 区分退票类型（中文国际票、海外站）
        String orderSource = "GJ";  // 中文国际票
        if ("00000098".equals(req.getOfficeNo())) {
            orderSource = "HWZ";  // 海外站
        }

        //退票单信息
        DTOFltRefund fltRefund = new DTOFltRefund(1l, "SYSTEM");

        fltRefund.setRemark(req.getRemark());
        fltRefund.setConstraintReason(constraintReason);
        fltRefund.setRefundType(refundTypeRe);
        fltRefund.setOrderSource(orderSource);
        fltRefund.setUserName(req.getUser());
        fltRefund.setRefundUser("laiyuan");
        fltRefund.setOfficeNo(req.getOfficeNo());
        fltRefund.setReserveRemark(req.getOrderNo());
        fltRefund.setRefundTime(DateUtil.parse(req.getRefundTime(), "yyyy-MM-dd HH:mm:ss"));
        fltRefund.setRetrieveTimeFlag(StringUtils.hasText(req.getHasInsurance()) ? Integer.parseInt(req.getHasInsurance()) : 0);

        //航段信息
        String[] flightNo = req.getFlightNos().split(";");
        String[] depTime = req.getDepTimes().split(";");
        String[] arrTime = req.getArrTimes().split(";");
        String[] tickets = req.getTicketNos().split(";");
        String[] pnr = req.getPnrs().split(";");
        String[] cabin = req.getCabins().split(";");
        String[] ticketPrice = req.getTicketPrices().split(";");
        String[] taxPrice = req.getTaxPrices().split(";");
        String[] allTaxDescArray = req.getAllTaxDesc().split(";");

        if (tickets.length != allTaxDescArray.length) {
            res.put("result", "ERROR");
            res.put("errorMsg", "客票数量和税明细数量不一致");
            return res;
        }

        List<DTOSegment> dtoSegmentList = new ArrayList<DTOSegment>();
        Set<String> lineSet = new HashSet<String>();
        int i = 0;
//		int j = 0; //单人所有航段是一个总价
        for (String line : req.getLines().split(";")) {
            if (!StringUtils.hasText(line)) {
                break;
            }

//			if (lineSet.contains(line)) {
//			    j ++;
//                lineSet.clear();
//            }

            String[] code = line.split("-");
            DTOSegment segment = new DTOSegment();
            segment.setDepCode(code[0]);
            segment.setArrCode(code[1]);

            segment.setDepTime(DateUtil.parse(depTime[i], "yyyy-MM-dd HH:mm"));
            segment.setFlightNo(flightNo[i]);
            segment.setAirlineCode("JD");
            segment.setArrTime(DateUtil.parse(arrTime[i], "yyyy-MM-dd HH:mm"));
            segment.setCabinClass(cabin[i]);
            segment.setBaseFare(Double.parseDouble("0"));
            segment.setCabinPrice(Double.parseDouble("0"));
//			segment.setProductCode("GJP");
            dtoSegmentList.add(segment);
            i++;
        }

        List<BigDecimal> ticketPrices = new ArrayList<BigDecimal>();
        for (String price : ticketPrice) {
            ticketPrices.add(new BigDecimal(price));
        }

        List<BigDecimal> taxPrices = new ArrayList<BigDecimal>();
        for (String price : taxPrice) {
            taxPrices.add(new BigDecimal(price));
        }

        List<DTOTicket> dtoTicketList = new ArrayList<DTOTicket>();
        for (String ticket : tickets) {
            DTOTicket dtoTicket = new DTOTicket();
            dtoTicket.setIssCode(ticket.split("-")[0]);
            dtoTicket.setTicketNo(ticket.split("-")[1]);

            boolean hasTicket = flightRefundWithoutOrderServiceHelper.hasTicketRefund(dtoTicket.getTicketNo());

            if (hasTicket) {
                res.put("result", "ERROR");
                res.put("errorMsg", "ticket has exits:" + dtoTicket.getIssCode() + dtoTicket.getTicketNo());
                return res;
            }

            dtoTicketList.add(dtoTicket);
            dtoTicket.setIssuedDate(DateUtil.parse(req.getIssuedTime(), "yyyy-MM-dd HH:mm:ss"));
            dtoTicket.setBookTime(DateUtil.parse(req.getIssuedTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        //旅客信息
        List<DTOPassenger> dtoPassengerList = new ArrayList<DTOPassenger>();
        String[] passengerName = req.getPassengerNames().split(";");
        String[] cTypeArray = req.getcTypes().split(";");
        String[] cNoArray = req.getcNos().split(";");
        String[] passengerTypeArray = req.getPassengerTypes().split(";");

        int index = 0;
        for (String pn : passengerName) {
            if (!StringUtils.hasText(pn)) {
                break;
            }
            DTOPassenger dtoPassenger = new DTOPassenger();
            dtoPassenger.setName(pn);
            dtoPassenger.setCertificateType(cTypeArray[index] == null ? "--" : cTypeArray[index]);
            dtoPassenger.setCertificateNo(cNoArray[index] == null ? "--" : cNoArray[index]);
            String pType = passengerTypeArray[index];
            if ("CNN".equalsIgnoreCase(pType)) {
                dtoPassenger.setPassengerType(PassengerType.CHILD);
            } else if ("INF".equalsIgnoreCase(pType)) {
                dtoPassenger.setPassengerType(PassengerType.INFANT);
            } else {
                dtoPassenger.setPassengerType(PassengerType.ADULT);
            }
            dtoPassengerList.add(dtoPassenger);
            dtoPassenger.setMobilePhone(req.getMobileNo());
            index++;
        }

        //支付信息
        DTOPayment dtoPayment = new DTOPayment();
        dtoPayment.setPaymentNo(req.getPaymentNo());//thirdPaymentNo
        dtoPayment.setRemark(req.getThirdPaymentNo());//第三方支付流水号，退款

        if ("WX".equals(req.getPayType())) {
            dtoPayment.setPayType(PayType.WX_PAY);
        } else if ("worldpay".equals(req.getPayType())) {
            dtoPayment.setPayType(PayType.WORLD_PAY);
        } else {
            dtoPayment.setPayType(PayType.ALIPAY);
        }
        dtoPayment.setPayTime(DateUtil.parse(req.getIssuedTime(), "yyyy-MM-dd HH:mm:ss"));
//		payment.setPayerName();
//		payment.setPayerContact();

        if ("00000098".equals(req.getOfficeNo())) {
            dtoPayment.setAmount(new BigDecimal(req.getTotalAmount().split("-")[0]));  // totalAmount 格式示例：1300.0-CNY
            dtoPayment.setCurrency(req.getTotalAmount().split("-")[1]);  // totalAmount 格式示例：1300.0-CNY
            // 海外站退票，从esb接口获取设置税费
            try {
                this.handleFee4GJ(dtoTicketList, taxPrice, allTaxDescArray);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            dtoPayment.setAmount(new BigDecimal(req.getTotalAmount()));
            dtoPayment.setCurrency("CNY");
        }

        log.info("" + fltRefund
                + dtoPayment
                + dtoSegmentList
                + dtoTicketList
                + dtoPassengerList
                + ticketPrices + taxPrices);

        String refundNo = "";
        try {
            refundNo = flightRefundWithoutOrderServiceHelper.createFltRefundMsg(fltRefund
                    , dtoPayment
                    , dtoSegmentList
                    , dtoTicketList
                    , dtoPassengerList
                    , ticketPrices, taxPrices, allTaxDescArray);
        } catch (Exception e) {
            e.printStackTrace();
            res.put("errorMsg", e.getMessage());
            res.put("result", "ERROR");
            return res;
        }

        if (StringUtils.hasText(refundNo) && !refundNo.contains("ERROR")) {
            res.put("result", "SUCCESS");
            res.put("refundNo", refundNo);
        } else {
            res.put("result", "ERROR");
        }
        return res;
    }


    /**
     * 国际票退票上传附件接口
     *
     * @return
     */
    @PostMapping(path = "refundTicketNoOrderUploadImg", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public Map<String, String> refundTicketNoOrderUploadImg(InterRefundUploadReq req) {
        Map<String, String> res = new HashMap<>();

        if (!checkParamsUpload(req)) {
            res.put("result", "ERROR");
            res.put("errorMsg", "need check params");
            return res;
        }

        if (!compareSignUpload(req)) {
            res.put("result", "ERROR");
            res.put("errorMsg", "sign error");
            return res;
        }

        try {
            flightRefundWithoutOrderServiceHelper.saveUploadImg(req.getRefundNo(), req.getImgUrlList().toArray(new String[req.getImgUrlList().size()]));
        } catch (Exception e) {
            e.printStackTrace();
            res.put("result", "ERROR");
            res.put("errorMsg", "系统异常，请稍后再试");
            return res;
        }
        res.put("result", "SUCCESS");
        return res;
    }


    /**
     * 国际票退票查询退票审核状态接口
     *
     * @return
     */
    @PostMapping(path = "searchRefundOrder", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    public Map<String, String> searchRefundOrder(InterRefundSearchReq req) {
        Map<String, String> res = new HashMap<>();

        if (!StringUtils.hasText(req.getRefundNo()) || !StringUtils.hasText(req.getTime())) {
            res.put("result", "ERROR");
            res.put("errorMsg", "need check params");
            return res;
        }

        if (!compareSignSearch(req)) {
            res.put("result", "ERROR");
            res.put("errorMsg", "sign error");
            return res;
        }

        Map<String, String>  resultMap = null;
        try {
            resultMap = flightRefundWithoutOrderServiceHelper.queryRefundOrderStatus(req.getRefundNo());
        } catch (Exception e) {
            log.error("SearchRefundOrderStatus:" + e.getMessage());
            res.put("result", "ERROR");
            res.put("errorMsg", "系统异常，请稍后再试");
            return res;
        }

        String refundOrderStatus = resultMap.get("status");
        String status = "";
        if (refundOrderStatus != null) {
            if (refundOrderStatus.equals("FPASS")) {
                status = "审核中";
            } else if (refundOrderStatus.equals("PASS") || refundOrderStatus.equals("PEND")||refundOrderStatus.equals("PENDING")) {
                status = "审核通过";
            } else if (refundOrderStatus.equals("FAIL") ) {
                status = "退款失败";
            } else if (refundOrderStatus.equals("REJECT")) {
                status = "审核中";
            } else if (refundOrderStatus.equals("FREJECT")) {
                status = "已拒绝";
            } else if (refundOrderStatus.equals("PAID") ) {
                status = "已退款";
            } else {
                status = "审核中";
            }
        } else {
            status = "审核中";
        }

        String refundAmount = resultMap.get("refundAmount") == null ? "0": resultMap.get("refundAmount");
        String fee = resultMap.get("fee") == null ? "0": resultMap.get("fee");
        res.put("result", "SUCCESS");
        res.put("refundNo", req.getRefundNo());
        res.put("status", status);
        res.put("refundAmount", refundAmount);
        res.put("fee", fee);

        return res;
    }

    private boolean checkParams(InterRefundReq req) {
        log.info("lines" + req.getLines() + req.getFlightNos() + req.getPassengerNames() + req.getDepTimes() + req.getArrTimes() + req.getTicketNos() + req.getPnrs()
                + ",cabins:" + req.getCabins()
                + ",ticketPrices:" + req.getTicketPrices() + req.getTaxPrices() + req.getTotalAmount() + req.getThirdPaymentNo()
                + ",user:" + req.getUser() + req.getRemark()
                + ",refundType:" + req.getRefundType() + req.getReason()
                + ",passengerCer:" + req.getcTypes() + req.getcNos()
                + ",passengerTypes:" + req.getPassengerTypes() + ",issuedTime:" + req.getIssuedTime() + ",refundTime:" + req.getRefundTime()
                + ",mobileNo:" + req.getMobileNo() + ",payType:" + req.getPayType() + ",officeNo:" + req.getOfficeNo() + ",allTaxDesc:" + req.getAllTaxDesc() + ",hasInsurance:" + req.getHasInsurance());
        if (!StringUtils.hasText(req.getLines()) || !StringUtils.hasText(req.getFlightNos())
                || !StringUtils.hasText(req.getPassengerNames()) || !StringUtils.hasText(req.getDepTimes())
                || !StringUtils.hasText(req.getArrTimes()) || !StringUtils.hasText(req.getTicketNos())
                || !StringUtils.hasText(req.getPnrs()) || !StringUtils.hasText(req.getCabins())
                || !StringUtils.hasText(req.getTicketPrices()) || !StringUtils.hasText(req.getTaxPrices())
                || !StringUtils.hasText(req.getTotalAmount()) || !StringUtils.hasText(req.getThirdPaymentNo())
                || !StringUtils.hasText(req.getUser()) || !StringUtils.hasText(req.getRefundType())
                || !StringUtils.hasText(req.getcTypes()) || !StringUtils.hasText(req.getcNos()) || !StringUtils.hasText(req.getOrderNo())
                || !StringUtils.hasText(req.getIssuedTime()) || !StringUtils.hasText(req.getPassengerTypes()) || !StringUtils.hasText(req.getRefundTime()) || !StringUtils.hasText(req.getHasInsurance())) {
            return false;
        }
        return true;
    }

    private boolean checkParamsUpload(InterRefundUploadReq req) {
        log.info("refundNo: " + req.getRefundNo() + ", imgUrlList: " + JSON.toJSONString(req.getImgUrlList()));
        if (!StringUtils.hasText(req.getRefundNo())
                || !StringUtils.hasText(req.getSign())
                || (req.getImgUrlList() == null || req.getImgUrlList().size() == 0)) {
            return false;
        }
        return true;
    }


    private boolean compareSign(InterRefundReq req) {
        Map<String, String> reParams = new TreeMap<String, String>(
                new Comparator<String>() {
                    public int compare(String obj1, String obj2) {
                        // 降序排序
                        return obj2.compareTo(obj1);
                    }
                });

        reParams.put("lines", req.getLines());
        reParams.put("arrTimes", req.getArrTimes());
        reParams.put("flightNos", req.getFlightNos());
        reParams.put("depTimes", req.getDepTimes());
        reParams.put("cNos", req.getcNos());
        reParams.put("cTypes", req.getcTypes());
        reParams.put("passengerNames", req.getPassengerNames());
        reParams.put("ticketNos", req.getTicketNos());
        reParams.put("pnrs", req.getPnrs());
        reParams.put("cabins", req.getCabins());
        reParams.put("ticketPrices", req.getTicketPrices());
        reParams.put("taxPrices", req.getTaxPrices());
        reParams.put("totalAmount", req.getTotalAmount());
        reParams.put("thirdPaymentNo", req.getThirdPaymentNo());
        reParams.put("user", req.getUser());
        reParams.put("remark", req.getRemark());
        reParams.put("refundType", req.getRefundType());
        reParams.put("reason", req.getReason());
        reParams.put("orderNo", req.getOrderNo());
        reParams.put("issuedTime", req.getIssuedTime());
        reParams.put("refundTime", req.getRefundTime());
        reParams.put("passengerTypes", req.getPassengerTypes());
        if (StringUtils.hasText(req.getMobileNo())) {
            reParams.put("mobileNo", req.getMobileNo());
        }
        if (StringUtils.hasText(req.getPayType())) {
            reParams.put("payType", req.getPayType());
        }
        if (StringUtils.hasText(req.getOfficeNo())) {
            reParams.put("officeNo", req.getOfficeNo());
        }
        if (StringUtils.hasText(req.getAllTaxDesc())) {
            reParams.put("allTaxDesc", req.getAllTaxDesc());
        }
        if (StringUtils.hasText(req.getPaymentNo())) {
            reParams.put("paymentNo", req.getPaymentNo());
        }
        if (StringUtils.hasText(req.getHasInsurance())) {
            reParams.put("hasInsurance", req.getHasInsurance());
        }

        String sign = MD5.generate(getSorftkey(reParams));
        return req.getSign().equals(sign);
    }

    private boolean compareSignUpload(InterRefundUploadReq req) {
        Map<String, String> reParams = new TreeMap<String, String>(
                new Comparator<String>() {
                    public int compare(String obj1, String obj2) {
                        // 降序排序
                        return obj2.compareTo(obj1);
                    }
                });

        reParams.put("refundNo", req.getRefundNo());
        String sign = MD5.generate(getSorftkey(reParams));
        return req.getSign().equals(sign);
    }

    private boolean compareSignSearch(InterRefundSearchReq req) {
        Map<String, String> reParams = new TreeMap<String, String>(
                new Comparator<String>() {
                    public int compare(String obj1, String obj2) {
                        // 降序排序
                        return obj2.compareTo(obj1);
                    }
                });

        reParams.put("refundNo", req.getRefundNo());
        reParams.put("time",req.getTime());

        String sign = MD5.generate(getSorftkey(reParams));
        return req.getSign().equals(sign);
    }

    private String getSorftkey(Map<String, String> KingRefundInfoMap) {
        String sign = "";
        Set<String> keySet = KingRefundInfoMap.keySet();
        Iterator<String> iter = keySet.iterator();
        while (iter.hasNext()) {
            String key = iter.next();
            sign = sign + KingRefundInfoMap.get(key);
        }
        log.info("sign:" + sign);

        sign = sign + "shouhangrefund";

        return sign;
    }


    /**
     * 从 ods esb 接口获取票面、机建燃油
     *
     * @param dtoTicketList
     * @param taxPrice
     * @param allTaxDescArray
     */
    private void handleFee4GJ(List<DTOTicket> dtoTicketList, String[] taxPrice, String[] allTaxDescArray) throws Exception {
        int ticketCount = dtoTicketList.size();
        // taxPrice = new String[ticketCount];
        // allTaxDescArray = new String[ticketCount];
        for (int i = 0; i < dtoTicketList.size(); i++) {
            DTOTicket dtoTicket = dtoTicketList.get(i);
            String issCode = dtoTicket.getIssCode();
            String ticketNo = dtoTicket.getTicketNo();
            List<EsbPaxTaxDetailVO.PaxTaxDetail> paxTaxDetailList = getPriceAndFeeFromEsb(issCode, ticketNo);
            String taxStr = "";
            for (EsbPaxTaxDetailVO.PaxTaxDetail paxTaxDetail : paxTaxDetailList) {
                if (StringUtils.hasText(paxTaxDetail.getTAX_CODE())) {
                    // 税费字符串格式，如： YQ:52.0,J9:15.0,YR:2400.0,CN:90.0,YP:150.0,PT:59.0
                    taxStr += paxTaxDetail.getTAX_CODE() + ":" + paxTaxDetail.getTAX_FEE() + ",";
                }
            }
            if (!"".equals(taxStr)) {
                allTaxDescArray[i] = taxStr;
            }
        }


        for (int i = 0; i < allTaxDescArray.length; i++) {
            String allTaxDesc = allTaxDescArray[i];
            BigDecimal allTaxFee = new BigDecimal(0);
            for (String taxKeyValue : allTaxDesc.split(",")) {
                if (StringUtils.hasText(taxKeyValue)) {
                    String[] keyValue = taxKeyValue.split(":");
                    if (keyValue.length == 2) {
                        allTaxFee = allTaxFee.add(new BigDecimal(keyValue[1]));  // 所有的税费和
                        log.info("allTaxFare-1:" + allTaxFee.intValue());
                    }
                }
            }
            taxPrice[i] = allTaxFee.toString();
        }
    }


    /**
     * 根据票号调用ESB接口获取票面、税费
     *
     * @param issCode
     * @param ticketNo
     * @return
     */
    private List<EsbPaxTaxDetailVO.PaxTaxDetail> getPriceAndFeeFromEsb(String issCode, String ticketNo) throws Exception {

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setOption("issCo", issCode);
        apiRequest.setOption("tno", ticketNo);

        ApiResponse apiResponse = null;
        try {
            log.info("ramsitfApi getPaxTaxDetail begin: " + issCode + "-" + ticketNo);
            apiResponse = ramsitfApi.getPaxTaxDetail(apiRequest);
        } catch (Exception e) {
            log.error("ramsitfApi getPaxTaxDetail error:" + e.getMessage());
            return new ArrayList<EsbPaxTaxDetailVO.PaxTaxDetail>();
        }
        log.info("ramsitfApi getPaxTaxDetail end: ");
        String responseStr = (String) apiResponse.getExtend().get("jsonStr");
        log.info(JSON.toJSONString(apiResponse));

//        String url = "http://esb.jdair.net/dubbo-consumer/getPaxTaxDetail";
//        SortedMap<Object, Object> params = new TreeMap<Object, Object>();
//        params.put("issCode", issCode);
//        params.put("ticketNo", ticketNo);
//        String responseStr = HttpInvokeClient.doGet(url, params);
//        JSONObject jsonObject = (JSONObject) JSON.parseObject(responseStr).get("extend");
//        responseStr = (String)jsonObject.get("jsonStr");
        EsbPaxTaxDetailVO response = JSONObject.parseObject(responseStr, EsbPaxTaxDetailVO.class);
        log.info("ramsitfApi getPaxTaxDetail is Ok | System.currentTimeMillis = " + System.currentTimeMillis() + " | response = " + JSON.toJSONString(response));

        if ("0".equals(response.getErrorCode())) {
            return response.getResult();
        } else {
            log.info("ramsitfApi getPaxTaxDetail return null: " + responseStr);
            return new ArrayList<EsbPaxTaxDetailVO.PaxTaxDetail>();
            //throw new Exception("数据获取失败");
        }
    }

    /**
     * @throws
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param msg
     * @param: @return
     * @return: JSONResult
     */
    public JSONResult getErrorJsonResult(StatusCode statusCode) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(statusCode.getCode(), statusCode.getMsg()));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

}