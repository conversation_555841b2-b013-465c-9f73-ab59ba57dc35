package net.jdair.specialrefund.bizz.controller.backend.domain;

import lombok.Data;
import net.jdair.specialrefund.bizz.controller.backend.page.PageDomain;

import java.util.List;

/**
 * 退票单列表查询请求
 */
@Data
public class SearchRefundOrderListReq extends PageDomain {

    private String refundNo;
    private List<String> orderSourceList;   // 订单来源, GJ、HWZ、TS、TSGJ
    private String refundDateStart;
    private String refundDateEnd;
    private String refundStatus;
    private String flightNo;
    private String flightDateStart;
    private String flightDateEnd;
    private String passengerName;
    private String issCode;
    private String ticketNo;
    private String depCode;
    private String arrCode;
    private String refundType;
    private String refundUserType;
    private String constraintReason; // 非自愿退票原因
    private String officeNo; // 中文国际票、海外站的订单来源标志

    // 小程序 openid
    private String openid;

    // 页码
    private int pageIndex;

    // 是否国际票
    private Boolean inter;

    private String orderSource;

    private String paymentNo;  // 退款支付流水号
}