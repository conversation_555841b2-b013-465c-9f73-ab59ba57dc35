package net.jdair.specialrefund.bizz.controller.frontend;

import com.alibaba.fastjson.JSON;
import com.alipay.util.SignatureHelper_return;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundPaymentMapper;
import net.jdair.specialrefund.bizz.mapper.EtPaymentMapper;
import net.jdair.specialrefund.bizz.utils.AmountUtils;
import net.jdair.specialrefund.bizz.utils.Base64Util;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.utils.XmlUtil;
import net.jdair.specialrefund.bizz.utils.email.EmailServiceUtil;
import net.jdair.specialrefund.bizz.utils.email.MD5Util;
import net.jdair.specialrefund.bizz.utils.pay.AlipayConfig;
import net.jdair.specialrefund.bizz.utils.pay.sdk.*;
import net.jdair.specialrefund.bizz.utils.sms.SmsServiceUtil;
import net.jdair.specialrefund.bizz.vo.DoAuditing;
import net.jdair.specialrefund.bizz.vo.PayStatus;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.security.Security;
import java.util.*;

/**
 * 在线退款回调接口
 */
@RestController
@Api(value = "在线退款回调接口")
@RequestMapping("/payment/")
@Slf4j
public class RefundCallbackController {

    @Autowired
    AlipayConfig alipayConfig;

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private WXPayConfigProperties wxPayConfigProperties;

    @Autowired
    private EmailServiceUtil emailServiceUtil;

    @Autowired
    private SmsServiceUtil smsServiceUtil;


    /**
     * 国际票、海外站支付宝在线退款回调处理
     *
     * @return
     */
    @RequestMapping(value = "alipayRefundCallbackGJ", method = RequestMethod.POST)
    @Transactional
    public void alipayRefundCallbackGJ(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> alipayResult = new HashMap<String, String>();

        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
                valueStr = new String(valueStr.getBytes("ISO-8859-1"), "UTF-8"); //转utf-8
            }
            alipayResult.put(name, valueStr);
        }
        log.info("Alipay Callback parameters gj: " + alipayResult.toString());
        EtPayment payment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", alipayResult.get("batch_no")));
        EtFltRefundPayment etFltRefundPayment = etFltRefundPaymentMapper.selectOne(new QueryWrapper<EtFltRefundPayment>().eq("payment_id", payment.getId()));
        EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(etFltRefundPayment.getFltRefundId());
        EtPayment outsidePayment = etFltRefund.getEtOutsidePaymentList().get(0);
        try {
            AlipayConfig.Config config = null;
            if (etFltRefund.getOrderSource().equals("GJ") && outsidePayment.getPayTime().after(DateUtils.StringToDate(alipayConfig.getSource().get("GJNew").getEffectiveDate()))) {
                config = alipayConfig.getSource().get("GJNew");
            } else if (etFltRefund.getOrderSource().equals("GJ")) {
                config = alipayConfig.getSource().get("GJ");
            } else if (etFltRefund.getOrderSource().equals("HWZ")) {
                config = alipayConfig.getSource().get("HWZ");
            }
            if (config != null) {
                String mysign = SignatureHelper_return.sign(alipayResult, config.getMd5Key().trim());
                if (mysign.equals(alipayResult.get("sign"))) {
                    Map<String, Object> params = new HashMap<String, Object>();
                    if ("1".equals(alipayResult.get("success_num").trim())) {
                        // 交易成功
                        params.put(PayStatus.class.getName(), PayStatus.PAID);
                    } else if ("0".equals(alipayResult.get("success_num").trim())) {
                        // 进行了退款交易，但处理失败。
                        params.put(PayStatus.class.getName(), PayStatus.FAIL);
                    }
                    params.put("paymentNo", alipayResult.get("batch_no"));
                    params.put(EtFltRefund.class.getName(), etFltRefund);
                    this.processBizz(params);
                    XmlUtil.responseText(response, "success");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 国际票、海外站微信在线退款回调处理
     *
     * @return
     */
    @RequestMapping(value = "backendCallBackPayWXpay", method = RequestMethod.POST)
    @Transactional
    public void backendCallBackPayWXpay(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("backendCallBackPayWXpay start");
        PayStatus returnResult = PayStatus.FAIL;
        //读取参数
        InputStream inputStream;
        StringBuffer sb = new StringBuffer();
        inputStream = request.getInputStream();
        String s;
        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        while ((s = in.readLine()) != null) {
            sb.append(s);
        }
        in.close();
        inputStream.close();
        log.info("sb str:" + sb.toString());
        //解析xml成map
        Map<String, String> map = WXPayUtil.xmlToMap(sb.toString());
        //过滤空 设置 TreeMap
        SortedMap<Object, Object> packageParams = new TreeMap<Object, Object>();
        Iterator it = map.keySet().iterator();
        while (it.hasNext()) {
            String parameter = (String) it.next();
            String parameterValue = map.get(parameter);
            String v = "";
            if (null != parameterValue) {
                v = parameterValue.trim();
            }
            packageParams.put(parameter, v);
        }
        log.info(JSON.toJSONString(packageParams));
        String res = (String) packageParams.get("return_code");
        String paymentNo = "";
        String resXml = "";

        //判断返回结果是否SUCCESS
        if (res.equals("SUCCESS")) {
            String req_info=(String)packageParams.get("req_info");
            String tradeInfoMsg = null;
            try {
                tradeInfoMsg = decryptData(req_info);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (tradeInfoMsg == null)  {
                log.info("通知签名验证失败");
                log.info("支付失败,错误信息：" + packageParams.get("err_code"));
                resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"
                        + "<return_msg><![CDATA[返回结果:FAIL]]></return_msg>" + "</xml> ";
                BufferedOutputStream out = new BufferedOutputStream(
                        response.getOutputStream());
                out.write(resXml.getBytes());
                out.flush();
                out.close();
                return;
            }
            Map<String, String> tradeInfoMsgMap = WXPayUtil.xmlToMap(tradeInfoMsg);
            paymentNo = tradeInfoMsgMap.get("out_refund_no");

            EtPayment etPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", paymentNo));
            EtFltRefundPayment etFltRefundPayment = etFltRefundPaymentMapper.selectOne(new QueryWrapper<EtFltRefundPayment>().eq("payment_id", etPayment.getId()));
            EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(etFltRefundPayment.getFltRefundId());

            try {
                WXPayConfig config = null;

                if (etFltRefund.getOrderSource().equals("GJ")) {
                    config = wxPayConfigProperties.getSource().get("GJ");
                } else if (etFltRefund.getOrderSource().equals("HWZ")) {
                    config = wxPayConfigProperties.getSource().get("HWZ");
                }

                if (config == null) {
                    return;
                }

                WXPay wxpay = new WXPay(config);
                //查询订单
                HashMap<String, String> data = new HashMap<String, String>();
                data.put("out_trade_no", tradeInfoMsgMap.get("out_trade_no"));
                Map<String, String> m = wxpay.orderQuery(data);
                // 账号信息
                String key = config.getKey(); // key
                log.info("order query info：" + WXPayUtil.mapToXml(m));
                //判断签名是否正确
                if (WXPayUtil.isSignatureValid(m, key, WXPayConstants.SignType.HMACSHA256)) {
                    log.info("backendCallBackPayWXpay sign ok.paymentNo:" + etPayment.getPaymentNo() + ",paymentPayStatus:" + etPayment.getPayStatus());
                    //交易状态
                    String tradeState = m.get("trade_state");
                    //支付金额
                    String totalFee = m.get("total_fee");
                    //业务结果
                    String result = (String) m.get("result_code");
                    //总金额
                    String paymentAmountStr = AmountUtils.convertBigDecimalToAmountStr(etPayment.getAmount()).replace(".", "");

                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("paymentNo", etPayment.getPaymentNo());

                    if ("SUCCESS".equals(result) && "REFUND".equals(tradeState) && paymentAmountStr.equals(totalFee)) {
                        params.put(EtFltRefund.class.getName(), etFltRefund);
                        if (etPayment.getPayStatus().equals(PayStatus.PENDING.toString()) || etPayment.getPayStatus().equals(PayStatus.PAID.toString())) {
                            params.put(PayStatus.class.getName(), PayStatus.PAID);
                        }
                        //通知微信.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.
                        resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"
                                + "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";
                        this.processBizz(params);
                    } else {
                        params.put(PayStatus.class.getName(), PayStatus.FAIL);
                        log.error("订单号:" + etPayment.getPaymentNo() + "支付失败, result:" + result + ", tradeState:" + tradeState + ",totalFee:" + totalFee);
                    }
                } else {
                    log.error("签名验证失败");
                }
            } catch (Exception e) {
                log.error("backendCallBackPayWXpay is error,paymentNo:" + etPayment.getPaymentNo() + ", errorMsg:" + e.getMessage());
                e.printStackTrace();
            }
        } else {
            resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"
                    + "<return_msg><![CDATA[" + returnResult + "]]></return_msg>" + "</xml> ";
        }
        log.info("paymentNo:" + paymentNo + ", send to wx info: " + resXml);

        BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
        out.write(resXml.getBytes());
        out.flush();
        out.close();
    }



    /**
     * 特殊退票易生代付在线退款回调处理
     *
     * @return
     */
    @RequestMapping(value = "paymentEasycardDFRefundCallBackAction", method = RequestMethod.POST)
    @Transactional
    public void paymentEasycardDFRefundCallBackAction(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("------EasycardDF Refund Callback Start-------");
        Map<String, String> easycardResult = new HashMap<String, String>();

        Map requestParams = request.getParameterMap();
        String name;
        String[] values;
        String valueStr;
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            name = (String) iter.next();
            if (name != null) {
                values = (String[]) requestParams.get(name);
                valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i]
                            : valueStr + values[i] + ",";
                }
                easycardResult.put(name, valueStr);
            }
        }

        String jsonResponse = JSON.toJSONString(easycardResult);
        log.info("EasycardDF Refund Callback params: " + jsonResponse);

        boolean callbackResult = false;
        try {
            String refundPaymentNo = JSON.parseObject(easycardResult.get("biz_content")).getString("out_trade_no");

            callbackResult = flightRefundServiceHelper.refundCallbackEasycardDF(jsonResponse, false, refundPaymentNo);

            log.info(refundPaymentNo + ": EasycardDF Refund Callback callbackResult:" + callbackResult);
            if (callbackResult) {
                XmlUtil.responseText(response, "SUCCESS");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw e;
        }
    }



    private void processBizz(Map<String, Object> params) throws Exception {
        String paymentNo = (String) params.get("paymentNo");
        PayStatus payStatus = (PayStatus) params.get(PayStatus.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) params.get(EtFltRefund.class.getName());
        //保存支付记录到数据库
        EtPayment payment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", paymentNo));
        if (null == payment) {
            log.info("Pay Call Back: Payment is not found. paymentNo is " + paymentNo);
            throw new Exception("Pay Call Back : Payment is not found. paymentNo is " + paymentNo);
        } else if (PayStatus.PAID.toString().equals(payment.getPayStatus()) || PayStatus.FAIL.toString().equals(payment.getPayStatus())) {
            log.info("Pay Call Back: Payment status has Been PAID. paymentNo is " + paymentNo);
            return;
        }
        log.info("refund no:" + etFltRefund.getRefundNo() + etFltRefund.getStatus());

        // 更新退票单状态
        this.updateRefundStatus(etFltRefund, payStatus.toString());
        if ("PAID".equals(payStatus.toString())) {
            if ("HWZ".equals(etFltRefund.getOrderSource())) {
                // 海外站退款成功后，给旅客发送邮件
                BigDecimal refundAmount = new BigDecimal(0);
                for (EtFltRefundPaxSeg fltRefundPaxSeg : etFltRefund.getEtFltRefundPaxSegList()) {
                    refundAmount = refundAmount.add(fltRefundPaxSeg.getActualRefundAmount());
                }
                emailServiceUtil.sendEmail4HWZ(etFltRefund.getUserName(), refundAmount.toString());
            } else if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                smsServiceUtil.sendToRefundSmsQueue(etFltRefund);
            }
        }

        if ("PAID".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus()) || "PENDICING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.PAID.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else if ("FAIL".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.FAIL.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else {
            throw new Exception("Pay Call Back: Success payment Status is fail by this process.");
        }

    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }


    private String decryptData(String base64Data) throws Exception {
        log.info("decryptData1:base64Data"+base64Data);
        Security.addProvider(new BouncyCastleProvider());
        log.info("decryptData2:");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding", "BC");
        log.info("decryptData3:cipher "+cipher);
        SecretKeySpec key = new SecretKeySpec(MD5Util.MD5Encode("d6e4ba9535e1b80e58885864be695b18", "UTF-8").toLowerCase().getBytes(), "AES");
        log.info("decryptData4:key "+key);
        cipher.init(Cipher.DECRYPT_MODE, key);
        String str = null;
        try {
            str = new String(cipher.doFinal(Base64Util.decode(base64Data)));
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        }
        log.info("decryptData:"+str);
        return str;
    }
}