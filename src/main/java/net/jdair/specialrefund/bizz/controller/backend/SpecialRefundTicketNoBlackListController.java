package net.jdair.specialrefund.bizz.controller.backend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchBlackListReq;
import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import net.jdair.specialrefund.bizz.controller.backend.page.TableDataInfo;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtTsTicketnoBlacklist;
import net.jdair.specialrefund.bizz.mapper.EtTsTicketnoBlacklistMapper;
import net.jdair.specialrefund.bizz.utils.PageUtil;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 特殊退票--后台管理  特殊退票票号黑名单
 */
@RestController
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundTicketNoBlackListController extends BaseController {

    @Autowired
    private EtTsTicketnoBlacklistMapper etTsTicketnoBlacklistMapper;

    /**
     * 条件查询
     */
    @PostMapping("/ticketNoBlacklist")
    @ResponseBody
    public RestResponse<TableDataInfo> list(@RequestBody SearchBlackListReq req) {
        List<EtTsTicketnoBlacklist> list = etTsTicketnoBlacklistMapper.selectList(new QueryWrapper<EtTsTicketnoBlacklist>()
                .eq(StringUtils.hasText(req.getTicketNo()), "ticket_no", req.getTicketNo())
                .eq(StringUtils.hasText(req.getStatus()), "status", req.getStatus())
        );

        PageUtil<EtTsTicketnoBlacklist> util = new PageUtil<>();
        PageParam<EtTsTicketnoBlacklist> page = util.getPage(req.getPageNum(), req.getPageSize(), list);
        TableDataInfo result = new TableDataInfo();
        result.setCode(0);
        result.setRows(page.getData());
        result.setTotal(page.getTotal());
        return RestResponse.ok(result);
    }

    /**
     * 获取
     * @param id
     * @return
     */
    @RequestMapping(value = "ticketNoBlackGetById", method = RequestMethod.GET)
    public RestResponse<EtTsTicketnoBlacklist> ticketNoBlackGetById(@RequestParam(name = "id") Long id) {
        return RestResponse.ok(etTsTicketnoBlacklistMapper.selectById(id));
    }

    /**
     * 插入
     * @param req
     * @return
     */
    @RequestMapping(value = "ticketNoBlackInsert", method = RequestMethod.POST)
    public RestResponse<Long> insert(@RequestBody EtTsTicketnoBlacklist req) {
        return RestResponse.ok(etTsTicketnoBlacklistMapper.insert(req));
    }

    /**
     * 批量插入
     * @param list
     * @return
     */
    @RequestMapping(value = "ticketNoBlackInsertList", method = RequestMethod.POST)
    public RestResponse<Long> insertList(@RequestBody List<EtTsTicketnoBlacklist> list) {
        for (EtTsTicketnoBlacklist etTsTicketnoBlacklist : list) {
            etTsTicketnoBlacklistMapper.insert(etTsTicketnoBlacklist);
        }
        return RestResponse.ok(1);
    }

    /**
     * 删除
     */
    @RequestMapping(value = "ticketNoBlackDelete", method = RequestMethod.GET)
    public RestResponse<Long> deleteById(@RequestParam(name = "id") Long id) {
        return RestResponse.ok(etTsTicketnoBlacklistMapper.deleteById(id));
    }

    /**
     * 修改
     */
    @RequestMapping(value = "ticketNoBlackUpdate", method = RequestMethod.POST)
    public RestResponse<Long> updateById(@RequestBody EtTsTicketnoBlacklist req) {
        return RestResponse.ok(etTsTicketnoBlacklistMapper.updateById(req));
    }



}
