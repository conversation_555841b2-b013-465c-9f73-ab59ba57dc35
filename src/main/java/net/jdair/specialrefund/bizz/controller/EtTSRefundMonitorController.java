package net.jdair.specialrefund.bizz.controller;

import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag;
import net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord;
import net.jdair.specialrefund.bizz.mapper.EtTsMultiRefundTagMapper;
import net.jdair.specialrefund.bizz.mapper.EtTsRefundMonitorMapper;
import net.jdair.specialrefund.bizz.mapper.EtTsRefundMonitorRecordMapper;
import net.jdair.specialrefund.bizz.utils.email.EmailServiceUtil;
import net.jdair.specialrefund.bizz.utils.email.TsEmailConfig;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ET 特殊退票退款监控预警
 */
@RestController
@RequestMapping("/bizz/etTSRefund/monitor")
@Slf4j
public class EtTSRefundMonitorController extends BaseController {

    @Autowired
    private EtTsRefundMonitorMapper etTSRefundMonitorMapper;

    @Autowired
    private EtTsRefundMonitorRecordMapper etTSRefundMonitorRecordMapper;

    @Autowired
    private EtTsMultiRefundTagMapper etTsMultiRefundTagMapper;

    @Autowired
    private EmailServiceUtil emailServiceUtil;

    @Autowired
    private TsEmailConfig tsEmailConfig;

    /**
     * 以下情况发送预警邮件
     * 1.同一个订单产生二次退款
     * 2.同一个金额、同一收款账号
     */
    @GetMapping("/alert")
    @ResponseBody
    public RestResponse<String> alert() {
        EtTsRefundMonitorRecord record = etTSRefundMonitorRecordMapper.selectById(1);
        // 1.同一个订单产生二次退款
        List<EtTsRefundMonitorRecord> multiPaymentList = etTSRefundMonitorMapper.queryByMultiPayment(record.getPreMaxPaymentId());

        Long maxPaymentId = record.getPreMaxPaymentId();

        /**
         * 收件人邮箱
         */
        String receiver = tsEmailConfig.getReceiver();
        if (multiPaymentList != null && multiPaymentList.size() > 0) {
            emailServiceUtil.sendEmail4TSRefundMonitor1(receiver, multiPaymentList);
            record.setPreMaxPaymentId(etTSRefundMonitorMapper.queryMaxPaymentId());
            record.setUpdateTime(new Date());
            etTSRefundMonitorRecordMapper.updateById(record);

        } else {
            // 2.同一个金额、同一收款账号
//            List<EtTsRefundMonitorRecord> sameCardAndAmountList = etTSRefundMonitorMapper.queryBySameCardAndAmount(record.getPreMaxPaymentId());
//            if (sameCardAndAmountList != null && sameCardAndAmountList.size() > 0) {
//                emailServiceUtil.sendEmail4TSRefundMonitor2(receiver, sameCardAndAmountList);
//                record.setPreMaxPaymentId(etTSRefundMonitorMapper.queryMaxPaymentId());
//                record.setUpdateTime(new Date());
//                etTSRefundMonitorRecordMapper.updateById(record);
//            }
        }

        /**
         * 重复退款熔断
         * 出现重复退款时（票号、退单号、收款人、收款账号、金额相同），系统自动关闭退款功能。在排除重复退款前提下，财务可以通过后台菜单重启退款功能并记录操作人。
         */
        List<EtTsRefundMonitorRecord> allSameList = etTSRefundMonitorMapper.queryByAllSame(maxPaymentId);
        if (allSameList != null && allSameList.size() > 0) {
            log.info("监测到重复退款（{}），系统将自动关闭在线退款功能", allSameList.stream().map(EtTsRefundMonitorRecord::getRefundNo).collect(Collectors.joining(",")));
            // 熔断. 将开关状态改成禁用。
            EtTsMultiRefundTag etTsMultiRefundTag = new EtTsMultiRefundTag();
            etTsMultiRefundTag.setId(1);
            etTsMultiRefundTag.setOnlineRefundStatus("INACTIVE");
            etTsMultiRefundTag.setUpdateTime(new Date());
            etTsMultiRefundTagMapper.updateById(etTsMultiRefundTag);

            record.setPreMaxPaymentId(etTSRefundMonitorMapper.queryMaxPaymentId());
            record.setUpdateTime(new Date());
            etTSRefundMonitorRecordMapper.updateById(record);
        }


        return RestResponse.ok("success");
    }


    /**
     * 特殊退票在线退款功能状态查询
     */
    @GetMapping("/queryOnlineRefundTag")
    @ResponseBody
    public RestResponse<EtTsMultiRefundTag> queryOnlineRefundTag() {
        EtTsMultiRefundTag etTsMultiRefundTag = etTsMultiRefundTagMapper.selectById(1);
        return RestResponse.ok(etTsMultiRefundTag);
    }

    /**
     * 启用特殊退票在线退款功能
     */
    @PostMapping("/openOnlineRefundTag")
    @ResponseBody
    public RestResponse<String> openOnlineRefundTag() {
        EtTsMultiRefundTag etTsMultiRefundTag = new EtTsMultiRefundTag();
        etTsMultiRefundTag.setId(1);
        etTsMultiRefundTag.setOnlineRefundStatus("ACTIVE");
        etTsMultiRefundTag.setUpdateTime(new Date());
        etTsMultiRefundTagMapper.updateById(etTsMultiRefundTag);

        return RestResponse.ok("success");
    }

}

