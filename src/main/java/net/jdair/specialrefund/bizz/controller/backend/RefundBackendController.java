package net.jdair.specialrefund.bizz.controller.backend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.controller.backend.domain.FirstAuditReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.RefundAmountReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SecondAuditReq;
import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import net.jdair.specialrefund.bizz.controller.backend.page.TableDataInfo;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtRefundGjImg;
import net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag;
import net.jdair.specialrefund.bizz.domain.InterMultiRefundTag;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.mapper.EtTsMultiRefundTagMapper;
import net.jdair.specialrefund.bizz.mapper.InterMultiRefundTagMapper;
import net.jdair.specialrefund.bizz.service.EtRefundGjImgService;
import net.jdair.specialrefund.bizz.utils.PageUtil;
import net.jdair.specialrefund.bizz.vo.SpecialRefundException;
import net.jdair.specialrefund.bizz.vo.StatusCode;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 管理后台--国际票、海外站退票单相关接口
 */
@RestController
@Api(value = "管理后台--国际票、海外站退票单相关接口")
@RequestMapping("/bizz/backend/interRefund")
@Slf4j
public class RefundBackendController extends BaseController {

    @Autowired
    private EtRefundGjImgService etRefundGjImgService;

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private InterMultiRefundTagMapper interMultiRefundTagMapper;


    /**
     * 条件查询退票单列表
     */
    @PostMapping("/list")
    @ResponseBody
    public RestResponse<TableDataInfo> list(@RequestBody SearchRefundOrderListReq req) {
        Page page = new Page();
        page.setCurrent(req.getPageNum());
        page.setSize(req.getPageSize());
        List<OrderItem> orders = new ArrayList<>();
        orders.add(new OrderItem("r.id", false));
        page.setOrders(orders);
        IPage<EtFltRefund> list = flightRefundServiceHelper.findRefundByParamsForPage(page, req);
        TableDataInfo result = new TableDataInfo();
        result.setCode(0);
        result.setRows(list.getRecords());
        result.setTotal(list.getTotal());
        return RestResponse.ok(result);
    }

    /**
     * 根据ID查询退票单明细
     */
    @GetMapping("/get")
    @ResponseBody
    public RestResponse<EtFltRefund> get(@RequestParam(name = "id") Long id) {
        EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(id);
        return RestResponse.ok(etFltRefund);
    }

    /**
     * 按退票单号查询附件图片
     */
    @GetMapping("/findImgByRefundNo")
    @ResponseBody
    public RestResponse<List<EtRefundGjImg>> findImgByRefundNo(@RequestParam(name = "refundNo") String refundNo) {
        List<EtRefundGjImg> etRefundGjImgList = etRefundGjImgService.getBaseMapper().selectList(new QueryWrapper<EtRefundGjImg>().eq("refund_no", refundNo));
        return RestResponse.ok(etRefundGjImgList);
    }

    /**
     * 一审通过
     */
    @PostMapping("/firstAuditPass")
    @ResponseBody
    public RestResponse<String> firstAuditPass(@RequestBody FirstAuditReq req) {
        flightRefundServiceHelper.firstAuditPass(req);
        return RestResponse.ok("success");
    }

    /**
     * 一审拒绝
     */
    @PostMapping("/firstAuditReject")
    @ResponseBody
    public RestResponse<String> firstAuditReject(@RequestBody FirstAuditReq req) {
        flightRefundServiceHelper.firstAuditReject(req);
        return RestResponse.ok("success");
    }

    /**
     * 二审通过
     */
    @PostMapping("/secondAuditPass")
    @ResponseBody
    public RestResponse<String> secondAuditPass(@RequestBody SecondAuditReq req) {
        flightRefundServiceHelper.secondAuditPass(req);
        return RestResponse.ok("success");
    }

    /**
     * 二审拒绝
     */
    @PostMapping("/secondAuditReject")
    @ResponseBody
    public RestResponse<String> secondAuditReject(@RequestBody SecondAuditReq req) {
        flightRefundServiceHelper.secondAuditReject(req);
        return RestResponse.ok("success");
    }

    /**
     * 线下退款
     */
    @PostMapping("/refundAmountOffline")
    @ResponseBody
    public RestResponse<String> refundAmountOffline(@RequestBody RefundAmountReq req) {
        flightRefundServiceHelper.refundAmountOffline(req);
        return RestResponse.ok("success");
    }

    /**
     * 在线退款
     */
    @PostMapping("/refundAmountOnline")
    @ResponseBody
    public RestResponse<String> refundAmountOnline(@RequestBody RefundAmountReq req) {
        InterMultiRefundTag interMultiRefundTag = interMultiRefundTagMapper.selectById(1);
        if (!"ACTIVE".equals(interMultiRefundTag.getOnlineRefundStatus())) {
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getCode());
        }
        flightRefundServiceHelper.refundAmountOnline(req);
        return RestResponse.ok("success");
    }

    /**
     * 国际票含随票保险支付宝在线退款的定时任务，查询退款状态,退款成功后处理业务逻辑
     */
    @PostMapping("/alipayRefundQuery")
    @ResponseBody
    public RestResponse<String> alipayRefundQuery() {
        flightRefundServiceHelper.alipayRefundQuery();
        return RestResponse.ok("success");
    }

}

