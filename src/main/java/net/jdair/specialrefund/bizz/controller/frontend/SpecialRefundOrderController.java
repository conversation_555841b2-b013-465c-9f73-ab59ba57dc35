package net.jdair.specialrefund.bizz.controller.frontend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import net.jdair.specialrefund.bizz.controller.backend.page.TableDataInfo;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.helper.FlightRefundServiceHelper;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialServiceHelper;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.*;
import net.jdair.specialrefund.bizz.utils.file.service.FileService;
import net.jdair.specialrefund.bizz.utils.file.vo.OssFileInfo;
import net.jdair.specialrefund.bizz.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 特殊退票退款服务接口
 */
@RestController
@Api(value = "et_order 服务接口")
@RequestMapping("miniapp/specialrefund/")
@Slf4j
public class SpecialRefundOrderController {

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private FlightRefundSpecialServiceHelper flightRefundSpecialServiceHelper;

    @Autowired
    private BaseInfoUtils baseInfoUtils;

    @Autowired
    private FileService fileService;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private CustomerMapper customerMapper;


    /**
     * @Fields jsonResult : API接口封装类
     */
    protected JSONResult jsonResult = new JSONResult();


    /**
     * 退票单列表查询接口
     *
     * @return
     */
    @RequestMapping(value = "refundOrderList", method = RequestMethod.POST)
    public JSONResult refundOrderList(@RequestBody RefundOrderListReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParamsForList(req)) {
                return jsonResult;
            }

            SearchRefundOrderListReq request = new SearchRefundOrderListReq();

            request.setOrderSource("TS");
            if (req.getInter() != null && req.getInter()) {
                request.setOrderSource("TSGJ");
            }
            request.setOpenid(req.getOpenid());
            request.setPageSize(req.getPageSize());
            request.setPageNum(req.getPageIndex());

            Page page = new Page();
            page.setCurrent(request.getPageNum());
            page.setSize(request.getPageSize());
            List<OrderItem> orders = new ArrayList<>();
            orders.add(new OrderItem("r.id", false));
            page.setOrders(orders);
            IPage<EtFltRefund> list = flightRefundServiceHelper.findRefundByParamsForPage(page, request);

            TableDataInfo result = new TableDataInfo();
            result.setCode(0);
            result.setRows(list.getRecords());
            result.setTotal(list.getTotal());
            //log.debug(JSON.toJSONString(list));

            Map<String, Object> data = new HashMap<String, Object>();

            data.put("refundOrderList", handleRefundOrder(req, result));

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 退票单明细接口
     * @param req
     * @return
     */
    @RequestMapping(value = "refundOrderDetail", method = RequestMethod.POST)
    public JSONResult refundOrderDetail(@RequestBody RefundOrderListReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParamsForDetail(req)) {
                return jsonResult;
            }

            SearchRefundOrderListReq request = new SearchRefundOrderListReq();

            request.setOrderSource("TS");
            if (req.getInter() != null && req.getInter()) {
                request.setOrderSource("TSGJ");
            }
            req.setPageIndex(1);
            req.setPageSize(10);
            request.setOpenid(req.getOpenid());
            request.setRefundNo(req.getRefundNo());
            request.setPageSize(req.getPageSize());
            request.setPageNum(req.getPageIndex());

            Page page = new Page();
            page.setCurrent(request.getPageNum());
            page.setSize(request.getPageSize());
            List<OrderItem> orders = new ArrayList<>();
            orders.add(new OrderItem("r.id", false));
            page.setOrders(orders);
            IPage<EtFltRefund> list = flightRefundServiceHelper.findRefundByParamsForPage(page, request);
            TableDataInfo result = new TableDataInfo();
            result.setCode(0);
            result.setRows(list.getRecords());
            result.setTotal(list.getTotal());

            Map<String, Object> data = new HashMap<String, Object>();
            RefundOrderVO refundOrderVO = handleRefundOrder(req, result);
            if (refundOrderVO.getRefundOrderList() == null || refundOrderVO.getRefundOrderList().size() == 0) {
                jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_ORDERDETAIL_ERROR);
                return jsonResult;
            }
            data.put("refundOrderDetail", refundOrderVO.getRefundOrderList().get(0));

            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 旅客银行卡号修改
     * @return
     */
    @RequestMapping(value = "changeBankCardNo", method = RequestMethod.POST)
    public JSONResult changeBankCardNo(@RequestBody RefundOrderListReq req) {
        jsonResult = new JSONResult();

        try {
            //查询参数合法性验证
            if (!validateParamsForChange(req)) {
                return jsonResult;
            }
            log.info("旅客修改银行卡号，特殊退票单号：" + req.getRefundNo() + ", 是否改成微信实名：" + req.isToRealName() +  ", 新银行卡号：" + req.getNewCardNo() + ", 新开户行名称：" + req.getNewBankName() + ", 新持卡人姓名：" + req.getNewCardHolder());
            flightRefundSpecialServiceHelper.changeBankCardNo(req.getRefundNo(), req.isToRealName(), req.getNewCardNo(), req.getNewBankName(), req.getNewCardHolder());

            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException se) {
            jsonResult = getErrorJsonResult(se.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 小程序用户上传图片
     *
     * @return
     */
    @RequestMapping(value = "uploadImage", method = RequestMethod.POST)
    public JSONResult uploadImage(HttpServletRequest request) {
        String bucketName = "special-refund";
        //获取文件需要上传到的路径
        MultipartHttpServletRequest req =(MultipartHttpServletRequest)request;
        MultipartFile file =  req.getFile("file");
        jsonResult = new JSONResult();

        try {
            String originalFileName = file.getOriginalFilename();
            String fileType = originalFileName.substring(Objects.requireNonNull(originalFileName).lastIndexOf(".") + 1);
            if (!"png,jpg,jpeg,bmp,gif".contains(fileType)) {
                throw new SpecialRefundException("请上传jpg、jpeg、bmp、gif、png格式文件！");
            }
            String preName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
            if (org.apache.commons.lang3.StringUtils.isBlank(preName)) {
                throw new SpecialRefundException("文件名不能为空。");
            }
            OssFileInfo ossFileInfo = fileService.ossUpload(file, bucketName);

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("fileName", ossFileInfo.getFileName());
            data.put("filePath", ossFileInfo.getFilePath());
            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException se) {
            jsonResult = getErrorJsonResult(se.getMessage());
        } catch (Exception e) {
            log.error("附件上传处理异常：" + e);
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * 获取小程序用户上传图片的预览地址
     *
     * @return
     */
    @RequestMapping(value = "getImagePreviewUrl", method = RequestMethod.POST)
    public JSONResult getImagePreviewUrl(@RequestBody List<String> filePathList) {
        String bucketName = "special-refund";
        jsonResult = new JSONResult();

        try {
            List<String> previewUrlList = new ArrayList<>();
            for (String filePath : filePathList) {
                String previewUrl = fileService.getPreviewUrl(filePath, bucketName);
                previewUrlList.add(previewUrl);
            }


            Map<String, Object> data = new HashMap<String, Object>();
            data.put("previewUrlList", previewUrlList);
            jsonResult.setData(data);
            jsonResult.setStatus(StatusCode.SUCCESS.getMsg());
        } catch (SpecialRefundException se) {
            jsonResult = getErrorJsonResult(se.getMessage());
        } catch (Exception e) {
            log.error("获取预览地址异常：" + e);
            e.printStackTrace();
            jsonResult = getErrorJsonResult(StatusCode.MARS_ERROR);
        }

        return jsonResult;
    }


    /**
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsForList(RefundOrderListReq req) {
        Map<String, Object> params = new HashMap<String, Object>();

        params.put("openid", req.getOpenid());
        params.put("pageIndex", req.getPageIndex());
        params.put("pageSize", req.getPageSize());

        if (req.getInter() != null) {
            params.put("inter", req.getInter());
        }



        // 验证 openid
        if(!StringUtils.hasText(req.getOpenid())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_OPENID_NULL);
            return false;
        }

        // 验证 pageIndex
        if(!ValidateUtils.isValidByRegex(String.valueOf(req.getPageIndex()), "^[1-9]d*|0$")) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_PAGE_INDEX_ERROR);
            return false;
        }

        // 验证 pageSize
        if(!ValidateUtils.isValidByRegex(String.valueOf(req.getPageSize()), "^[1-9]d*$") && req.getPageSize() > 100) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_PAGE_INDEX_ERROR);
            return false;
        }

        return true;
    }

    /**
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsForDetail(RefundOrderListReq req) {

        Map<String, Object> params = new HashMap<String, Object>();

        params.put("openid", req.getOpenid());
        params.put("pageIndex", req.getPageIndex());
        params.put("pageSize", req.getPageSize());
        params.put("refundNo", req.getRefundNo());

        if (req.getInter() != null) {
            params.put("inter", req.getInter());
        }



        // 验证 openid
        if(!StringUtils.hasText(req.getOpenid())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_OPENID_NULL);
            return false;
        }

        // 验证 refundNo
        if (!StringUtils.hasText(req.getRefundNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getRefundNo(), ValidateUtils.NUMBER_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_ERROR);
            return false;
        }

        // 验证 pageIndex
        if(!ValidateUtils.isValidByRegex(String.valueOf(req.getPageIndex()), "^[1-9]d*|0$")) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_PAGE_INDEX_ERROR);
            return false;
        }

        // 验证 pageSize
        if(!ValidateUtils.isValidByRegex(String.valueOf(req.getPageSize()), "^[1-9]d*$") && req.getPageSize() > 100) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_PAGE_INDEX_ERROR);
            return false;
        }

        return true;
    }

    /**
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsForChange(RefundOrderListReq req) {

        Map<String, Object> params = new HashMap<String, Object>();

        params.put("openid", req.getOpenid());
        params.put("refundNo", req.getRefundNo());
        params.put("toRealName", req.isToRealName());
        params.put("newCardNo", req.getNewCardNo());
        params.put("newBankName", req.getNewBankName());
        params.put("newCardHolder", req.getNewCardHolder());



        // 验证 openid
        if(!StringUtils.hasText(req.getOpenid())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_OPENID_NULL);
            return false;
        }

        // 验证 refundNo
        if (!StringUtils.hasText(req.getRefundNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_NULL);
            return false;
        } else if (!ValidateUtils.isValidByRegex(req.getRefundNo(), ValidateUtils.NUMBER_REGEX)) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_REFUNDNO_ERROR);
            return false;
        }

        // 验证 newCardNo
        if(!StringUtils.hasText(req.getNewCardNo())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDNO_NULL);
            return false;
        }

        // 验证 newCardHolder
        if(!StringUtils.hasText(req.getNewCardHolder())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_CARDHOLDER_NULL);
            return false;
        }

        return true;
    }


    /**
     * @Description: 验证请求参数
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean validateParamsForUploadImage(SpecialRefundUploadImageReq req) {

        Map<String, Object> params = new HashMap<String, Object>();

        params.put("openid", req.getOpenid());



        // 验证 openid
        if(!StringUtils.hasText(req.getOpenid())) {
            jsonResult = getErrorJsonResult(StatusCode.SPECIAL_REFUND_OPENID_NULL);
            return false;
        }

        return true;
    }


    public RefundOrderVO handleRefundOrder(RefundOrderListReq req, TableDataInfo tableDataInfo) {
        RefundOrderVO refundOrderVO = new RefundOrderVO();
        refundOrderVO.setPageIndex(req.getPageIndex());
        refundOrderVO.setPageSize(req.getPageSize());
        refundOrderVO.setTotalSize((int) tableDataInfo.getTotal());

        List<RefundOrderVO.RefundOrder> refundOrderList = new ArrayList<RefundOrderVO.RefundOrder>();
        for (Object obj : tableDataInfo.getRows()) {
            EtFltRefund dtoFltRefund = (EtFltRefund) obj;
            RefundOrderVO.RefundOrder refundOrder = new RefundOrderVO.RefundOrder();

            refundOrder.setUserName(dtoFltRefund.getUserName());
            refundOrder.setRefundNo(dtoFltRefund.getRefundNo());
            refundOrder.setStatus(showStatus(dtoFltRefund.getStatus()));
            refundOrder.setCreateTime(DateUtils.convertOracleDate(dtoFltRefund.getCreateTime()));
            refundOrder.setRefundType(dtoFltRefund.getRefundType());
            refundOrder.setRefundReason(dtoFltRefund.getConstraintReason());
            refundOrder.setRefundMoneyTime(dtoFltRefund.getPayTime());
            if ("TSGJ".equals(dtoFltRefund.getOrderSource())) {
                refundOrder.setInter(true);
            } else {
                refundOrder.setInter(false);
            }

            EtSpecialRefundBankInfo etSpecialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(new QueryWrapper<EtSpecialRefundBankInfo>().eq("flt_refund_id", dtoFltRefund.getId()));

            RefundOrderVO.BankInfo bankInfo = new RefundOrderVO.BankInfo();
            bankInfo.setCardHolder(etSpecialRefundBankInfo.getCardHolder());
            bankInfo.setCardNo(etSpecialRefundBankInfo.getCardNo());
            bankInfo.setBankName(etSpecialRefundBankInfo.getBankName());
            bankInfo.setMobileNo(etSpecialRefundBankInfo.getMobileNo());

            refundOrder.setBankInfo(bankInfo);

            // 查询退票单下的所有审核记录
            List<EtFltRefundAudit> dtoRefundAuditList = etFltRefundAuditMapper.selectList(new QueryWrapper<EtFltRefundAudit>().eq("refund_id", dtoFltRefund.getId()));
            List<RefundOrderVO.AuditInfo> auditInfoList = new ArrayList<RefundOrderVO.AuditInfo>();

            Iterator<EtFltRefundAudit> iterator = dtoRefundAuditList.iterator();
            while (iterator.hasNext()) {
                EtFltRefundAudit dtoRefundAudit = iterator.next();
                if (!DoAuditing.FREJECT.getValue().equals(dtoRefundAudit.getAuditResult())
                        && !DoAuditing.NEW.getValue().equals(dtoRefundAudit.getAuditResult())
                        && StringUtils.hasText(dtoRefundAudit.getAuditResult())) {
                    iterator.remove();
                }
            }

            for (EtFltRefundAudit dtoRefundAudit : dtoRefundAuditList) {
                RefundOrderVO.AuditInfo auditInfo = new RefundOrderVO.AuditInfo();

                // 查询审核单下的所有图片
                List<EtSpecialRefundImg> dtoSpecialRefundImgList = etSpecialRefundImgMapper.selectList(new QueryWrapper<EtSpecialRefundImg>().eq("flt_refund_id", dtoFltRefund.getId()));
                if (dtoSpecialRefundImgList != null && dtoSpecialRefundImgList.size() > 0) {
                    List<String> imgUrlList = new ArrayList<String>();
                    for (EtSpecialRefundImg dtoSpecialRefundImg : dtoSpecialRefundImgList) {
                        if (SpecialRefundImgSource.USER.name().equals(dtoSpecialRefundImg.getSource()) && SpecialRefundImgStatus.ACTIVE.name().equals(dtoSpecialRefundImg.getStatus())) {
                            imgUrlList.add(dtoSpecialRefundImg.getImgUrl());
                        }
                    }
                    auditInfo.setImgUrlList(imgUrlList);
                }
                auditInfo.setCreateTime(dtoRefundAudit.getAuditTime() == null ? dtoRefundAudit.getSubmitTime() : dtoRefundAudit.getAuditTime());

                if (DoAuditing.FIRSTAUDIT.getValue().equals(dtoRefundAudit.getAction())
                        && dtoRefundAudit.getAuditUsername() != null
                        && DoAuditing.FREJECT.getValue().equals(dtoRefundAudit.getAuditResult())) {
                    // 一审拒绝，返回备注信息
                    auditInfo.setRejectRemark(dtoRefundAudit.getNotion());
                    auditInfoList.add(auditInfo);
                } else if (DoAuditing.NEW.getValue().equals(dtoRefundAudit.getAuditResult()) || !StringUtils.hasText(dtoRefundAudit.getAuditUsername())) {
                    auditInfo.setRemark(dtoRefundAudit.getNotion());
                    auditInfoList.add(auditInfo);
                }
            }

            // 按创建时间倒序排列
            Collections.sort(auditInfoList, new Comparator<RefundOrderVO.AuditInfo>() {
                @Override
                public int compare(RefundOrderVO.AuditInfo t1, RefundOrderVO.AuditInfo t2) {
                    return t2.getCreateTime().compareTo(t1.getCreateTime());
                }
            });

            refundOrder.setAuditInfoList(auditInfoList);

            List<RefundOrderVO.SegmentInfo> segmentInfoList = new ArrayList<RefundOrderVO.SegmentInfo>();

            // 航段排序
            Collections.sort(dtoFltRefund.getEtPassengerSegmentList(), new Comparator<EtPassengerSegment>() {
                @Override
                public int compare(EtPassengerSegment ps1, EtPassengerSegment ps2) {
                    return ps1.getSequence() - ps2.getSequence();
                }
            });

            double totalRefundAmount = 0;
            for (EtPassengerSegment etPassengerSegment : dtoFltRefund.getEtPassengerSegmentList()) {
                EtTicket etTicket = etTicketMapper.selectById(etPassengerSegment.getTicketId());
                RefundOrderVO.SegmentInfo segmentInfo = new RefundOrderVO.SegmentInfo();
                segmentInfo.setDepCode(etPassengerSegment.getEtSegment().getDepCode());
                segmentInfo.setArrCode(etPassengerSegment.getEtSegment().getArrCode());
                segmentInfo.setDepCityCN(baseInfoUtils.getCityChiName(etPassengerSegment.getEtSegment().getDepCode()));
                segmentInfo.setArrCityCN(baseInfoUtils.getCityChiName(etPassengerSegment.getEtSegment().getArrCode()));
                segmentInfo.setFlightDate(DateUtils.dateToString(etPassengerSegment.getEtSegment().getDepTime()));
                segmentInfo.setFlightNo(etPassengerSegment.getEtSegment().getFlightNo());
                segmentInfo.setCabin(etPassengerSegment.getEtSegment().getCabinClass());
                segmentInfo.setTicketNo(etTicket.getTicketNo());
                for (EtFltRefundPaxSeg etFltRefundPaxSeg : dtoFltRefund.getEtFltRefundPaxSegList()) {
                    if (etFltRefundPaxSeg.getPaxSegId().equals(etPassengerSegment.getId())) {
                        segmentInfo.setRefundAmount(etFltRefundPaxSeg.getActualRefundAmount().doubleValue());
                    }
                }
                segmentInfoList.add(segmentInfo);
                totalRefundAmount += segmentInfo.getRefundAmount();
            }

            for (EtPassengerSegment etPassengerSegment : dtoFltRefund.getEtPassengerSegmentList()) {
                EtPassenger etPassenger = etPassengerMapper.selectById(etPassengerSegment.getPassengerId());
                Customer customer = customerMapper.selectById(etPassenger.getCustomerId());
                RefundOrderVO.PassengerInfo passengerInfo = new RefundOrderVO.PassengerInfo();
                passengerInfo.setPassengerName(customer.getName());
                passengerInfo.setPassengerType(PassengerType.valueOf(etPassenger.getPassengerType()));
                refundOrder.setPassengerInfo(passengerInfo);
            }

            refundOrder.setSegmentInfoList(segmentInfoList);

            refundOrder.setTotalRefundAmount(totalRefundAmount);

            refundOrderList.add(refundOrder);
        }

        refundOrderVO.setRefundOrderList(refundOrderList);

        return refundOrderVO;
    }


    /**
     * 审核状态转换
     * @param originStatus
     * @return
     */
    private String showStatus(String originStatus) {
        if (DoAuditing.NEW.getValue().equals(originStatus)
                || DoAuditing.FPASS.getValue().equals(originStatus)
                || DoAuditing.REJECT.getValue().equals(originStatus)) {
            return "审核中";
        } else if (DoAuditing.PASS.getValue().equals(originStatus)
                || DoAuditing.PEND.getValue().equals(originStatus)
                || DoAuditing.WAIT_OFFLINE.getValue().equals(originStatus)
                || DoAuditing.FAIL.getValue().equals(originStatus)) {  // 二审通过、退款处理中、退款失败、待线下退款
            return "审核通过";
        } else if (DoAuditing.FREJECT.getValue().equals(originStatus)) {  // 一审拒绝
            return "审核拒绝";
        } else if (DoAuditing.PAID.getValue().equals(originStatus)) {
            return "已退款";
        } else if (DoAuditing.CLOSED.getValue().equals(originStatus)) {
            return "已关闭";
        }
        return "审核中";
    }


    /**
     * @throws
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param msg
     * @param: @return
     * @return: JSONResult
     */
    public JSONResult getErrorJsonResult(StatusCode statusCode) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(statusCode.getCode(), statusCode.getMsg()));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

    /**
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param errorMessage
     * @return: JSONResult
     * @throws
     */
    public JSONResult getErrorJsonResult(String errorMessage) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage("-1", errorMessage));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }
}