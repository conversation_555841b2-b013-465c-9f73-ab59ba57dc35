package net.jdair.specialrefund.bizz.controller;

import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.InterMultiRefundTag;
import net.jdair.specialrefund.bizz.domain.InterRefundMonitorRecord;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.email.EmailServiceUtil;
import net.jdair.specialrefund.bizz.utils.email.TsEmailConfig;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 国际票海外站 退票退款监控预警
 */
@RestController
@RequestMapping("/bizz/interRefund/monitor")
@Slf4j
public class InterRefundMonitorController extends BaseController {

    @Autowired
    private InterRefundMonitorMapper interRefundMonitorMapper;

    @Autowired
    private InterRefundMonitorRecordMapper interRefundMonitorRecordMapper;

    @Autowired
    private InterMultiRefundTagMapper interMultiRefundTagMapper;

    @Autowired
    private EmailServiceUtil emailServiceUtil;

    @Autowired
    private TsEmailConfig tsEmailConfig;

    /**
     * 以下情况发送预警邮件
     */
    @GetMapping("/alert")
    @ResponseBody
    public RestResponse<String> alert() {
        InterRefundMonitorRecord record = interRefundMonitorRecordMapper.selectById(1);
        // 1.同一个订单产生二次退款
        List<InterRefundMonitorRecord> multiPaymentList = interRefundMonitorMapper.queryByMultiPayment(record.getPreMaxPaymentId());

        /**
         * 收件人邮箱
         */
        String receiver = tsEmailConfig.getReceiver();
        if (multiPaymentList != null && multiPaymentList.size() > 0) {
            emailServiceUtil.sendEmail4InterRefundMonitor(receiver, multiPaymentList);
            /**
             * 重复退款熔断
             * 出现重复退款时,系统自动关闭退款功能。在排除重复退款前提下，财务可以通过后台菜单重启退款功能并记录操作人。
             */
            log.info("监测到重复退款（{}），系统将自动关闭在线退款功能", multiPaymentList.stream().map(InterRefundMonitorRecord::getRefundNo).collect(Collectors.joining(",")));
            // 熔断. 将开关状态改成禁用。
            InterMultiRefundTag interMultiRefundTag = new InterMultiRefundTag();
            interMultiRefundTag.setId(1);
            interMultiRefundTag.setOnlineRefundStatus("INACTIVE");
            interMultiRefundTag.setUpdateTime(new Date());
            interMultiRefundTagMapper.updateById(interMultiRefundTag);

            record.setPreMaxPaymentId(interRefundMonitorMapper.queryMaxPaymentId());
            record.setUpdateTime(new Date());
            interRefundMonitorRecordMapper.updateById(record);

        }

        return RestResponse.ok("success");
    }


    /**
     * 国际票海外站在线退款功能状态查询
     */
    @GetMapping("/queryOnlineRefundTag")
    @ResponseBody
    public RestResponse<InterMultiRefundTag> queryOnlineRefundTag() {
        InterMultiRefundTag interMultiRefundTag = interMultiRefundTagMapper.selectById(1);
        return RestResponse.ok(interMultiRefundTag);
    }

    /**
     * 启用国际票海外站在线退款功能
     */
    @PostMapping("/openOnlineRefundTag")
    @ResponseBody
    public RestResponse<String> openOnlineRefundTag() {
        InterMultiRefundTag interMultiRefundTag = new InterMultiRefundTag();
        interMultiRefundTag.setId(1);
        interMultiRefundTag.setOnlineRefundStatus("ACTIVE");
        interMultiRefundTag.setUpdateTime(new Date());
        interMultiRefundTagMapper.updateById(interMultiRefundTag);

        return RestResponse.ok("success");
    }

}

