package net.jdair.specialrefund.bizz.controller.backend;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.BaseController;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.helper.FlightRefundSpecialHisDataSyncHelper;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.vo.SpecialRefundException;
import net.jdair.specialrefund.bizz.vo.SpecialRefundHisDataSyncBackendReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 特殊退票--后台管理, PGS 历史数据同步接口
 */
@RestController
@RequestMapping("/backend/miniapp/specialrefund")
@Slf4j
public class SpecialRefundHisDataSyncController extends BaseController {

    @Autowired
    private EtTsPGSMapper etTsPGSMapper;
    @Autowired
    private EtFltRefundMapper etFltRefundMapper;
    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;
    @Autowired
    private EtPaymentMapper etPaymentMapper;
    @Autowired
    private EtFltRefundOutsidePaymentMapper etFltRefundOutsidePaymentMapper;
    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private EtPnrMapper etPnrMapper;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    @Autowired
    private EtSegmentMapper etSegmentMapper;

    @Autowired
    private EtPassengerSegmentMapper etPassengerSegmentMapper;

    @Autowired
    private EtSpecialRefundOriginTktMapper etSpecialRefundOriginTktMapper;

    @Autowired
    private EtFltRefundPaxSegMapper etFltRefundPaxSegMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtProductMapper etProductMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private FlightRefundSpecialHisDataSyncHelper flightRefundSpecialHisDataSyncHelper;


    /**
     * 历史数据同步
     *
     * @return
     */
    // @RequestMapping(value = "historyDataSync", method = RequestMethod.POST)
    public void historyDataSync(@RequestBody SpecialRefundHisDataSyncBackendReq req) {
        try {
            log.info("pgs history sync, req: {}", JSON.toJSONString(req));
            List<EtFltRefund> etFltRefundList = etTsPGSMapper.findRefundByTime(req.getCreateTimeStart(), req.getCreateTimeEnd());
            for (EtFltRefund refund : etFltRefundList) {
                try {
                    EtFltRefund exist = etFltRefundMapper.selectOne(new QueryWrapper<EtFltRefund>().eq("refund_no", refund.getRefundNo()));
                    if (exist == null) {
                            String refundNo = refund.getRefundNo();
                            log.info("ts history data sync: " + refundNo);
                            EtFltRefund etFltRefund = etTsPGSMapper.findRefundByRefundNo(refundNo);
                            List<EtFltRefundAudit> etFltRefundAuditList = etTsPGSMapper.findAuditByRefundId(etFltRefund.getId());
                            List<EtFltRefundPaxSeg> etFltRefundPaxSegList = etTsPGSMapper.findPaxSegByRefundId(etFltRefund.getId());
                            List<EtPassengerSegment> etPassengerSegmentList = new ArrayList<>();
                            for (EtFltRefundPaxSeg etFltRefundPaxSeg : etFltRefundPaxSegList) {
                                etPassengerSegmentList.addAll(etTsPGSMapper.findPsByPaxSegId(etFltRefundPaxSeg.getPaxSegId()));
                            }

                            List<EtPassenger> etPassengerList = new ArrayList<>();
                            List<EtSegment> etSegmentList = new ArrayList<>();
                            List<EtTicket> etTicketList = new ArrayList<>();
                            EtSpecialRefundOriginTkt etSpecialRefundOriginTkt = new EtSpecialRefundOriginTkt();
                            for (EtPassengerSegment etPassengerSegment : etPassengerSegmentList) {
                                etPassengerList.addAll(etTsPGSMapper.findPByPId(etPassengerSegment.getPassengerId()));
                                etSegmentList.addAll(etTsPGSMapper.findSBySId(etPassengerSegment.getSegmentId()));
                                etTicketList.addAll(etTsPGSMapper.findTByTId(etPassengerSegment.getTicketId()));
                                etSpecialRefundOriginTkt = etTsPGSMapper.findOriginByPaxSegId(etPassengerSegment.getId());
                            }
                            List<Customer> customerList = new ArrayList<>();
                            List<EtPnr> etPnrList = new ArrayList<>();

                            for (EtPassenger etPassenger : etPassengerList) {
                                customerList.addAll(etTsPGSMapper.findCByCId(etPassenger.getCustomerId()));
                                if (etPassenger.getPnrId() != null) {
                                    etPnrList.addAll(etTsPGSMapper.findPnrByPnrId(etPassenger.getPnrId()));
                                }
                            }

                            List<EtFltRefundOutsidePayment> etFltRefundOutsidePaymentList = etTsPGSMapper.findOutPaymentByRefundId(etFltRefund.getId());

                            List<EtPayment> etPayPayList = new ArrayList<>();
                            for (EtFltRefundOutsidePayment etFltRefundOutsidePayment : etFltRefundOutsidePaymentList) {
                                etPayPayList.addAll(etTsPGSMapper.findPayPaymentById(etFltRefundOutsidePayment.getPaymentId()));
                            }

                            List<EtFltRefundPayment> etRefundPaymentList = etTsPGSMapper.findRefundPaymentByRefundId(etFltRefund.getId());
                            List<EtPayment> etRefundPayList = new ArrayList<>();
                            for (EtFltRefundPayment etFltRefundPayment : etRefundPaymentList) {
                                etRefundPayList.addAll(etTsPGSMapper.findRefundPaymentById(etFltRefundPayment.getPaymentId()));
                            }

                            EtSpecialRefundBankInfo etSpecialRefundBankInfo = etTsPGSMapper.findBankByRefundId(etFltRefund.getId());
                            List<EtSpecialRefundImg> etSpecialRefundImgList = etTsPGSMapper.findImgByRefundId(etFltRefund.getId());

                            flightRefundSpecialHisDataSyncHelper.insertRefund(
                                    etFltRefund,
                                    etFltRefundAuditList,
                                    etPayPayList,
                                    customerList,
                                    etPassengerList,
                                    etPnrList,
                                    etFltRefundOutsidePaymentList,
                                    etTicketList,
                                    etSegmentList,
                                    etPassengerSegmentList,
                                    etSpecialRefundOriginTkt,
                                    etFltRefundPaxSegList,
                                    etSpecialRefundBankInfo,
                                    etSpecialRefundImgList
                            );
                    }
                } catch (Exception e) {
                    log.error("pgs history sync, req: {}, error: {}", JSON.toJSONString(req), refund.getRefundNo());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 补录退款支付记录数据
     *
     * @return
     */
    // @RequestMapping(value = "historyDataSyncForRefundPayment", method = RequestMethod.POST)
    public void historyDataSyncForRefundPayment(@RequestBody SpecialRefundHisDataSyncBackendReq req) {
        try {
            log.info("pgs history sync for refund payment, req: {}", JSON.toJSONString(req));
            List<EtFltRefund> etFltRefundList = etTsPGSMapper.findRefundByTime(req.getCreateTimeStart(), req.getCreateTimeEnd());
            for (EtFltRefund refund : etFltRefundList) {
                try {
                    EtFltRefund exist = etFltRefundMapper.selectOne(new QueryWrapper<EtFltRefund>().eq("refund_no", refund.getRefundNo()));
                    if (exist != null) {
                        List<EtFltRefundPayment> etRefundPaymentList = etTsPGSMapper.findRefundPaymentByRefundId(refund.getId());
                        List<EtPayment> etRefundPayList = new ArrayList<>();
                        if (etRefundPaymentList != null && etRefundPaymentList.size() > 0) {
                            for (EtFltRefundPayment etFltRefundPayment : etRefundPaymentList) {
                                etRefundPayList.addAll(etTsPGSMapper.findRefundPaymentById(etFltRefundPayment.getPaymentId()));
                            }
                            List<EtFltRefundPayment> existRP = etFltRefundPaymentMapper.selectList(new QueryWrapper<EtFltRefundPayment>().eq("flt_refund_id", exist.getId()));
                            if (existRP == null || existRP.size() == 0) {
                                flightRefundSpecialHisDataSyncHelper.insertRefundPayment(exist.getId(), etRefundPaymentList, etRefundPayList);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("pgs history sync for refund payment, req: {}, error: {}", JSON.toJSONString(req), refund.getRefundNo());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
