package net.jdair.specialrefund.bizz.feign;


import net.jdair.specialrefund.bizz.domain.TsStation;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "jd-ms-baseinfo",path="/ts-station",contextId="ts-station")
public interface BaseinfoClient {


    @GetMapping("getTsStationByIataId")
    RestResponse<TsStation> getTsStationByIataId(@RequestParam(name = "iataId") String iataId) ;

}
