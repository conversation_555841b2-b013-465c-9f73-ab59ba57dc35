package net.jdair.specialrefund.bizz.feign;

import net.jdair.specialrefund.bizz.vo.TicketAndPnrClearRequest;
import net.jdair.specialrefund.common.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "JD-IBE")
public interface JdIbeFeignClient {
    /**
     * 退票清位处理
     * @param request
     * @return
     */

    @PostMapping("/api/ticketAndPnr/clear")
    RestResponse<Boolean> ticketAndPnrClear(@RequestBody TicketAndPnrClearRequest request) ;
}
