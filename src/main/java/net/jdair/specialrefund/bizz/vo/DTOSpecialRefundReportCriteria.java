package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 特殊退票 - 报表导出
 */
@Data
public class DTOSpecialRefundReportCriteria implements Serializable {

	private static final long serialVersionUID = 1086477888592214350L;

	/**
	 * 退票申请日期开始。eg. 2020-08-01
	 */
	private String refundDateStart;
	/**
	 * 退票申请日期截止。eg. 2020-08-10
	 */
	private String refundDateEnd;

	/**
	 * 航班日期开始。eg. 2020-08-01
	 */
	private String flightDateStart;
	/**
	 * 航班日期截止。eg. 2020-08-10
	 */
	private String flightDateEnd;

	/**
	 * 审核日期开始。eg. 2020-08-01
	 */
	private String auditDateStart;
	/**
	 * 审核日期截止。eg. 2020-08-10
	 */
	private String auditDateEnd;

	/**
	 * 退款时间开始。eg. 2020-08-01
	 */
	private String payTimeStart;
	/**
	 * 退款时间截止。eg. 2020-08-10
	 */
	private String payTimeEnd;

	/**
	 * 审核状态。eg. FREJECT
	 */
	private String auditStatus;
	/**
	 * 退票类型。eg. FLT_CHANGED
	 */
	private String refundType;


	/**
	 * 出发地。eg. PKX
	 */
	private String depCode;
	/**
	 * 目的地。eg. HAK
	 */
	private String arrCode;

	/**
	 * 当前页号 例如（第1页/共20页）则 currentPageNum 为 1
	 */
	private int currentPageNum;

	/**
	 * 每页记录数
	 */
	private int pageSize;

}
