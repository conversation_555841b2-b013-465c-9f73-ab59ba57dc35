package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 特殊退票用户上传图片
 */
@Data
public class DTOSpecialRefundImg implements Serializable {

	private static final long serialVersionUID = -7231426987109222848L;
	private Long id;
	private String refundNo;   // 退票单号
	private Long fltRefundId;   // 退票单ID
	private String imgUrl; // 图片URL
	private String imgOriginName; // 图片名称
	private SpecialRefundImgSource source;  // 图片来源
    private SpecialRefundImgStatus status;  // 图片状态
    private String operator; // 操作人
    private Date createTime; // 创建时间
	private Long refundAuditId;//审核记录

}
