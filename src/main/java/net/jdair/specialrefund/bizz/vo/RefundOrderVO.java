package net.jdair.specialrefund.bizz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 航班展示
 */
@Data
public class RefundOrderVO implements Serializable {

    private static final long serialVersionUID = 4677562248858017201L;

    public RefundOrderVO() {
    }

    private List<RefundOrder> refundOrderList;

    // 当前页码
    private int pageIndex;
    // 每页数量
    private int pageSize;
    // 总数
    private int totalSize;

    @Data
    public static class RefundOrder {
        // 用户
        private String userName;

        // 退票单创建时间
        private Date createTime;

        // 退票单状态
        private String status;

        // 退票单号
        private String refundNo;

        // 退票类型
        private String refundType;

        // 退票原因
        private String refundReason;

        // 退款日期
        private Date refundMoneyTime;

        // 退款总额
        private double totalRefundAmount;

        // 是否国际票
        private boolean inter;

        // 客票信息
        private List<SegmentInfo> segmentInfoList;

        // 乘机人信息
        private PassengerInfo passengerInfo;

        // 银行卡信息
        private BankInfo bankInfo;

        // 提交的审核信息
        private List<AuditInfo> auditInfoList;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getRefundNo() {
            return refundNo;
        }

        public void setRefundNo(String refundNo) {
            this.refundNo = refundNo;
        }

        public String getRefundType() {
            return refundType;
        }

        public void setRefundType(String refundType) {
            this.refundType = refundType;
        }

        public String getRefundReason() {
            return refundReason;
        }

        public void setRefundReason(String refundReason) {
            this.refundReason = refundReason;
        }

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        public Date getRefundMoneyTime() {
            return refundMoneyTime;
        }

        public void setRefundMoneyTime(Date refundMoneyTime) {
            this.refundMoneyTime = refundMoneyTime;
        }

    }

    /**
     * 客票航段信息
     */
    @Data
    public static class SegmentInfo {
        private String depCode;
        private String depCityCN;
        private String arrCode;
        private String arrCityCN;
        private String flightNo;
        private String flightDate;
        private String cabin;
        private String ticketNo;
        private double refundAmount;
    }


    /**
     * 乘机人信息
     */
    @Data
    public static class PassengerInfo {
        private String passengerName;
        private PassengerType passengerType;
        private String certNo;
    }


    /**
     * 银行卡信息
     */
    @Data
    public static class BankInfo {
        private String cardHolder;
        private String cardNo;
        private String bankName;
        private String mobileNo;
    }


    /**
     * 提交的审核信息
     */
    @Data
    public static class AuditInfo {
        private List<String> imgUrlList; // 用户上传图片 url
        private String remark; // 备注信息
        private Date createTime; // 上传时间
        private String rejectRemark; // 审核拒绝的备注信息


        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }
    }

}