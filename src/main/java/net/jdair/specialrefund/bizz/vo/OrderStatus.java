package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum OrderStatus {
    WK("WK"),
    WW("WW"),
    WP("WP"),
    PI("PI"),
    YF("YF"),
    HP("HP"),
    PF("PF"),
    XX("XX"),
    RI("RI"),
    RF("RF"),
    WF("WF"),
    CF("CF"),
    EX("EX"),
    SO("SO"),
    HANDLED("HANDLED"),
    UNHANDLE("UNHANDLE"),
    REFUSE("REFUSE");

    private final String alias;
    private static HashMap<String, OrderStatus> map = new HashMap();

    static {
        OrderStatus[] var3;
        int var2 = (var3 = values()).length;

        for(int var1 = 0; var1 < var2; ++var1) {
            OrderStatus var0 = var3[var1];
            map.put(var0.alias, var0);
        }

    }

    private OrderStatus(String var3) {
        this.alias = var3;
    }

    public String toString() {
        return this.alias;
    }

    public static OrderStatus valueOfAlias(String var0) {
        OrderStatus var1 = (OrderStatus)map.get(var0);
        if (var1 == null) {
            throw new IllegalArgumentException("Unknown action alias [" + var0 + "]");
        } else {
            return var1;
        }
    }

    public String getAlias() {
        return this.alias;
    }
}
