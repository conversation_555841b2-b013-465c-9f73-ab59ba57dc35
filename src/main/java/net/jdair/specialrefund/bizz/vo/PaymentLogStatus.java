package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum PaymentLogStatus {
	SUCCESS("SUCCESS"), FAILED("FAILED"), ERROR("ERROR");

	private final String alias;
	private static HashMap<String, PaymentLogStatus> map;

	static {
		map = new HashMap<String, PaymentLogStatus>();
		for (PaymentLogStatus type : PaymentLogStatus.values()) {
			map.put(type.alias, type);
		}
	}

	PaymentLogStatus(String alias) {
		this.alias = alias;
	}

	@Override
	public String toString() {
		return this.alias;

	}
	
	public static PaymentLogStatus valueOfAlias(String alias) {
		PaymentLogStatus type = map.get(alias);

		if (type == null) {
			throw new IllegalArgumentException("Unknown pay status alias [" + alias + "]");
		}

		return type;
	}
}