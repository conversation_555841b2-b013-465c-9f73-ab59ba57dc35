package net.jdair.specialrefund.bizz.vo;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 特殊退票 - 报表导出
 * 二审通过时间	舱位	航班号	退票单号	出发地	目的地	退票申请时间（明确至具体时刻）	旅客姓名	票号	备注	审核状态	一审备注	二审备注	售价	机建	燃油	申请退款	实际退款	退票类型	退票原因
 *
 */
@Data
public class DTOSpecialRefundReport implements Serializable {

	private static final long serialVersionUID = 6417173772186665299L;

	private Long paxSegId;

	/**
	 * 航班日期。eg. 2020-08-20 15:30:22
	 */
	private String secondAuditTime;

	/**
	 * 舱位。eg. U
	 */
	private String cabin;

	/**
	 * 航班号。eg. JD5588
	 */
	private String flightNo;

	/**
	 * 退票单号。eg. 20200827094557294667
	 */
	private String refundNo;

	/**
	 * 出发地。eg. PKX
	 */
	private String depCode;

	/**
	 * 目的地。eg. HAK
	 */
	private String arrCode;

	/**
	 * 退票申请日期。eg. 2020-08-10 15:00:02
	 */
	private String refundDate;

	/**
	 * 旅客姓名。eg. 张三
	 */
	private String passengerName;

	/**
	 * 票号。eg. 898-1234567890
	 */
	private String ticketNo;

	/**
	 * 备注。
	 */
	private String remark;

	/**
	 * 审核状态。
	 */
	private String auditResult;

	/**
	 * 一审备注。
	 */
	private String firstAuditRemark;

	/**
	 * 二审备注。
	 */
	private String secondAuditRemark;

	/**
	 * 售价。
	 */
	private BigDecimal salePrice;
	/**
	 * 基建费。
	 */
	private BigDecimal airportTax;
	/**
	 * 燃油费。
	 */
	private BigDecimal fuelTax;
	/**
	 * 申请退款。
	 */
	private BigDecimal applyAmount;
	/**
	 * 实际退款。eg. 245.5
	 */
	private BigDecimal refundAmount;

	/**
	 * 退票类型。eg. FEE_CHANGED
	 */
	private String refundType;

	/**
	 * 退票原因。eg. DOWNCABIN
	 */
	private String refundReason;

	/**
	 * 退款时间。eg. 2020-08-10 15:00:02
	 */
	private String payTime;

}
