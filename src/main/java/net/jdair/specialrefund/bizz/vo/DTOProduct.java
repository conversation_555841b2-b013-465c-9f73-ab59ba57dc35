package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DTOProduct implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long id;
	private String code;
	private String name;
	private String description;
	private String airlineCode;
	private Date createDate;
	/** Login ID of the user who created the product */
	private String createId;
	private ProductStatus status;
	private String ei;
	private String type;

	public DTOProduct() {

	}

	public DTOProduct(String code, String name, String description, String airlineCode, Date createDate,
                      String createId) {
		super();
		this.code = code;
		this.name = name;
		this.description = description;
		this.airlineCode = airlineCode;
		this.createDate = createDate;
		this.createId = createId;
	}
	public DTOProduct(String code, String name, String description, String airlineCode, Date createDate,
                      String createId, String type) {
		super();
		this.code = code;
		this.name = name;
		this.description = description;
		this.airlineCode = airlineCode;
		this.createDate = createDate;
		this.createId = createId;
		this.type=type;
	}

	@Override
	public String toString() {
		return "DTOProduct [id=" + id + ", code=" + code + ", name=" + name + ", description=" + description + ", airlineCode=" + airlineCode + ", createDate=" + createDate + ", createId=" + createId
				+ ", status=" + status + ", ei=" + ei + ", type="+ type +"]";
	}
	

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((airlineCode == null) ? 0 : airlineCode.hashCode());
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		result = prime * result
				+ ((createDate == null) ? 0 : createDate.hashCode());
		result = prime * result
				+ ((createId == null) ? 0 : createId.hashCode());
		result = prime * result
				+ ((description == null) ? 0 : description.hashCode());
		result = prime * result + ((ei == null) ? 0 : ei.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		result = prime * result + ((status == null) ? 0 : status.hashCode());
		result = prime * result + ((type == null) ? 0 : type.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DTOProduct other = (DTOProduct) obj;
		if (airlineCode == null) {
			if (other.airlineCode != null)
				return false;
		} else if (!airlineCode.equals(other.airlineCode))
			return false;
		if (code == null) {
			if (other.code != null)
				return false;
		} else if (!code.equals(other.code))
			return false;
		if (createDate == null) {
			if (other.createDate != null)
				return false;
		} else if (!createDate.equals(other.createDate))
			return false;
		if (createId == null) {
			if (other.createId != null)
				return false;
		} else if (!createId.equals(other.createId))
			return false;
		if (description == null) {
			if (other.description != null)
				return false;
		} else if (!description.equals(other.description))
			return false;
		if (ei == null) {
			if (other.ei != null)
				return false;
		} else if (!ei.equals(other.ei))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equals(other.name))
			return false;
		if (status != other.status)
			return false;
		if (type == null) {
			if (other.type != null)
				return false;
		} else if (!type.equals(other.type))
			return false;
		return true;
	}
	

	
}