package net.jdair.specialrefund.bizz.vo;

public enum PassengerType {
    ADULT("ADULT"),
    CHILD("CHILD"),
    INFANT("INFANT");

    private final String alias;

    private PassengerType(String var3) {
        this.alias = var3;
    }

    public String getAlias() {
        return this.alias;
    }

    public static String convertString(String[] var0) {
        if (var0 != null && var0.length > 0) {
            String var1 = "";
            String[] var5 = var0;
            int var4 = var0.length;

            for(int var3 = 0; var3 < var4; ++var3) {
                String var2 = var5[var3];
                var1 = var1 + var2 + ",";
            }

            if (var1.endsWith(",")) {
                var1 = var1.substring(0, var1.length() - 1);
            }

            return var1;
        } else {
            return null;
        }
    }
}
