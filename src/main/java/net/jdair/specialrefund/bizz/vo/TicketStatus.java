package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum TicketStatus {
	OPEN_FOR_USE("O"),		// 客票未使用,有效
	VOID("V"),				// 已作废

	REFUNDED("R"),			// 已退票

	CHECKED_IN("C"),		// 已经办理值机
	LIFT_OR_BOARED("L"),	// 客票已使用（可修改）
	USED_OR_FLOWN("F"),		// 客票已使用（不可修改）

	SUSPENDED("S"),			// 系统处理，客票禁止使用

	PRINT_OR_EXCH("X"),		// 电子客票已换开为纸票

	EXCHANGE("E"),			// 换开ET/手工换开
	FIM_OR_EXCHANGE("G");	// 使用中断舱单签转外航
	
	/**
	 * Alias name. This is the name which will be stored in database.
	 */
	private final String alias;

	/**
	 * A hash map which keep all the ticket status enum with alias name as key.
	 */
	private static HashMap<String, TicketStatus> map;

	/**
	 * Construct the hash map at load time
	 */
	static {
		map = new HashMap<String, TicketStatus>();
		for (TicketStatus status : TicketStatus.values()) {
			map.put(status.alias, status);
		}
	}

	TicketStatus(String alias) {
		this.alias = alias;
	}

	@Override
	public String toString() {
		return this.alias;

	}

	public String getAlias() {
		return alias;
	}

	/**
	 * Return a <code>TicketStatus</code> object holding the value of specified alias name
	 * 
	 * @param alias
	 *            the alias name of the status
	 * 
	 * @return a <code>TicketStatus</code> object holding the value represented by the alias name
	 *         argument
	 */
	public static TicketStatus valueOfAlias(String alias) {
		TicketStatus status = map.get(alias);

		if (status == null) {
			throw new IllegalArgumentException("Unknown ticket status alias [" + alias + "]");
		}

		return status;
	}
}
