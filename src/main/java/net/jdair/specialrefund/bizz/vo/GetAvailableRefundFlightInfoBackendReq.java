    package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

@Data
public class GetAvailableRefundFlightInfoBackendReq {
    // 旅客姓名
    private String passengerName;

    // 票号
    private String ticketNo;

    // 旅客身份证号
    private String idCardNo;

    /**
     * 后台管理员退票类型:
     *
     * 航班改期类退票：购票成功后。客票发生变动导致无法在原出票地办理退票。(FLT_CHANGED)
     * 退补差价、手续费：因航司原因航班变动导致旅客产生额外费用。(FEE_CHANGED)
     * 降舱、备降/经停取消退费：客票状态USED (AIRLINE_REASON)
     * 客票超过一年有效期退税：客票自购票之日起超过1年有效期，无法在原出票地提交退票，仅能办理退税业务（仅限国内）。(EXPIRED)
     * 代理人错误导致删票：主要解决目前小程序仅补退退票手续费可提交Refunded状态的客票，如删票通过补退提交进杂费单，影响票证结算，涉及客票已销未运，但实际金额已退款的问题。（AGENT_FAULT）
     * */
    private String refundType;


    // 航班日期。格式：yyyy-MM-dd
    private String flightDate;

    /**
     * 团队网退票。旅客姓名/票号。
     * 格式：
     * 测试一,898-1234567890
     * 测试一,898-1234567890
     * 测试一,898-1234567890
     * 测试一,898-1234567890
     */
    private String passengerNameAndTicketNo;
}
