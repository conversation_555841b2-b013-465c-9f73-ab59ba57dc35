
package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/** 
 * @ClassName:	JSONResult 
 * @Description: api接口返回封装对象 
 * @author:	wxp
 * @date:	2015年6月4日 
 *  
 */
@Data
public class JSONResult implements Serializable {

	private static final long serialVersionUID = -5252597313673064208L;
	/**
	 * @Fields code : 请求返回状态码（success/fail） 
	 */ 
	private String status;
	
	
	/** 
	 * @Fields data : 请求返回数据 
	 */ 
	private Map<String, Object> data;
	
	
	/** 
	 * @Fields error : 请求失败错误信息 
	 */ 
	private ErrorMessage error;
	
	public JSONResult() {
		super();
	}

	public JSONResult(String status, ErrorMessage error) {
		super();
		this.status = status;
		this.error = error;
	}
}
