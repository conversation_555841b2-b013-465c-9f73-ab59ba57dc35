package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import lombok.Getter;

/**
 * 国际票、海外站退票请求参数
 */
@Data
public class InterRefundReq {
    private String lines;
    private String flightNos;
    private String passengerNames;
    private String passengerTypes;

    @Getter
    private String cTypes;
    @Getter
    private String cNos;
    private String depTimes;
    private String arrTimes;
    private String ticketNos;
    private String pnrs;
    private String cabins;
    private String ticketPrices;
    private String taxPrices;

    private String totalAmount;
    private String thirdPaymentNo;

    private String user;
    private String remark;
    private String refundType;//自愿非自愿
    private String reason;

    private String issuedTime;
    private String refundTime;

    private String orderNo;

    private String sign;

    private String mobileNo;

    private String payType;

    private String officeNo;

    private String allTaxDesc;

    private String paymentNo;

    /**
     * 标识是否含随票保险.(1: 含， 0：不含)
     */
    private String hasInsurance;

    public String getcTypes() {
        return cTypes;
    }

    public String getcNos() {
        return cNos;
    }
}
