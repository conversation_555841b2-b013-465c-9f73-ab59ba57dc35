package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DTOPassenger {
    private Long id;
    private String name;
    private String othername;
    private PassengerType passengerType;
    private Gender gender;
    private Date birthday;
    private String birthStr;
    private String certificateNo;
    private String certificateType;
    private String jjcType;
    private String jjcNo;
    private String frequentflyerno;
    private DTOPnr pnrValue;
    private String firstname;
    private String lastname;
    private String nationality;
    private String passportno;
    private String bornDate;
    private String expiredDate;
    private String remark;
    private String issueCountry;
    private String depCity;
    private String depProvince;
    private String depCountry;
    private String depStreet;
    private String depPost;
    private String arrCity;
    private String arrProvince;
    private String arrCountry;
    private String arrPost;
    private String arrStreet;
    private DTOOrder dtoOrder;
    private DTOPassenger accompaniedByPassenger;
    private List<DTOPassenger> accompanyingPassengerList;
    private List<DTOPaxSegment> passengerSegmentList;
    private Long changeNameTime;
    private Long changeCertiTime;
    private String ticketMoney = "0";
    private String airportTaxMoney = "0";
    private String fuelTaxMoney = "0";
    private String allMoney = "0";
    private String insuranceAmount = "0";
    private String xcdPrintStatus;
    private int isInsure;
    private int insurance1;
    private int insurance2;
    private int insurance3;
    private int insurance4;
    private int insurance5;
    private String coPromote;
    private int memberPoint;
    private String memberLevel;
    private String memberPointStr;
    private String memberPlusNo;
    private String inuranceNumberAndType;
    private int isSaveCommonPassenger = 0;
    private String mobilePhone;
    private int isFlightSms;
}
