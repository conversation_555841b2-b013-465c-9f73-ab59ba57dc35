
package net.jdair.specialrefund.bizz.vo;

import java.io.Serializable;

/**
 * @ClassName:	Error 
 * @Description: 接口错误信息类 
 * @author:	wxp
 * @date:	2015年6月4日 下午9:56:59 
 *  
 */
public class ErrorMessage implements Serializable {

	private static final long serialVersionUID = -5707174682745786617L;
	/**
	 * @Fields code : 错误代码 
	 */ 
	private String code;
	
	/** 
	 * @Fields message : 错误信息 
	 */ 
	private String message;

	public ErrorMessage() {

	}
	
	/** 
	 * @Title:	Error 
	 * @Description: 构造函数 
	 * @param:	@param code
	 * @param:	@param message
	 * @throws 
	 */
	public ErrorMessage(String code, String message) {
		super();
		this.code = code;
		this.message = message;
	}
	
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
}
