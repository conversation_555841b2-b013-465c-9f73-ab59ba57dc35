package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 航班展示
 */

@Data
public class DTOFlightInfo implements Serializable {

    private static final long serialVersionUID = 4201979520326159655L;

    private String ticketNo;  // 票号
    private boolean international;  // 是否国际
    private List<Segment> segmentList;  // 航段信息


    public DTOFlightInfo() {
    }

    /**
     * 航段信息
     */
    @Data
    public static class Segment {
        private String passengerName;
        private PassengerType passengerType;
        private String depCode;
        private String depCityCN;
        private String arrCode;
        private String arrCityCN;
        private String airlineCode;
        private String flightNo;
        private String flightDate;
        private String cabin;
        private String depTime; // 17:00
        private String arrTime; // 18:47

    }

}