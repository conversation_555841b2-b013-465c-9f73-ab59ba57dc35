package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

@Data
public class SpecialRefundSubmitBackendReq {
    /**
     * 航班查询的 uuid
     */
    private String uuid;

    // 客票信息。格式：票号1,出发地三字码1-目的地三字码1,舱位1;票号1,目的地三字码1-出发地三字码1,舱位1;票号2,出发地三字码2-目的地三字码2,舱位2。
    // 示例：898-2198315770,HFE-XIY,Y;898-2198315771,PXK-HAK,Y
    private String ticketInfos;

    // 退票类型
    private String refundType;

    // 退票具体原因。如之前选择的是改期后退票，则增加两个小项选择：“自愿退票”、“非自愿退票”；如之前选择的是降舱、备降/经停取消退费，则增加两个小项选择：“降舱”、“备降/经停地取消”，为必选项，旅客选择后，带到后台生成的订单中展示；
    private String refundReason;

    // 上传的附件。
    private List<MultipartFile> resources;
    private List<String> resourcesFileName;


    // 持卡人姓名
    private String cardHolder;

    // 银行卡号
    private String cardNo;

    // 开户行名称
    private String bankName;

    // 账号类型（个人账号: 1，对公账号: 2）
    private String accountType;

    // 银行联行号（对公账号必传）
    private String nbkno;

    // 代客提交，传给易生的备注信息
    private String ecardRemark;

    // 联系人电话
    private String mobileNo;

    // 退票单号
    private String fltRefundNo;

    // 备注信息
    private String remark;

    // 后台管理员登录的账号名
    private String loginUserName;
}
