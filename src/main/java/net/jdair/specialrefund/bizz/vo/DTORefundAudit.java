
package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.util.Date;

/**
 *退票审核传输对象
 */
@Data
public class DTORefundAudit implements Serializable {

	private static final long serialVersionUID = -6466753533152488313L;//增加序列化，防止序列化不一致问题
	/**
	 * log instance,will be called to write down system log
	 */
	private static Log log = LogFactory.getLog(DTORefundAudit.class);
	
	private Long id;
	/**
	 * 退票申请 id
	 */
	private Long refundId;
	/**
	 * 提交人

	 */
	private String submmitUsername;
	/**
	 * 提交时间
	 */
	private Date submmitTime;
	/**
	 * 审核人

	 */
	private String auditUsername;
	/**
	 * 审核时间
	 */
	private Date auditTime;
	/**
	 * 审核结果
	 */
	private String auditResult;
	/**
	 * 动作 （一审、二审）
	 */
	private String action;
	/**
	 * 审核备注
	 */
	private String notion;
	private String amountStr;

}
