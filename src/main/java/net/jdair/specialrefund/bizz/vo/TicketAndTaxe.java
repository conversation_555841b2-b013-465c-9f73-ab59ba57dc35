package net.jdair.specialrefund.bizz.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON> on 20/8/25.
 */
public class TicketAndTaxe implements Serializable {
    private static final long serialVersionUID = -2503124738881234161L;
    private String ticketNo;
    private String cn;//机建
    private String yr;//燃油
    private String yq;//战险
    private BigDecimal other;//其他税


    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getYq() {
        return yq;
    }

    public void setYq(String yq) {
        this.yq = yq;
    }


    public String getYr() {
        return yr;
    }

    public void setYr(String yr) {
        this.yr = yr;
    }

    public BigDecimal getOther() {
        return other;
    }

    public void setOther(BigDecimal other) {
        this.other = other;
    }
}
