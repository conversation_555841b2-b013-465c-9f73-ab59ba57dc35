package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

@Data
public class GetAvailableRefundFlightInfoReq {

    // 小程序 openid
    private String openid;

    // 旅客姓名
    private String passengerName;

    // 票号
    private String ticketNo;

    // 旅客身份证号
    private String idCardNo;

    /**
     * 退票类型:
     *
     * 航班改期类退票：购票成功后。客票发生变动导致无法在原出票地办理退票。(FLT_CHANGED)
     * 退补差价、手续费：因航司原因航班变动导致旅客产生额外费用。(FEE_CHANGED)
     * 降舱、备降/经停取消退费：客票状态USED (AIRLINE_REASON)
     * 客票超过一年有效期退税：客票自购票之日起超过1年有效期，无法在原出票地提交退票，仅能办理退税业务（仅限国内）。(EXPIRED)
     * */
    private String refundType;


    // 航班日期。格式：yyyy-MM-dd
    private String flightDate;

    // 是否国际票
    private Boolean inter;

    // 签名
    private String sign;
}
