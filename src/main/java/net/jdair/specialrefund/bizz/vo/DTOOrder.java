package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DTOOrder implements Serializable, Cloneable {

    private static final long serialVersionUID = -2117680661563418372L;

    private Long id;
    private String orderNo;
    private Date createTime;
    private Date createTimeforTQ;
    private String userName;
    private String officeNo;
    private String currency;
    private OrderStatus status;
    private String contactName;
    private String contactTel;
    private String contactMobile;
    private String contactEmail;
    private String contactAddress;
    private Date timeLimit;
    private String airlineCode;
    private String responsibility;
    private String mobile;
    private String ipAddress;
    private String source;
    private Integer issurSMSSend = 0;
    private String webSource;
    private Long userId;
    private List<DTOSegment> segmentList;
    private List<DTOPassenger> passengerList;
    private List<DTOPayment> payments;
    private BigDecimal orderAllMoney;
    private BigDecimal orderAmount;
    private BigDecimal orderHasPay;
    private BigDecimal totalFare;
    private BigDecimal ticketprice;
    private BigDecimal mailFee;
    private int international;

}
