package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import net.jdair.specialrefund.bizz.utils.NumberUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class DTOFltRefundPaxSeg implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6918625642433369645L;

	private Long id;

	private Long refundId;

	private Date refundDate;
	/**
	 * 取消的航段
	 * 
	 */
	private DTOPaxSegment paxSegment;

	private double fareRefundAmount = 0;

	private double airportTaxRefundAmount = 0;

	private double fuelSurchargeRefundAmount = 0;

	private double otherTaxesRefundAmount = 0;

	private double actualRefundAmount = 0;

	private double actualChargeAmount = 0;

	private double fareRefundRate = 0;

	private double processFeeRate = 0;
	
	//手续费
	private double processFee=0;
	
	//随心游的手续费和费率
	private double sxyprocessFeeRate = 0;
	
	private double sxyprocessFee = 0;

	/**
	 * Remark field for fare refund amount calculation. The remark is expected to be set in refund rules.
	 */
	private String remark;
	
	/**
	 * add by ange-wan
	 * 随心游产品手续费基础票价
	 */
    private double baseAmountForFee=0;
	/**
	 * add by huang_yu
	 */
	private double applyAmount = 0;
	
	private int refundPoint = 0;
	
	/**
	 * 辅营产品费用
	 */
	private double serviceOrderAmount = 0;
	
	private double amsRefundPoint =0;
	
	/**
	 * 机上餐食
	 */
	
	private double mealFee =0;
	
	/**
	 * 逾重行李
	 */
	
	private double luggageFee =0;
	
	
	/**
	 * 行程单
	 */
	
	private double itinerary =0;

    /**
     * 真实折扣：特价产品/基础运价 用于计算退票费率
     */
    private double realRebate = 0;

	//标识是否是改升 ORG:不是改升  OLD：改升旧航段 CHANGE：改升的航段 NEW：改升的新航段
	private String changeFlg;
	//退款的状态 REFUND:已退   FAIL:退款失败 NO_REFUND:未退款
	private String status;


	public DTOFltRefundPaxSeg() {

	}

	public void finalize() throws Throwable {

	}

	public double getRefundAmount() 
	{
		double totalTax=0.0;
		if(paxSegment.getPaxsegTaxList()!=null)
		{
			for(DTOPaxsegTax tax:paxSegment.getPaxsegTaxList())
			{
				totalTax =totalTax+tax.getFare();
			}
		}
		return fareRefundAmount + airportTaxRefundAmount + fuelSurchargeRefundAmount + otherTaxesRefundAmount+serviceOrderAmount+totalTax;

	}
	
	public double getTotalTax() 
	{
		double totalTax=0.0;
		if(paxSegment.getPaxsegTaxList()!=null)
		{
			for(DTOPaxsegTax tax:paxSegment.getPaxsegTaxList())
			{
				totalTax =totalTax+tax.getFare();
			}
		}
		return totalTax;

	}

	public double getProcessFeeRate() {
		
		double result=1;
		double processFee=this.getProcessFee();

		//出票时间在2017-05-22（含）之后，退票时间在2017-06-30(含)之后的，CCDJ，PYY1，CClass产品使用实付价计算手续费
		Date refundTimeLimit = null;
		Date issueTimeLimit = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			refundTimeLimit = sdf.parse("2016-06-30");
			issueTimeLimit = sdf.parse("2016-05-22");
		}catch (Exception e){
			refundTimeLimit = new Date();
			issueTimeLimit = new Date();
		}

		if( null != paxSegment.getProductApplied()
				&& ",YFYB,".indexOf("," + paxSegment.getProductApplied().getCode() + ",") < 0
				&& paxSegment.getTicketIssued().getBookTime().getTime() >= issueTimeLimit.getTime()
				&&  refundDate.getTime() >= refundTimeLimit.getTime()
				&& processFee < paxSegment.getNetFare()){
			//出票时间在2017-05-22（含）之后，退票时间在2017-06-23(含)之后的，CCDJ，PYY1，CClass产品使用实付价计算手续费
			result=processFee/(paxSegment.getNetFare() + paxSegment.getCouponPrice().doubleValue());
		}
		else{
			//手续费小于实际售价，大于则按照100%计算
			if(processFee<paxSegment.getNetFare()){
				result=processFee/paxSegment.getMarketFare();
			}
		}
		
		return NumberUtils.roundtoNearest(result*100,1) ;
	}

	public void setProcessFeeRate(double processFeeRate) {
		this.processFeeRate = processFeeRate;
	}

	/**
	 * @return the processFee
	 */
	public double getProcessFee() {
		double result=0;
		double insuranceFare=0;
		double totalTax=0;
		double refundAmount=actualRefundAmount;
		double serviceAmount = serviceOrderAmount;
		if(actualRefundAmount==0){
			refundAmount=this.applyAmount;		
		}
		/* 保险单独处理，费用不在退票中处理
		 * List<DTOInsurance> insuranList = paxSegment.getInsuranceList();
		if(insuranList!=null&&insuranList.size()>0)
		{
			for(DTOInsurance dtoInsurance:insuranList)
			{
				if((!dtoInsurance.getStatus().equals( InsuranceStatus.XX.toString()))&&(!dtoInsurance.getStatus().equals( InsuranceStatus.OH.toString()))
						&&(!dtoInsurance.getStatus().equals( InsuranceStatus.OF.toString())))								
				{
					insuranceFare+=dtoInsurance.getAmount();
				}
			}
		}*/
		if(paxSegment.getPaxsegTaxList()!=null&&paxSegment.getPaxsegTaxList().size()>0)
		{
			for(DTOPaxsegTax tax:paxSegment.getPaxsegTaxList())
			{
				totalTax=totalTax+tax.getFare();
			}
		}


			result=(paxSegment.getNetFare()+paxSegment.getAirportTax()+paxSegment.getFuelTax()+totalTax+insuranceFare+serviceAmount -refundAmount);
			if(result>=paxSegment.getNetFare()){
				result = paxSegment.getMarketFare();
			}

		return result;
	}


    public double getRealRebate() {
        try {
            this.realRebate = this.getPaxSegment().getNetFare()/this.getPaxSegment().getBaseFare();
        }catch (Exception e){

            this.realRebate = 0;
        }
        return realRebate;
    }

    public void setRealRebate(double realRebate) {
        this.realRebate = realRebate;
    }

}