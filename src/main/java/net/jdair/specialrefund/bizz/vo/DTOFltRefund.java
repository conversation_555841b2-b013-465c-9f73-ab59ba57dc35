package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class DTOFltRefund implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7744046230437330313L;
	private Long id;
	private Long orderId;
	private String refundNo;
	private String userName;
	private Date createTime;
	private String status;
	private String remark; //退票申请备注

	private String airlineCode;//为了兼容apollo这个坑

	/**
	 * 退票原因
	 */
	private String constraintReason;

	private String payMode;//退款方式（线上：online 线下:offline）

	private Date payTime;//退款时间

	private String refundType;//退票类型(当日作废、自愿退票、非自愿退票)
	private String officeNo;
	private boolean rejected = false;

	private List<DTOFltRefundPaxSeg> fltRefundPaxSegList = new ArrayList<DTOFltRefundPaxSeg>();

	private List<DTOPayment> payments=new ArrayList<DTOPayment>();

	private List<DTOPayment> outsidePayments = new ArrayList<DTOPayment>();

	//是否退还免票

	private String refundFT;
	//是否退还积分

	private String refundPoint;
	/**
	 * 退票人
	 */
	private String refundUser ;
	/**
	 * 退款时间

	 */
	private Date refundTime;
	/*
	 * 用户类型
	 */
	private String userType;
	/*
	 * 订单来源
	 */
	private String orderSource;
	
	
	///////////////方便查看退款信息//////////////////////机建、舱位、票面价、实售价、申请退款、实际退款、手续费
	//燃油
	private double fareRefundAmount = 0;
	//机建
	private double airportTaxRefundAmount = 0;
	//舱位
	private String cabinRefund = "";
	//票面价

	private double ticketSaleAmount = 0;
	//实售价

	private double actualSaleAmount = 0;
	//申请退款

	private double applyRefundAmount = 0;
	//实际退款

	private double actualRefundAmount = 0;
	//手续费

	private double processFeeAmount = 0; 
	//是否多人退款

	private String morePeople = "NO";
	
	private Date retrieveTime;//清位时间
	
	//升舱改期单ID
	private Long changeId;
	
	private int retrieveTimeFlag= 0;//获取清位时间是否正常标识符，0为正常，1为异常
	
	
	//优惠券号码
	private String couponNo;
	private String couponType;//优惠券类型

	private String fictitious;
	private String reserveRemark;

	// 特殊退票的银行信息
	private DTOSpecialRefundBankInfo dtoSpecialRefundBankInfo;

	// 特殊退票的用户上传的图片 url
	private List<DTOSpecialRefundImg> dtoSpecialRefundImgList;

	private Boolean isAotoPass = false;//是否为自动二审
	public int getRetrieveTimeFlag() {
		return retrieveTimeFlag;
	}

	public void setRetrieveTimeFlag(int retrieveTimeFlag) {
		this.retrieveTimeFlag = retrieveTimeFlag;
	}

	public DTOFltRefund() {

	}

	public DTOFltRefund(Long orderId, String userName) {
		this.orderId = orderId;
		this.userName = userName;
		this.createTime = new Date();
		this.status = "NEW";
	}

	public void finalize() throws Throwable {

	}

	/**
	 * Get total refund amount.
	 * 
	 * @return
	 */
	public double getTotalRefundAmount() {
		double amount = 0.0;
		for (DTOFltRefundPaxSeg frps : fltRefundPaxSegList) {
			amount += frps.getRefundAmount();
		}
		return amount;
	}


	/**
	 * Get total actual refund amount.
	 * 
	 * @return
	 */
	public double getTotalActualRefundAmount() {
		double amount = 0.0;
		for (DTOFltRefundPaxSeg frps : fltRefundPaxSegList) {
			amount += frps.getActualRefundAmount();
		}
		return amount;
	}



}
