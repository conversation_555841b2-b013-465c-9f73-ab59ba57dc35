package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class DTOPayment {
    private long id;
    private long[] orderId;
    private String paymentNo;
    private BigDecimal amount = new BigDecimal(0.0D);
    private Date payTime;
    private Date returnTime;
    private String currency;
    private String payer;
    private Character batch;
    private String remark;
    private Date setttleDate;
    private PayType payType;
    private PayAction action;
    private PayStatus payStatus;
    private String content;
    private String merchantId;
    private String cardType;
    private String payerName;
    private String payerContactType;
    private String payerContact;
    private String orderFound;
    private String version;
    private int language;
    private int signType;
    private int paymentType;
    private String bankId;
    private String dealId;
    private String bankDealId;
    private Date dealTime;
    private double fee;
    private String ext1;
    private String ext2;
    private int payResult;
    private String errCode;
    private String errMsg;
    private String signMsg;
    private String paymentTime;
    private BigDecimal paidAmount = new BigDecimal(0.0D);
    private String callBackInfo;
    private String chkValue;
    private String mispayId;
    private String transname;
    private String purchaserId;
    private String attach;
    private String cmdno;
    private String signs;
    private String busType;
    private String busArgs;
    private String busDesc;
    private String tenpayResult;
    private String field1;
    private String field2;
    private String field3;
    private String transId;
    private String orderNum;
    private String transAmt;
    private String transDateTime;
    private String currencyType;
    private String customerName;
    private String merSecName;
    private String productInfo;
    private String customerEMail;
    private String merURL;
    private String merURL1;
    private String merURL2;
    private String payIp;
    private String msgExt;
    private String procStatus;
    private Map extMap;
    private String yeepayPayerId;
    private String productExtraInfo;
    private String resultType;
    private long[] cancelId;
    private String cancelNo;
    private long[] changeId;
    private String changeNo;
    private List<DTOOrder> orders;
    private long orderNo;
    private long[] xcdApplyId;
    private String systemSsn;
    private String source;

}
