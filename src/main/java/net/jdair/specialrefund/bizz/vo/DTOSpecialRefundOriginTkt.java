package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 特殊退票换开情况的原客票信息
 */
@Data
public class DTOSpecialRefundOriginTkt implements java.io.Serializable {


	private static final long serialVersionUID = 7869049903408550033L;
	private Long id;
	private String passengerName;
	private String passengerType;
	private String certNo;
	private String depCode;
	private String arrCode;
	private String airlineCode;
	private String flightNo;
	private String cabinClass;
	private String ticketNo;
	private Date depTime;
	private Date arrTime;
	private BigDecimal marketFare;
	private BigDecimal netFare;
	private BigDecimal airportTax;
	private BigDecimal fuelTax;
	private BigDecimal otherTaxes;
	private BigDecimal tax1;
	private BigDecimal tax2;
	private BigDecimal tax3;
	private BigDecimal tax4;
	private TicketStatus ticketStatus; // Transient
	private Integer sequence;
	private String officeNo;


}
