package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum PayStatus {
	PENDICING("PENDICING"), PENDING("PEND"), PAID("PAID"), FAIL("FAIL"),REFUNDING("REFUNDING"),REFUNDED("REFUNDED"),WAIT_PAY("WAIT_PAY");

	private final String alias;
	private static HashMap<String, PayStatus> map;

	/**
	 * Construct the hash map at load time
	 */
	static {
		map = new HashMap<String, PayStatus>();
		for (PayStatus type : PayStatus.values()) {
			map.put(type.alias, type);
		}
	}

	PayStatus(String alias) {
		this.alias = alias;
	}

	@Override
	public String toString() {
		return this.alias;

	}

	/**
	 * Return a <code>PayStatus</code> object holding the value of specified alias name
	 * @param alias the alias name of the type
	 * @return a <code>PayStatus</code> object holding the value represented by the alias name argument
	 */
	public static PayStatus valueOfAlias(String alias) {
		PayStatus type = map.get(alias);

		if (type == null) {
			throw new IllegalArgumentException("Unknown pay status alias [" + alias + "]");
		}

		return type;
	}

}