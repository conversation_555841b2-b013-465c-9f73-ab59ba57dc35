package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum TripType {
    ONEWAY("OW"),
    RETURN("RT"),
    MULTIWAY("MW"),
    OPEN_JAW("OJ");

    private final String alias;
    private static HashMap<String, TripType> map = new HashMap();

    static {
        TripType[] var3;
        int var2 = (var3 = values()).length;

        for(int var1 = 0; var1 < var2; ++var1) {
            TripType var0 = var3[var1];
            map.put(var0.alias, var0);
        }

    }

    private TripType(String var3) {
        this.alias = var3;
    }

    public String toString() {
        return this.alias;
    }

    public static TripType valueOfAlias(String var0) {
        TripType var1 = (TripType)map.get(var0);
        if (var1 == null) {
            throw new IllegalArgumentException("Unknown trip type alias [" + var0 + "]");
        } else {
            return var1;
        }
    }
}