package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

/**
 * 支付类型
 *
 * NINE_NINE_BILL:快钱
 * STAFF_CARD:员工卡

 * CHINA_PNR:汇付天下
 * CHINA_PAY:银联网付通

 * ALIPAY:支付宝
 * GS_CHINA_BANK:天航手机支付
 * CHINA_EASY_CARD:渤海易生支付
 * HNA_PAY 新生支付
 * TAO_BAO 祥鹏淘宝旗舰店支付
 * E_CURRENCY  虚拟币支付
 * UNION_PAY  银联在线支付
 * LIANLIAN	  连连快捷支付
 * DIRECT  光大直连支付
 * WX_PAY 微信支付
 * CLOUDPAY 云闪付 银联在线
 */
public enum PayType {

	NINE_NINE_BILL("99B"),STAFF_CARD("SC"),CHINA_PNR("CPNR"),CHINA_PNR_CARD("CPNRCARD"),CHINA_PAY("CPAY"),ALIPAY("ALIP"),TEN_PAY("TPAY"),YEE_PAY("YEEPAY"),WX_PAY("WXPAY"),

	CHINA_PAY_CARD("CPAYCARD"),STAFF_CARD_PHONE("SCP"),WAP_ALIPAY("WAPALI"),CHINA_MOBILE_PAY("CMPAY"),PGS_PAY("PGSPAY"),BACK_PAY("SUPER"),JD_CHINA_BANK("JDCNBANK"),

	CHINA_EASY_CARD("ECARD"),GS_CHINA_BANK("GSCNBANK"),HNA_PAY("HNAPAY"),CHINAPNR_XINHUA("CPNRX"),CMB_PAY("CMBPAY"),TAO_BAO("TAOBAO"),E_CURRENCY("ECURRENCY"),UNION_PAY("UNIONPAY"),

	AMS_POINT("AMS_POINT"),YXTX_CARD("YXTX_CARD"),JING_DONG("JINGDONG"),LIANLIAN("LIANLIAN"),DIRECT("DIRECT"),BOC_DIRECT("BOCDIRECT"),SHOUFUYOU("SFY"),MINSHENG_IPAY("MSIPAY"), WORLD_PAY("WORLDPAY"),WX_H5_PAY("WXH5PAY"),WX_G_PAY("WXGPAY"),

	CLOUD_PAY("CLOUDPAY"), COUPON_DJQ("COUPON_DJQ");
	private final String alias;
	private static HashMap<String, PayType> map;

	/**
	 * Construct the hash map at load time
	 */
	static {
		map = new HashMap<String, PayType>();
		for (PayType type : PayType.values()) {
			map.put(type.alias, type);
		}
	}

	PayType(String alias) {
		this.alias = alias;
	}

	@Override
	public String toString() {
		return this.alias;

	}

	/**
	 * Return a <code>PayType</code> object holding the value of specified alias name
	 * @param alias the alias name of the type
	 * @return a <code>PayType</code> object holding the value represented by the alias name argument
	 */
	public static PayType valueOfAlias(String alias) {
		PayType type = map.get(alias);
		if (type == null) {
			throw new IllegalArgumentException("Unknown pay type alias [" + alias + "]");
		}
		return type;
	}

}