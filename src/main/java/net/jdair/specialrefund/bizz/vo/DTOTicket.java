package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class DTOTicket implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9121965747464707680L;

	private Long id;
	private String issCode;
	private String ticketNo;
	private String serialNo;
	private int serialNoCk;
	private boolean printed;
	private int bspType;
	private String endorsement;
	private String ck;
	private String conjunctionTkt;
	private String agentCode;
	private String issuedBy;
	private Date issuedDate;
	private Date bookTime;
	
	private List<DTOPaxSegment> passengerSegmentList = new ArrayList<DTOPaxSegment>();
}
