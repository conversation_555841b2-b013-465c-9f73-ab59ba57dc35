package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

@Data
public class SubmitRefundOrderReq {
    // 小程序 openid
    private String openid;

    // 客票信息。格式：票号1,出发地三字码1-目的地三字码1;票号1,目的地三字码1-出发地三字码1;票号2,出发地三字码2-目的地三字码2。
    // 示例：898-2198315770,HFE-XIY;898-2198315771,PXK-HAK
    private String ticketInfos;

    // 退票类型
    private String refundType;

    // 退票具体原因。如之前选择的是改期后退票，则增加两个小项选择：“自愿退票”、“非自愿退票”；如之前选择的是降舱、备降/经停取消退费，则增加两个小项选择：“降舱”、“备降/经停地取消”，为必选项，旅客选择后，带到后台生成的订单中展示；
    private String refundReason;

    // 小程序端上传的图片 url。多个 url 用英文逗号隔开
    private String imgUrls;

    // 持卡人姓名
    private String cardHolder;

    // 银行卡号
    private String cardNo;

    // 开户行名称
    private String bankName;

    // 联系人电话
    private String mobileNo;

    // 退票单号
    private String fltRefundNo;

    // 备注信息
    private String remark;

    // 签名
    private String sign;

    // 是否国际票
    private Boolean inter;

}
