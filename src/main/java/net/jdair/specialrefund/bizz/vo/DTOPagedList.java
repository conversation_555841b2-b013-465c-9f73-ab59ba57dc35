
package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Data
public class DTOPagedList<T> implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1573919494972028371L;

	private List<T> list=new ArrayList<T>();
	
	//最大页号

	private int maxPageNumber;
	
	//返回的结果所在页号

	private int currentPageNumber;
	
	//总结果总数
	private Long totalResultCount;

}
