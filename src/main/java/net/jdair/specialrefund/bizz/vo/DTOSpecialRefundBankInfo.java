package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 特殊退票银行卡信息
 */
@Data
public class DTOSpecialRefundBankInfo implements Serializable {

	private static final long serialVersionUID = 7621405761799086555L;

	private Long id;
	private String refundNo;   // 退票单号
	private String cardHolder; // 持卡人姓名
	private String cardNo;     // 卡号
	private String bankName;   // 开户行名称
	private String mobileNo;   // 联系人手机号
	private String nbkno;   // 联行号
	private String remark;   // 管理员代客提交时填写的备注信息（传给易生代付接口，方便在易生后台查看）

}
