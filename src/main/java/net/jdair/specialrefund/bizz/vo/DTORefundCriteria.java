

package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.util.Date;

/**
 *<AUTHOR>
 *@description
 *
 * TODO your comment for 
 *
 *@date 2008-10-9
 */
@Data
public class DTORefundCriteria implements Serializable
{
	/**
	 * log instance,will be called to write down system log
	 */
	private static Log log = LogFactory.getLog(DTORefundCriteria.class);

	private String refundNo;
	private Date startDate;
	private Date endDate;
	private String status;
	private String ticketNo;
	private String officNo;
	private String pnrNo;
	private String passenerName;
	private String flightNo;
	private Date flightStartDate;
	private Date flightEndDate;
	private String depCode;
	private String arrCode;
	private String userName;
	private String userType;
	private Date auditStart;
	private Date auditStart2;
	private Date auditEnd;
	private Date auditEnd2;
	private String refundType;
	private String orderSource;
	private String auditUserName;
	private String jjcType;
	private String useCouponDJQ;

	private String cabinDetail;	
	private Date bookTime;

	private String constraintReason;
	private String fictitious;

	private String payMethod;
	
	//用于标示,是否为7月26号 之前出票,对应页面中的 "出票时间"
	//""为全部,   "before"为7月26号前,  "After"为7月26号后
	private String ticketBookTimeStatus;

	//仓位
	private String cabinClass;
	
	/**
	 * 当前页号 例如（第1页/共20页）则 currentPageNum 为 1
	 */
	private int currentPageNum;
	
	/**
	 * 每页记录数
	 */
	private int pageSize;
	/**
	 * 查询类型：一审,FIRSTAUDIT;二审,SECONDAUDIT
	 */
	private String searchType;

	private String beginBookTime;
	private String endBookTime;

	/**
	 * 退款日期
	 */
	private Date payDateStart;
	private Date payDateEnd;

}
