package net.jdair.specialrefund.bizz.vo;

import lombok.Data;
import net.jdair.specialrefund.bizz.controller.backend.page.PageDomain;

import java.util.List;

/**
 * 退票 报表列表查询请求
 */
@Data
public class SearchRefundReportQueryReq extends PageDomain {

    private String refundDateStart;
    private String refundDateEnd;
    private String flightDateStart;
    private String flightDateEnd;
    private String auditDateStart;
    private String auditDateEnd;
    private String payTimeStart;
    private String payTimeEnd;
    private String auditStatus;
    private String refundType;
    private String depCode;
    private String arrCode;
    private String orderSource;

}