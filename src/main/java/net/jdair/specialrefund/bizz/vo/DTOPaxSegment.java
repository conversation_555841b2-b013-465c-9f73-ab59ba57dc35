package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DTOPaxSegment extends DTOSegment {
    private Long passengerId;
    private Long segmentId;
    private DTOPassenger dtoPassenger;
    private DTOTicket ticketIssued;
    private DTOProduct productApplied;
    private double marketFare;
    private double netFare;
    private double airportTax;
    private double fuelTax;
    private double otherTaxes;
    private double insurance;
    private double agentFeeRate;
    private double spFeeRate;
    private double agentFee;
    private double spFee;
    private String fareBasis;
    private Date notValidBefore;
    private Date notValidAfter;
    private String allow;
    private SegmentStatus status;
    private TicketStatus ticketStatus;
    private String currency;
    private String changeReferenceCabin;
    private String hasFreeTicket;
    private String usedFreeTicket;
    private String usrdFlow;
    private String payFreeTicket;
    private int sequence;
    private Integer point;
    private String dayAndMoth;
    private String hourAndmin;
    private DTOSpecialRefundOriginTkt dtoSpecialRefundOriginTkt;
    private List<DTOPaxsegTax> paxsegTaxList;
    private BigDecimal couponPrice;
    private BigDecimal oldNetFare;
    private Long orderId;
    private double pointPrice;
    private double realRebate;
    private String oldTicketNo;
    private DTOProduct oldProduct;
    private String oldCabin;
    private String oriProductCode;
    private BigDecimal oldBaseFare;
}
