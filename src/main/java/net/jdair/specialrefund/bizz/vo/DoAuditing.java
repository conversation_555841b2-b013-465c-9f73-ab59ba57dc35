

package net.jdair.specialrefund.bizz.vo;

/**
 * <AUTHOR>
 * @description TODO your comment for
 * @date 2008-9-3
 */
public enum DoAuditing {
    FIRSTAUDIT("FIRSTAUDIT"), //一审

    SECONDAUDIT("SECONDAUDIT"),//二审

    /**
     * 审核状态
     */
    NEW("NEW"), //一审通过
    FPASS("FPASS"), //一审通过
    PASS("PASS"), //二审通过
    PAID("PAID"),//已退款
    PEND("PEND"),
    REJECT("REJECT"),
    FREJECT("FREJECT"),//一审拒绝
    FAIL("FAIL"),//退款失败


    ONLINE("ONLINE"), //在线支付
    OFFLINE("OFFLINE"),//线下支付

    INTRADAY("INTRADAY"),//当日作废
    UNCONSTRAINT("UNCONSTRAINT"),//自愿退票

    CONSTRAINT("CONSTRAINT"),//非自愿退票

    WAIT_OFFLINE("WAIT_OFFLINE"), // 特殊退票，【待线下退款】状态

    CLOSED("CLOSED"), // 特殊退票，【已关闭】状态
    CHANGE_REFUND_TYPE("CHANGE_REFUND_TYPE"),

    UNCON_ATUO_SPASS("UNCON_ATUO_SPASS"),//自愿自动二审

    CON_ATUO_SPASS("CON_ATUO_SPASS"),//非自愿自动二审

    UNCON_AUTO_REFUND("UNCON_AUTO_REFUND"),//自愿自动退款

    CON_AUTO_REFUND("CON_AUTO_REFUND");//非自愿自动退款

    private final String value;

    private DoAuditing(String value) {
        this.value = value;

    }

    public String getValue() {
        return this.value;
    }

}
