package net.jdair.specialrefund.bizz.vo;

import lombok.Data;

import java.util.Date;

@Data
public class DTOSegment {

    private Long id;
    private String depCode;
    private String arrCode;
    private String airlineCode;
    private String flightNo;
    private String cabinClass;
    private Date depTime;
    private Date depTimeforTQ;
    private Date arrTime;
    private String planeType;
    private String stop;
    private String productCode;
    private String offerItemID;
    private String cabinState;
    private TripType tripType;
    private double cabinPrice;
    private double baseFare;
    private Integer point;
    private String referenceCabin;
    private double referenceFare = 0.0D;
    private Date timeLimit;
    private String lowstCabin;
    private String cabinCodeDesc;
    private String oriProductCode;
    private String origCabinCode;
    private String arrterminal;
    private String depterminal;
}
