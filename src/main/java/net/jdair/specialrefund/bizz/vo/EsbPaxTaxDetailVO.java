package net.jdair.specialrefund.bizz.vo;

import java.util.List;

/**
 * ODS EBS 获取国际税费接口返回对象
 */
public class EsbPaxTaxDetailVO {

    // 错误代码
    private String ErrorCode;
    // 消息说明
    private String Message;
    // 结果集
    private List<PaxTaxDetail> Result;


    /**
     * resltList->代理人代码	AGENTCODE	String	VARCHAR2(8)
     * resltList->旅客姓名	P_NAME	String	VARCHAR2(60)
     * resltList->日报票款	D_FARE	String	NUMBER(11,2)
     * resltList->出票日期	ISS_DATE	String	DATE
     * resltList->关联票号明细	TKT_STR	String	VARCHAR2(4000)
     * resltList->主结算票号	M_TNO	String	VARCHAR2(15)
     * resltList->出票公司	ISS_CO	String	VARCHAR2(4)
     * resltList->税代码	TAX_CODE	String	VARCHAR2(20)
     * resltList->币种代码	CUR_CODE	String	CHAR(3)
     * resltList->税费金额	TAX_FEE	String	NUMBER(11,2)
     */
    public static class PaxTaxDetail {
        // 代理人代码
        private String AGENTCODE;

        // 旅客姓名
        private String P_NAME;

        // 日报票款
        private Double D_FARE;

        // 出票日期
        private String ISS_DATE;

        // 关联票号明细
        private String TKT_STR;

        // 主结算票号
        private String M_TNO;

        // 出票公司
        private String ISS_CO;

        // 税代码
        private String TAX_CODE;

        // 币种代码
        private String CUR_CODE;

        // 税费金额
        private Double TAX_FEE;

        public String getAGENTCODE() {
            return AGENTCODE;
        }

        public void setAGENTCODE(String AGENTCODE) {
            this.AGENTCODE = AGENTCODE;
        }

        public String getP_NAME() {
            return P_NAME;
        }

        public void setP_NAME(String p_NAME) {
            P_NAME = p_NAME;
        }

        public Double getD_FARE() {
            return D_FARE;
        }

        public void setD_FARE(Double d_FARE) {
            D_FARE = d_FARE;
        }

        public String getISS_DATE() {
            return ISS_DATE;
        }

        public void setISS_DATE(String ISS_DATE) {
            this.ISS_DATE = ISS_DATE;
        }

        public String getTKT_STR() {
            return TKT_STR;
        }

        public void setTKT_STR(String TKT_STR) {
            this.TKT_STR = TKT_STR;
        }

        public String getM_TNO() {
            return M_TNO;
        }

        public void setM_TNO(String m_TNO) {
            M_TNO = m_TNO;
        }

        public String getISS_CO() {
            return ISS_CO;
        }

        public void setISS_CO(String ISS_CO) {
            this.ISS_CO = ISS_CO;
        }

        public String getTAX_CODE() {
            return TAX_CODE;
        }

        public void setTAX_CODE(String TAX_CODE) {
            this.TAX_CODE = TAX_CODE;
        }

        public String getCUR_CODE() {
            return CUR_CODE;
        }

        public void setCUR_CODE(String CUR_CODE) {
            this.CUR_CODE = CUR_CODE;
        }

        public Double getTAX_FEE() {
            return TAX_FEE;
        }

        public void setTAX_FEE(Double TAX_FEE) {
            this.TAX_FEE = TAX_FEE;
        }
    }


    public String getErrorCode() {
        return ErrorCode;
    }

    public void setErrorCode(String errorCode) {
        ErrorCode = errorCode;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String message) {
        Message = message;
    }

    public List<PaxTaxDetail> getResult() {
        return Result;
    }

    public void setResult(List<PaxTaxDetail> result) {
        Result = result;
    }
}