package net.jdair.specialrefund.bizz.vo;

import java.util.HashMap;

public enum Gender {
    MALE("M"),
    FEMALE("F");

    private final String alias;
    private static HashMap<String, Gender> map = new HashMap();

    static {
        Gender[] var3;
        int var2 = (var3 = values()).length;

        for(int var1 = 0; var1 < var2; ++var1) {
            Gender var0 = var3[var1];
            map.put(var0.alias, var0);
        }

    }

    private Gender(String var3) {
        this.alias = var3;
    }

    public String toString() {
        return this.alias;
    }

    public static Gender valueOfAlias(String var0) {
        Gender var1 = (Gender)map.get(var0);
        if (var1 == null) {
            throw new IllegalArgumentException("Unknown gender alias [" + var0 + "]");
        } else {
            return var1;
        }
    }


}
