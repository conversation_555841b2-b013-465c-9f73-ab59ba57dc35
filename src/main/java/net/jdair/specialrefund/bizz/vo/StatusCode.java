
package net.jdair.specialrefund.bizz.vo;

/** 
 * @ClassName:	StatusCode 
 * @Description: 错误信息常量类 
 * @author:	wxp
 * @date:	2015年6月4日 下午9:41:21 
 *  
 */
public enum StatusCode {

	/** 
	 * @Fields SUCCESS : 请求成功 
	 */ 
	SUCCESS("", "success"),
	
	/** 
	 * @Fields FAIL : 请求失败 
	 */ 
	FAIL("", "fail"),
	
	/** 
	 * @Fields UNLOGIN : 未登录 
	 */ 
	UNLOGIN("4001","用户没有登录"),
	
	/** 
	 * @Fields LOGIN_ERROR : 登录错误 
	 */ 
	LOGIN_ERROR("4002","用户名或密码错误"),
	
	/**
	 * 网络连接错误
	 */
	CANNOT_CONNECT("4203","网络连接有误，请您检查网络"),
	
	/**
	 * 航班查询接口参数错误码
	 */
	SEARCH_FLIGHT_ORGCITY_NULL("4003","航班查询参数“orgCity”不能为空"),
	SEARCH_FLIGHT_ORGCITY_ERROR("4004","航班查询参数“orgCity”格式错误，正确格式形如：PEK"),
	SEARCH_FLIGHT_DSTCITY_NULL("4005","航班查询参数“dstCity”不能为空"),
	SEARCH_FLIGHT_DSTCITY_ERROR("4006","航班查询参数“dstCity”格式错误，正确格式形如：HAK"),
	SEARCH_FLIGHT_AIRLINECODE_NULL("4007","航班查询参数“airlineCode”不能为空"),
	SEARCH_FLIGHT_AIRLINECODE_ERROR("4008","航班查询参数“airlineCode”格式错误，正确格式形如：JD"),
	SEARCH_FLIGHT_FLIGHTDATE_NULL("4009","航班查询参数“flightDate”不能为空"),
	SEARCH_FLIGHT_FLIGHTDATE_ERROR("4010","航班查询参数“flightDate”格式错误，正确格式形如：2015-06-01"),
	SEARCH_FLIGHT_RETURN_FLIGHTDATE_NULL("4011","航班查询参数“returnDate”不能为空"),
	SEARCH_FLIGHT_RETURN_FLIGHTDATE_ERROR("4012","航班查询参数“returnDate”格式错误，正确格式形如：2015-06-02"),
	SEARCH_FLIGHT_TRIPTYPE_NULL("4013","航班查询参数“tripType”不能为空"),
	SEARCH_FLIGHT_TRIPTYPE_ERROR("4014","航班查询参数“tripType”值错误"),
	SEARCH_FLIGHT_DATE_LOGIC_ERROR("4015","返程日期不能早于出发日期"),
	SEARCH_FLIGHT_AGENT_ERROR("4016","您好，代理人购票系统已升级，如需购票请访问http://b2b.jdair.net/"),
	SELECT_FLIGHT_DATE_ERROR("4017","请检查航段日期先后顺序"),
	SELECT_FLIGHT_NO_FLIGHT_ROUTE_ERROR("4018","非常抱歉，您查询的航班无直达航班，您可购买其他首航自营航线机票！"),



	/**
	 * 航班查询接口参数错误码
	 */
	SPECIAL_REFUND_FLIGHTDATE_NULL("7001","航班日期不能为空"),
	SPECIAL_REFUND_FLIGHTDATE_ERROR("7002","航班日期格式错误"),
	SPECIAL_REFUND_OPENID_NULL("7003","用户不能为空"),
	SPECIAL_REFUND_NAME_NULL("7004","姓名不能为空"),
	SPECIAL_REFUND_CERTNO_TICKETNO_NULL("7005","证件号/票号不能为空"),
	SPECIAL_REFUND_CERTNO_TICKETNO_ERROR("7006","证件号/票号格式错误"),
	SPECIAL_REFUND_TYPE_NULL("7007","退票类型不能为空"),
	SPECIAL_REFUND_TYPE_ERROR("7008","退票类型格式错误"),
	SPECIAL_REFUND_SIGN_ERROR("7009","签名错误"),
	SPECIAL_REFUND_TICKETINFOS_NULL("7010","客票信息不能为空"),
	SPECIAL_REFUND_TICKETINFOS_ERROR("7011","客票信息格式错误"),
	SPECIAL_REFUND_DEPCODE_NULL("7012","出发地不能为空"),
	SPECIAL_REFUND_DEPCODE_ERROR("7013","出发地格式错误"),
	SPECIAL_REFUND_ARRCODE_NULL("7014","目的地不能为空"),
	SPECIAL_REFUND_ARRCODE_ERROR("7015","目的地格式错误"),
	SPECIAL_REFUND_CARDHOLDER_NULL("7016","持卡人姓名不能为空"),
	SPECIAL_REFUND_CARDNO_NULL("7017","银行卡号不能为空"),
	SPECIAL_REFUND_CARDNO_ERROR("7018","银行卡号格式错误"),
	SPECIAL_REFUND_BANKNAME_NULL("7019","开户行名称不能为空"),
	SPECIAL_REFUND_MOBILENO_NULL("7020","联系人手机号不能为空"),
	SPECIAL_REFUND_MOBILENO_ERROR("7021","联系人手机号格式错误"),
	SPECIAL_REFUND_SUBMIT_ERROR("7022","提交退票失败，请您重新查询后重试"),
	SPECIAL_REFUND_REFUNDNO_NULL("7023","退票单号不能为空"),
	SPECIAL_REFUND_REFUNDNO_ERROR("7024","退票单号格式错误"),
	SPECIAL_REFUND_EXIST_TICKET_ERROR("7025","您的客票已提交过退票申请，请勿重复提交！"),
	SPECIAL_REFUND_CREATE_ERROR("7026","提交退票失败，系统异常，请稍后再试"),
	SPECIAL_REFUND_PAGE_INDEX_ERROR("7027","pageIndex 错误"),
	SPECIAL_REFUND_PAGE_SIZE_ERROR("7028","pageSize 错误"),
	SPECIAL_REFUND_ORDERDETAIL_ERROR("7029","退票单号不存在"),
	SPECIAL_REFUND_NO_FLIGHT_ERROR("7030","未找到符合条件的客票，建议您通过填写准确的票号进行提交申请。"),
	SPECIAL_REFUND_NOT_EXIST_NULL("7031","退票单号不能为空"),
	SPECIAL_REFUND_STATUS_ERROR("7032","非拒绝状态，无法提交"),
	SPECIAL_REFUND_SUBMITAGAIN_ERROR("7033","提交失败，请稍后重试"),
	SPECIAL_REFUND_REASON_ERROR("7034","退票原因错误"),
	SPECIAL_REFUND_CARDHOLDER_NOT_MATCH("7035","持卡人姓名须为旅客本人"),
	SPECIAL_REFUND_ADMIN_FILE_TOO_LARGE("7036","上传文件的大小错误， 单个文件大小不能大于4M!"),
	SPECIAL_REFUND_ADMIN_FILE_FORMAT_ERROR("7037","上传文件格式只能是bmp，jpg，jpeg,png。"),
	SPECIAL_REFUND_NBKNO_NULL("7038","对公账号联行号不能为空"),
	SPECIAL_REFUND_TICKETNO_IN_BLACKLIST("7039","此票号已在其他渠道进行过退票处理，不再支持线上提交特殊退票，请进行核实"),
	SPECIAL_REFUND_TICKETNO_NULL("7040","票号不能为空"),
	SPECIAL_REFUND_TICKETNO_ERROR("7041","票号格式错误"),
	SPECIAL_REFUND_GT_INFO_MATCH_ERROR("7042","目前所选的旅客航段不一致，请确认选择相同航段再进行提交"),
	SPECIAL_REFUND_GT_SIZE_ERROR("7043","最多可填写9张客票"),
	SPECIAL_REFUND_GT_SAME_NAME_ERROR("7044","旅客姓名填写重复，请检查"),
	SPECIAL_REFUND_GT_SAME_TICKET_ERROR("7045","票号填写重复，请检查"),
	SPECIAL_REFUND_SUBMIT_INTER_PARAM_ERROR("7047","系统异常，无法提交"),
	SPECIAL_REFUND_SUBMIT_GJ_ESB_ERROR("7046","获取信息失败，请您重新查询后重试"),


	/**
	 * 补偿功能相关错误码
	 */
	COMPENSATE_CREATE_ORDER_ERROR("7101","补偿单创建失败，系统异常，请稍后再试"),
	COMPENSATE_SIGN_ERROR("7102","签名错误"),
	COMPENSATE_OPENID_NULL("7103","OPENID不能为空"),
	COMPENSATE_COMPENSATENO_NULL("7104","补偿单号不能为空"),
	COMPENSATE_COMPENSATENO_FORMAT_ERROR("7105","补偿单格式错误"),
	COMPENSATE_TICKETINFOS_NULL("7106","客票信息不能为空"),
	COMPENSATE_TICKETINFOS_FORMAT_ERROR("7107","客票信息格式错误"),
	COMPENSATE_CARDHOLDER_NULL("7108","持卡人姓名不能为空"),
	COMPENSATE_MOBILENO_NULL("7109","手机号不能为空"),
	COMPENSATE_MOBILENO_FORMAT_ERROR("7110","手机号格式错误"),
	COMPENSATE_CARDNO_NULL("7111","银行卡号不能为空"),
	COMPENSATE_CARDNO_FORMAT_ERROR("7112","银行卡号格式错误"),
	COMPENSATE_BANKNAME_NULL("7113","开户行名称不能为空"),
	COMPENSATE_FLIGHTDATE_NULL("7114","航班日期不能为空"),
	COMPENSATE_FLIGHTDATE_FORMAT_ERROR("7115","航班日期格式错误"),
	COMPENSATE_TICKETNO_CERTNO_NULL("7116","客票号/证件号不能为空"),
	COMPENSATE_TICKETNO_CERTNO_FORMAT_ERROR("7117","客票号/证件号格式错误"),
	COMPENSATE_DEPARTMENT_NULL("7118","部门参数不能为空"),
	COMPENSATE_COMPENSATE_TYPE_NULL("7119","补偿类型不能为空"),
	COMPENSATE_COMPENSATE_AMOUNT_NULL("7120","补偿金额不能为空"),
	COMPENSATE_PSGNAME_NULL("7121","旅客姓名不能为空"),
	COMPENSATE_COMPENSATENO_EMPTY("7122","补偿单号不存在"),

	QUERY_BASE_INFO_ERROR("50001","三字码格式错误"),



	/** 
	 * 调用Mars Servcie通用错误码
	 */ 
	MARS_ERROR("5001","系统后台异常"),

	/**
	 * 系统错误
	 */
	SYSTEM_ERROR("5002","系统异常，请稍后再试"),
	
	/** 
	 * 恶意访问限制
	 */ 
	SEARCH_FLIGHT_TIME_LIMIT("6001","查询航班次数过于频繁，请稍候再试");

	private String msg;
	private String code;

	StatusCode(String code, String msg) {
		this.code=code;
		this.msg=msg;
	}

	public String getMsg() {
		return this.msg;
	}

	public String getCode() {
		return this.code;
	}

}
