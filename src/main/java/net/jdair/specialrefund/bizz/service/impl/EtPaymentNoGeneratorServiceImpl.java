package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtPaymentNoGenerator;
import net.jdair.specialrefund.bizz.service.EtPaymentNoGeneratorService;
import net.jdair.specialrefund.bizz.mapper.EtPaymentNoGeneratorMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_payment_no_generator(退款支付流水号生成表)】的数据库操作Service实现
* @createDate 2025-02-25 09:32:48
*/
@Service
public class EtPaymentNoGeneratorServiceImpl extends ServiceImpl<EtPaymentNoGeneratorMapper, EtPaymentNoGenerator>
    implements EtPaymentNoGeneratorService{

}




