package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtTsTicketnoBlacklist;
import net.jdair.specialrefund.bizz.service.EtTsTicketnoBlacklistService;
import net.jdair.specialrefund.bizz.mapper.EtTsTicketnoBlacklistMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_ts_ticketno_blacklist】的数据库操作Service实现
* @createDate 2022-03-31 16:56:18
*/
@Service
public class EtTsTicketnoBlacklistServiceImpl extends ServiceImpl<EtTsTicketnoBlacklistMapper, EtTsTicketnoBlacklist>
    implements EtTsTicketnoBlacklistService{

}




