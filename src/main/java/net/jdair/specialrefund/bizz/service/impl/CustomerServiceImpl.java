package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.Customer;
import net.jdair.specialrefund.bizz.service.CustomerService;
import net.jdair.specialrefund.bizz.mapper.CustomerMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer】的数据库操作Service实现
* @createDate 2022-03-31 16:56:04
*/
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer>
    implements CustomerService{

}




