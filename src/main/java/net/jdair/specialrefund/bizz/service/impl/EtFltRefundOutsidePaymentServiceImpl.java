package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtFltRefundOutsidePayment;
import net.jdair.specialrefund.bizz.service.EtFltRefundOutsidePaymentService;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundOutsidePaymentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_flt_refund_outside_payment】的数据库操作Service实现
* @createDate 2022-03-31 16:56:17
*/
@Service
public class EtFltRefundOutsidePaymentServiceImpl extends ServiceImpl<EtFltRefundOutsidePaymentMapper, EtFltRefundOutsidePayment>
    implements EtFltRefundOutsidePaymentService{

}




