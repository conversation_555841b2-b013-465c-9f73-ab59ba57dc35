package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtPassenger;
import net.jdair.specialrefund.bizz.service.EtPassengerService;
import net.jdair.specialrefund.bizz.mapper.EtPassengerMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_passenger】的数据库操作Service实现
* @createDate 2022-03-31 16:56:18
*/
@Service
public class EtPassengerServiceImpl extends ServiceImpl<EtPassengerMapper, EtPassenger>
    implements EtPassengerService{

}




