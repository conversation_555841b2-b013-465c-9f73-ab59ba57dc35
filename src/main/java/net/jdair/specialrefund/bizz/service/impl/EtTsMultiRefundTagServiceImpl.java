package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag;
import net.jdair.specialrefund.bizz.service.EtTsMultiRefundTagService;
import net.jdair.specialrefund.bizz.mapper.EtTsMultiRefundTagMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_ts_multi_refund_tag(特殊退票重复退票熔断标识表)】的数据库操作Service实现
* @createDate 2025-02-19 13:50:50
*/
@Service
public class EtTsMultiRefundTagServiceImpl extends ServiceImpl<EtTsMultiRefundTagMapper, EtTsMultiRefundTag>
    implements EtTsMultiRefundTagService{

}




