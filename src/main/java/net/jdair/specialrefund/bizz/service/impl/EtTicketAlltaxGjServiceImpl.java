package net.jdair.specialrefund.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.jdair.specialrefund.bizz.domain.EtTicketAlltaxGj;
import net.jdair.specialrefund.bizz.service.EtTicketAlltaxGjService;
import net.jdair.specialrefund.bizz.mapper.EtTicketAlltaxGjMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【et_ticket_alltax_gj】的数据库操作Service实现
* @createDate 2023-01-10 10:24:29
*/
@Service
public class EtTicketAlltaxGjServiceImpl extends ServiceImpl<EtTicketAlltaxGjMapper, EtTicketAlltaxGj>
    implements EtTicketAlltaxGjService{

}




