package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord;
import net.jdair.specialrefund.bizz.domain.InterRefundMonitorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InterRefundMonitorMapper extends BaseMapper<InterRefundMonitorRecord> {

    /**
     * 查询当前支付记录最大ID
     * @return
     */
    Long queryMaxPaymentId();

    /**
     * 查询 同一个订单产生二次退款（二审通过或已退款）
     * @return
     */
    List<InterRefundMonitorRecord> queryByMultiPayment(@Param("preMaxPaymentId") Long preMaxPaymentId);

}
