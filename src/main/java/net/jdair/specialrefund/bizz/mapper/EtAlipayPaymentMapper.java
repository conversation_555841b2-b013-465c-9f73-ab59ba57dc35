package net.jdair.specialrefund.bizz.mapper;

import net.jdair.specialrefund.bizz.domain.EtAlipayPayment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【et_payment_alip】的数据库操作Mapper
* @createDate 2023-01-11 15:30:12
* @Entity net.jdair.specialrefund.bizz.domain.EtAlipayPayment
*/
public interface EtAlipayPaymentMapper extends BaseMapper<EtAlipayPayment> {

    /**
     * 根据ID查询
     * @param paymentId
     * @return
     */
    public EtAlipayPayment findById(@Param("paymentId") Long paymentId);
}




