package net.jdair.specialrefund.bizz.mapper;

import net.jdair.specialrefund.bizz.domain.EtPassengerSegment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【et_passenger_segment】的数据库操作Mapper
* @createDate 2022-03-31 16:56:18
* @Entity net.jdair.specialrefund.bizz.domain.EtPassengerSegment
*/
public interface EtPassengerSegmentMapper extends BaseMapper<EtPassengerSegment> {

    List<EtPassengerSegment> findByRefundId(Long refundId);
}




