package net.jdair.specialrefund.bizz.mapper;

import net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【et_flt_refund_pax_seg】的数据库操作Mapper
* @createDate 2022-03-31 16:56:17
* @Entity net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg
*/
public interface EtFltRefundPaxSegMapper extends BaseMapper<EtFltRefundPaxSeg> {

    List<EtFltRefundPaxSeg> getByRefundId(Long id);

}




