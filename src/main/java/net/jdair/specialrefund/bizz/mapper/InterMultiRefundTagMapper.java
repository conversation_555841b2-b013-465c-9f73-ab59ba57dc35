package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag;
import net.jdair.specialrefund.bizz.domain.InterMultiRefundTag;

/**
* <AUTHOR>
* @description 针对表【inter_multi_refund_tag(国际票海外站重复退票熔断标识表)】的数据库操作Mapper
* @createDate 2025-02-19 13:50:50
* @Entity net.jdair.specialrefund.bizz.domain.InterMultiRefundTag
*/
public interface InterMultiRefundTagMapper extends BaseMapper<InterMultiRefundTag> {

}




