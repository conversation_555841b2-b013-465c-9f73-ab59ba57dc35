package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundReport;
import net.jdair.specialrefund.bizz.vo.SearchRefundReportQueryReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SpecialRefundReportMapper extends BaseMapper<DTOSpecialRefundReport> {

    List<DTOSpecialRefundReport> querySpecialRefundReport(@Param("criteria") SearchRefundReportQueryReq criteria);
}
