package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListRes;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【et_flt_refund】的数据库操作Mapper
* @createDate 2022-03-31 16:56:17
* @Entity net.jdair.specialrefund.bizz.domain.EtFltRefund
*/
public interface EtFltRefundMapper extends BaseMapper<EtFltRefund> {

    /**
     * 条件查询退票单列表
     * @param req
     * @return
     */
    List<EtFltRefund> findRefundByParams(SearchRefundOrderListReq req);

    IPage<EtFltRefund> findRefundListByParamsForPage(Page page, @Param("param") SearchRefundOrderListReq req);

    List<EtFltRefund> findRefundDetailByIdsForPage(@Param("refundIds") List<Long> refundIds);

}




