package net.jdair.specialrefund.bizz.mapper;

import net.jdair.specialrefund.bizz.domain.Customer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.utils.EncoderUtil;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【customer】的数据库操作Mapper
* @createDate 2022-03-31 16:56:04
* @Entity net.jdair.specialrefund.bizz.domain.Customer
*/
public interface CustomerMapper extends BaseMapper<Customer> {

    Customer findCustomerByNameAndId(@Param("name") String name,
                                     @Param("certificateNo") String certificateNo,
                                     @Param("certificateType") String certificateType);
}




