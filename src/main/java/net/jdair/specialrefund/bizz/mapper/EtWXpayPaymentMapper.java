package net.jdair.specialrefund.bizz.mapper;

import net.jdair.specialrefund.bizz.domain.EtWXpayPayment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【et_payment_wxpay】的数据库操作Mapper
* @createDate 2023-01-11 15:30:52
* @Entity net.jdair.specialrefund.bizz.domain.EtWXpayPayment
*/
public interface EtWXpayPaymentMapper extends BaseMapper<EtWXpayPayment> {

    /**
     * 根据ID查询
     * @param paymentId
     */
    public EtWXpayPayment findById(@Param("paymentId") Long paymentId);

}




