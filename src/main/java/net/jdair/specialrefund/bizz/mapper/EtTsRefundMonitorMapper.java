package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EtTsRefundMonitorMapper extends BaseMapper<EtTsRefundMonitorRecord> {

    /**
     * 查询当前支付记录最大ID
     * @return
     */
    Long queryMaxPaymentId();

    /**
     * 查询 同一个订单产生二次退款（二审通过或已退款）
     * @return
     */
    List<EtTsRefundMonitorRecord> queryByMultiPayment(@Param("preMaxPaymentId") Long preMaxPaymentId);

    /**
     * 同一个金额、同一收款账号（二审通过或已退款）
     * @return
     */
    List<EtTsRefundMonitorRecord> queryBySameCardAndAmount(@Param("preMaxPaymentId") Long preMaxPaymentId);

    /**
     * 票号、退单号、收款人、收款账号、金额均相同
     * @param preMaxPaymentId
     * @return
     */
    List<EtTsRefundMonitorRecord> queryByAllSame(@Param("preMaxPaymentId") Long preMaxPaymentId);

}
