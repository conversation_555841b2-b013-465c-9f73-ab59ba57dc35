package net.jdair.specialrefund.bizz.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.jdair.specialrefund.bizz.domain.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

//@DS("oracle")
@Mapper
public interface EtTsPGSMapper extends BaseMapper<EtFltRefund> {

    //@DS("oracle")
    List<EtFltRefund> findRefundByTime(
            @Param("createTimeStart") String createTimeStart,
            @Param("createTimeEnd") String createTimeEnd
    );

    //@DS("oracle")
    EtFltRefund findRefundByRefundNo(@Param("refundNo") String refundNo);

    List<EtFltRefundPaxSeg> findPaxSegByRefundId(@Param("refundId") Long refundId);

    List<EtPassengerSegment> findPsByPaxSegId(@Param("paxSegId") Long paxSegId);

    List<EtPassenger> findPByPId(@Param("pId") Long pId);

    List<Customer> findCByCId(@Param("cId") Long cId);

    List<EtPnr> findPnrByPnrId(@Param("pnrId") Long pnrId);

    List<EtSegment> findSBySId(@Param("sId") Long sId);

    List<EtTicket> findTByTId(@Param("tId") Long tId);

    List<EtFltRefundAudit> findAuditByRefundId(@Param("refundId") Long refundId);

    List<EtFltRefundOutsidePayment> findOutPaymentByRefundId(@Param("refundId") Long refundId);

    List<EtPayment> findPayPaymentById(@Param("paymentId") Long paymentId);

    List<EtFltRefundPayment> findRefundPaymentByRefundId(@Param("refundId") Long refundId);

    EtSpecialRefundBankInfo findBankByRefundId(@Param("refundId") Long refundId);

    List<EtSpecialRefundImg> findImgByRefundId(@Param("refundId") Long refundId);

    List<EtPayment> findRefundPaymentById(@Param("paymentId") Long paymentId);

    EtSpecialRefundOriginTkt findOriginByPaxSegId(@Param("paxSegId") Long paxSegId);

}
