package net.jdair.specialrefund.bizz.utils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtil {

	private static final Logger log = Logger.getLogger(DateUtil.class);

    public DateUtil() {
    }

    /*TODO 日期格式化
     *@param date 日期
     *@param format 日期的格式，如:yyyy-MM-dd HH:mm:ss SSS
     */
    public static String formatDate(Date date, String format)  {
        if (date == null) {
            return "";
        }
        if (format == null || format.equals("")) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String dateStr = sdf.format(date);
        return dateStr;
    }
    
    public static Date parse(String source , String format){
    	try{
	    	if(StringUtils.isEmpty(source)){
	    		return null;
	    	}
	    	
	        if (format == null || format.equals("")) {
	            format = "yyyy-MM-dd";
	        }
	        SimpleDateFormat sdf = new SimpleDateFormat(format);
	        return sdf.parse(source);
    	}catch(Exception e){
    		
    	}
    	return null;
    }

	public static Date parseWithTimeZone(String source , String format){
		try{
			if(StringUtils.isEmpty(source)){
				return null;
			}

			if (format == null || format.equals("")) {
				format = "yyyy-MM-dd";
			}
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			log.info("parseWithTimeZone:" + sdf.getTimeZone());
			return sdf.parse(source);
		}catch(Exception e){

		}
		return null;
	}
    
    public static String formatDateString(String dateString,String format)throws Exception{
    	if(dateString.length() == 10){
    		if(format.equals("yyyy-MM-dd"))
    			return dateString;
    		if(format.equals("yyyy-MM-dd HH:mm"))
    			return dateString +" "+ "00:00";
    		if(format.equals("yyyy-MM-dd HH:mm:ss"))
    			return dateString +" "+ "00:00:00";
    	}
    	if(dateString.length() == 16){
    		if(format.equals("yyyy-MM-dd"))
    			return dateString.substring(0, 10);
    		if(format.equals("yyyy-MM-dd HH:mm"))
    			return dateString;
    		if(format.equals("yyyy-MM-dd HH:mm:ss"))
    			return dateString + ":00";
    	}
    	if(dateString.length() == 19){
    		if(format.equals("yyyy-MM-dd"))
    			return dateString.substring(0, 10);
    		if(format.equals("yyyy-MM-dd HH:mm"))
    			return dateString.substring(0, 16);;
    		if(format.equals("yyyy-MM-dd HH:mm:ss"))
    			return dateString;
    	}
    	
    	throw new Exception("时间格式转换失败");
    }
    public static long timeTotalMinutes(Date date1,Date date2){	  
		  return (date1.getTime()-date2.getTime())/(60*1000);
	}
	/*
    * 将时间转换为时间戳
    */
	public static String dateToStamp(String s) throws ParseException {
		String res;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = simpleDateFormat.parse(s);
		long ts = date.getTime();
		res = String.valueOf(ts);
		return res;
	}
    public static Date StringTODateTime(Date source,String format) {// add by zhouhui

		Date obj = null;

		try {
			if (format == null || format.equals("")) {
	            format = "yyyy-MM-dd";
	        }
			SimpleDateFormat sdf = new SimpleDateFormat(format);

			String dateStr = sdf.format(source);// 构造Date对象
			obj= sdf.parse(dateStr);

		} catch (Exception e) {

			e.printStackTrace();

		}

		return obj;

	}
	//////////////////////////////////////////////////////////////////////////////////////////////////////////
	/*
	 * 将时间戳转换为时间字符串
	 */
	public static String StampTodateString(String s) throws ParseException {
		Long timeLong = Long.parseLong(s);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//要转换的时间格式
		Date date;
		String res="";
		try {
			date = sdf.parse(sdf.format(timeLong));
			res= sdf.format(date);
		} catch (Exception e) {
			e.printStackTrace();

		}
		return res;
	}
	
    public static void main(String args[]) throws Exception {
    	/**
        // 得到本地的缺省格式
        DecimalFormat df1 = new DecimalFormat(".000");
        //double testd=1234567.234667;
        // 得到德国的格式
        Locale.setDefault(Locale.GERMAN);
        DecimalFormat df2 = new DecimalFormat("####.000");
        YYYYMMDDHHmmssnnnn
        */
    	System.out.println(dateToStamp("2019-01-01 12:20:00"));
    }

}
