
package net.jdair.specialrefund.bizz.utils;

import com.travelsky.ibe.client.pnr.*;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.vo.IBEException;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
public class ShowTicket {

    private String airline;

    private static Map<String, ShowTicket> instances = new HashMap<String, ShowTicket>();

    private ShowTicket(String airline) {
        this.airline = airline;
    }

    public static ShowTicket getInstance(String airline) {
        if (!instances.containsKey(airline)) {
            ShowTicket st = new ShowTicket(airline);
            instances.put(airline, st);
        }
        return instances.get(airline);
    }

    /**
     * 提取客票信息
     *
     * @param ticketNo
     * @return
     * @auther 郑兴(zhengxing)
     * 2012-5-16
     */
    public String showTicketInfo(String ticketNo) {
        DETR detr = new DETR();
        PNRManage.configIBEClient(detr, airline);
        DETRTKTResult result = null;
        int times = 3;
        for (int i = 0; i < times; i++) {
            try {
                result = detr.getTicketInfoByTktNo(ticketNo);
                log.info("ShowTicket.showTicketInfo detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1)
                        + "|" + PNRUtils.getTxnid(detr) + "|error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + "|" + PNRUtils.getTxnid(detr) + " error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        if (null != result) {
            return result.toString();
        }
        return "";
    }

    /**
     * 返回客票状态
     *
     * @param ticketNo 客票 XXX-XXXXXXXXXX
     * @return
     * @auther chen-bing
     */
    public String getTicketStatus(String ticketNo) {
        DETR detr = new DETR();
        PNRManage.configIBEClient(detr, airline);
        DETRTKTResult result = null;
        int times = 3;
        String ticketstatus = null;
        for (int i = 0; i < times; i++) {
            try {
                result = detr.getTicketInfoByTktNo(ticketNo);
                log.info("ShowTicket.getTicketStatus detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) +
                        "|" + PNRUtils.getTxnid(detr) + ",error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + "|" + PNRUtils.getTxnid(detr) + "error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        if (null != result) {
            if (result.getSegmentCount() > 0) {
                ticketstatus = result.getSegment(0).getTicketStatus().replaceAll(" ", "");
            }
        }
        return ticketstatus;
    }

    /**
     * 返回客票状态
     *
     * @param ticketNo 客票 XXX-XXXXXXXXXX
     * @return
     * @auther chen-bing
     */
    public String getTicketStatus(String ticketNo, String depCode) {
        DETR detr = new DETR();
        PNRManage.configIBEClient(detr, airline);
        DETRTKTResult result = null;
        int times = 3;
        String ticketstatus = null;
        for (int i = 0; i < times; i++) {
            try {
                result = detr.getTicketInfoByTktNo(ticketNo);
                log.info("ShowTicket.getTicketStatus detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) +
                        "|" + PNRUtils.getTxnid(detr) + ",error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + "|" + PNRUtils.getTxnid(detr) + "error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        if (null != result) {
            if (result.getSegmentCount() > 0) {
                Iterator<DETRTKTSegment> it = result.getAirSeg().iterator();
                while (it.hasNext()) {
                    DETRTKTSegment segment = it.next();
                    if (depCode.equals(segment.getDepAirportCode())) {
                        ticketstatus = segment.getTicketStatus().replaceAll(" ", "");
                        break;
                    }
                }
            }
        }
        return ticketstatus;
    }

    /**
     * 获得客票信息
     *
     * @param ticketNo
     * @return
     * @auther chen-bing
     */
    public DETRTKTResult getResult(String ticketNo) {
        DETR detr = new DETR();
        PNRManage.configIBEClient(detr, airline);
        DETRTKTResult result = null;
        int times = 3;
        for (int i = 0; i < times; i++) {
            try {
                result = detr.getTicketInfoByTktNo(ticketNo);
                log.info("ShowTicket.getResult detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) + "|" + PNRUtils.getTxnid(detr) + "|error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + "|" + PNRUtils.getTxnid(detr) + "|error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        return result;
    }


    /**
     * 获得客票信息  双因素
     *
     * @param ticketNo
     * @return
     * @auther chen-bing
     */
    public DETRTKTResult getResult2F(String ticketNo, String secondFactorCode, String secondFactorValue) {
        DETR2F detr2f = new DETR2F();
        PNRManage.configIBEClient(detr2f, airline);
        DETRTKTResult result = null;
        int times = 3;
        for (int i = 0; i < times; i++) {
            try {
                try {
                    result = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
                } catch (Exception e) {
                    // 当前库中没有，即客票已进历史
                    if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                        result = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "H", secondFactorCode, secondFactorValue);
                    }
                }
                log.info("ShowTicket.getResult detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr2f));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) + "|" + PNRUtils.getTxnid(detr2f) + "|error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + "|" + PNRUtils.getTxnid(detr2f) + "|error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        return result;
    }

    /**
     * 获得客票历史信息
     *
     * @param ticketNo
     * @return
     * @auther chen-bing
     */
    public DETRHistoryResult getHistoryResult(String ticketNo) {
        DETR detr = new DETR();
        PNRManage.configIBEClient(detr, airline);
        DETRHistoryResult result = null;
        int times = 3;
        for (int i = 0; i < times; i++) {
            try {
                result = detr.getTicketHistoryByTktNo(ticketNo);
                log.info("ShowTicket.getHistoryResult detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) + "|" + PNRUtils.getTxnid(detr) + ",error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + " error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        return result;
    }

    /**
     * 获得客票历史信息 双因素
     *
     * @param ticketNo
     * @return
     * @auther chen-bing
     */
    public DETRHistoryResult getHistoryResult2F(String ticketNo, String secondFactorCode, String secondFactorValue) {
        DETR2F detr2f = new DETR2F();
        PNRManage.configIBEClient(detr2f, airline);
        DETRHistoryResult result = null;
        int times = 3;
        for (int i = 0; i < times; i++) {
            try {
                try {
                    result = detr2f.getTicketHistoryByTktNo2F(ticketNo, "N", secondFactorCode, secondFactorValue);
                } catch (Exception e) {
                    // 当前库中没有，即客票已进历史
                    if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                        result = detr2f.getTicketHistoryByTktNo2F(ticketNo, "H", secondFactorCode, secondFactorValue);
                    }
                }
                log.info("ShowTicket.getHistoryResult detr ticket|ticketNo:"
                        + ticketNo + "|" + PNRUtils.getTxnid(detr2f));
                break;
            } catch (Exception e) {
                log.error("Refund ticket：" + ticketNo + " error,trytime " + (i + 1) + "|" + PNRUtils.getTxnid(detr2f) + ",error info:" + e.getMessage());
                if (i < (times - 1)) {
                    continue;
                } else {
                    log.error("DETR ticket ：" + ticketNo + " error,超出尝试次数，异常抛出,错误信息:" + e.getMessage());
                    throw new IBEException("Error during DETR of ticket number " + ticketNo + ". " + e.getMessage(), e);
                }
            }
        }
        return result;
    }
}
