package net.jdair.specialrefund.bizz.utils.pay;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtPayment;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper;
import net.jdair.specialrefund.bizz.utils.SpringUtils;
import net.jdair.specialrefund.bizz.vo.PayType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.swing.*;
import java.util.HashMap;
import java.util.Map;


/**
 * 支付退款服务
 */
@Component
@Slf4j
public class PayService {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    /**
     * 退款
     * @param etPayment 支付记录
     * @param payType 支付类型
     * @return
     * @throws Exception
     */
    @Transactional
    public Map<String, Object> onlineRefund(EtFltRefund etFltRefund, EtPayment etPayment, PayType payType) throws Exception {

        // 加锁
        EtFltRefund lockEtFltRefund = etFltRefundMapper.selectOne(
                new LambdaQueryWrapper<EtFltRefund>()
                        .eq(EtFltRefund::getId, etFltRefund.getId())
                        .last("FOR UPDATE")
        );
        log.info("lock entity: " + lockEtFltRefund.getRefundNo() + ", " + lockEtFltRefund.getStatus());

        if (!"PASS".equals(lockEtFltRefund.getStatus())) {
            throw new Exception("该退票单已经退款");
        }


        Map<String, Object> params = new HashMap<String, Object>();
        params.put(etFltRefund.getClass().getName(), etFltRefund);
        params.put(etPayment.getClass().getName(), etPayment);
        params.put(payType.getClass().getName(), payType);

        this.saveFailedRefundPayment(params);
        this.refund(params);
        return params;
    }

    /**
     * 退款预处理。保存支付失败记录、修改退票单状态、插入退款中间表等
     * @param params
     * @return
     * @throws Exception
     */
    private Map<String, Object> saveFailedRefundPayment(Map<String, Object> params) throws Exception {
        AbstractRefundHandler.Builder<Object> saveFailPaymentHandler = new AbstractRefundHandler.Builder<>();
        saveFailPaymentHandler
                .addHandler(SpringUtils.getBean(SaveAlipayFailedRefundPaymentHandlerGJ.class))
                .addHandler(SpringUtils.getBean(SaveWXpayFailedRefundPaymentHandlerGJ.class))
                .addHandler(SpringUtils.getBean(SaveEasyCardDFFailedRefundPaymentHandler.class))
                .build().doHandler(params);

        params.put("refundPaymentNo", params.get("refundPaymentNo"));
        params.put("canRefund", params.get("canRefund"));

        return params;
    }

    /**
     * 发起第三方支付在线退款请求
     * @param params
     * @return
     * @throws Exception
     */
    private Map<String, Object> refund(Map<String, Object> params) throws Exception {
        AbstractRefundHandler.Builder<Object> refundHandler = new AbstractRefundHandler.Builder<>();
        refundHandler
                .addHandler(SpringUtils.getBean(AlipayRefundHandlerGJ.class))
                .addHandler(SpringUtils.getBean(AlipayRefundHandlerGJWithInsurance.class))
                .addHandler(SpringUtils.getBean(WXpayRefundHandlerGJ.class))
                .addHandler(SpringUtils.getBean(EasyCardDFRefundHandler.class))
                .build().doHandler(params);
        return params;
    }
}
