package net.jdair.specialrefund.bizz.utils;


import com.thoughtworks.xstream.XStream;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.io.DocumentSource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.transform.*;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: </p>
 *
 * <p>Description: </p>
 *
 * <p>Copyright: Copyright (c) 2005</p>
 *
 * <p>Company: </p>
 *
 * <AUTHOR> attributable
 * @version 1.0
 */
public class XmlUtil {
    public XmlUtil() {
    }

    @Deprecated
    public static Document readXmlByStr(String xmlStr) throws DocumentException {
        Document document = null;
        //InputStream is = new ByteArrayInputStream(xmlStr.getBytes()); 
        document = DocumentHelper.parseText(xmlStr);
        return document;
    }
    @SuppressWarnings("unchecked")
	public static List parse(Document doc, String filter) throws DocumentException {
        List lst = doc.selectNodes(filter);
        return lst;
    }

    @Deprecated
    public static Document createDocumentFromRequest(HttpServletRequest request){
        Document doc = null;
        try{

            InputStream is =  request.getInputStream();

            byte []  b = new byte [request.getContentLength()] ; //开辟文件大小的空间

            int len = 0;

            int temp = 0;

            while((temp = is.read()) != -1){
                b [len]  = (byte)temp;
                len++;
            }

            is.close();

            String xmlStr = new String(b,0,len,"UTF-8");
            doc = DocumentHelper.parseText(xmlStr);
        	
        	
            /*request.setCharacterEncoding("UTF-8");
            
            int binArrayCount = request.getContentLength();

            binArrayData = new byte[binArrayCount];
            int j;
            for (int i = 0; i < binArrayCount; i += j) {
                try {
                    j = request.getInputStream().read(binArrayData, i,
                            binArrayCount - i);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new Exception("=========创建Doc出错=========");
                }
            }
            String xmlStr = new String(binArrayData,"UTF-8");
            doc = DocumentHelper.parseText(xmlStr);
            */
        }catch(Exception e){
        }
        return doc;
    }
    
    public static String createTextFromRequest(HttpServletRequest request){
        String str = null;
        try{
        	
        	InputStream is =  request.getInputStream();
        	
        	byte []  b = new byte [request.getContentLength()] ; //开辟文件大小的空间

        	int len = 0;

        	int temp = 0;

        	while((temp = is.read()) != -1){
        		b [len]  = (byte)temp;
        		len++;
        	}

        	is.close();

        	str = new String(b,0,len,"UTF-8");
        	
        	
            /*request.setCharacterEncoding("UTF-8");
            
            int binArrayCount = request.getContentLength();

            binArrayData = new byte[binArrayCount];
            int j;
            for (int i = 0; i < binArrayCount; i += j) {
                try {
                    j = request.getInputStream().read(binArrayData, i,
                            binArrayCount - i);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new Exception("=========创建Doc出错=========");
                }
            }
            String xmlStr = new String(binArrayData,"UTF-8");
            doc = DocumentHelper.parseText(xmlStr);
            */
        }catch(Exception e){
        }
        return str;
    }
    
    public static void responseXML(HttpServletResponse response,String docXML){
    	try{
			response.setContentType("text/xml;charset=utf-8");           
			response.setHeader("Cache-Control","no-cache");
			PrintWriter pw = null; 
			pw = response.getWriter();       
			pw.print(docXML);       
			pw.close(); 
    	}catch(Exception e){
    		e.printStackTrace();
    	}
    }
    
    public static void responseText(HttpServletResponse response,String text){
    	try{
	    	response.setContentType("text/html;charset=utf-8");           
	    	response.setHeader("Cache-Control","no-cache");
			PrintWriter pw = null; 
			pw = response.getWriter();       
			pw.print(text);       
			pw.flush();
			pw.close(); 
    	}catch(Exception e){
    		
    	}
    }
    

    public static String getStr(String str) {
        try {
            String temp_p = str;
            byte[] temp_t = temp_p.getBytes("ISO8859-1");
            String temp = new String(temp_t);
            return temp;
        } catch (Exception e) {

        }
        return "null";
    }
    /**
     * 将Bean转换为XML
     *
     * @param clazzMap 别名-类名映射Map
     * @param bean     要转换为xml的bean对象
     * @return XML字符串
     */
    public static String bean2xml(Map<String, Class> clazzMap, Object bean) {
        XStream xstream = new XStream();
        for (Iterator it = clazzMap.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, Class> m = (Map.Entry<String, Class>) it.next();
            xstream.alias(m.getKey(), m.getValue());
        }
        String xml = xstream.toXML(bean);
        return xml;
    }

    /**
     * 将XML转换为Bean
     *
     * @param clazzMap 别名-类名映射Map
     * @param xml      要转换为bean对象的xml字符串
     * @return Java Bean对象
     */
    public static Object xml2Bean(Map<String, Class> clazzMap, String xml) {
        XStream xstream = new XStream();
        if (!clazzMap.isEmpty()) {
	        for (Iterator it = clazzMap.entrySet().iterator(); it.hasNext();) {
	            Map.Entry<String, Class> m = (Map.Entry<String, Class>) it.next();
	            xstream.alias(m.getKey(), m.getValue());
	        }	
		}
        Object bean = xstream.fromXML(xml);
        return bean;
    }
    /**
     * @param omitMap
     * @param aliasMap		别名-类名映射Map
     * @param xml			要转换为bean对象的xml字符串
     * @return				Java Bean对象
     */
    public static Object xml2BeanOmitProperties(Map<Class, String>omitMap, Map<String, Class> aliasMap, String xml) {
        XStream xstream = new XStream();
        if (!aliasMap.isEmpty()) {
	        for (Iterator it = aliasMap.entrySet().iterator(); it.hasNext();) {
	            Map.Entry<String, Class> am = (Map.Entry<String, Class>) it.next();
	            xstream.alias(am.getKey(), am.getValue());
	        }	
		}
        Object bean = xstream.fromXML(xml);
        return bean;
    }
    /**
     * 获取XStream对象
     *
     * @param clazzMap 别名-类名映射Map
     * @return XStream对象
     */
    public static XStream getXStreamObject(Map<String, Class> clazzMap) {
        XStream xstream = new XStream();
        for (Iterator it = clazzMap.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, Class> m = (Map.Entry<String, Class>) it.next();
            xstream.alias(m.getKey(), m.getValue());
        }
        return xstream;
    }
}
