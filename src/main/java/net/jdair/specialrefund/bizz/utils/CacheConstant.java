package net.jdair.specialrefund.bizz.utils;
public class CacheConstant {

	/**
	 * 缓存时间配置  缓存1分钟
	 */
	public static final int MEMC_TIME_MINUTES_1 = 60;
	/**
	 * 缓存时间配置  缓存0.5分钟
	 */
	public static final int MEMC_TIME_SECOND_30 = 30;
	/**
	 * 缓存时间配置  缓存2分钟
	 */
	public static final int MEMC_TIME_MINUTES_2 = 120;
	/**
	 * 缓存时间配置  缓存3分钟
	 */
	public static final int MEMC_TIME_MINUTES_3 = 180;
	/**
	 * 缓存时间配置  缓存5分钟
	 */
	public static final int MEMC_TIME_MINUTES_5 = 300;
	/**
	 *缓存时间配置  缓存10分钟 
	 */
	public static final int MEMC_TIME_MINUTES_10 = 600;
	/**
	 *缓存时间配置  缓存15分钟 
	 */
	public static final int MEMC_TIME_MINUTES_15 = 900;
	/**
	 *缓存时间配置  缓存15分钟 
	 */
	public static final int MEMC_TIME_MINUTES_20 = 1200;
	/**
	 * 缓存时间配置  缓存30分钟
	 */
	public static final int MEMC_TIME_MINUTES_30 = 1800;
	/**
	 * 缓存时间配置  缓存60分钟
	 */
	public static final int MEMC_TIME_MINUTES_60 = 3600;
	/**
	 * 缓存时间配置  缓存1天
	 */
	public static final int MEMC_TIME_1_day = 86400;
	/**
	 * 缓存时间配置  缓存10天
	 */
	public static final int MEMC_TIME_10_day = 864000;
	/**
	 * 缓存时间配置  缓存20天
	 */
	public static final int MEMC_TIME_20_day = 1728000;

	/**
	 * 缓存时间配置  缓存120分钟
	 */
	public static final int RECOM_TIME_MINUTES_120 = 7200;
	/**
	 * 定义缓存key值的最大长度，超出后用md5加密
	 */
	public static int MAX_CACHE_LENGTH = 32;


	/*
	 * 特殊退票退款航班信息缓存
	 */
	public static final String SPECIAL_REFUND_KEY_PREFIX = "SPECIALREFUND-KEY-";

	/**
	 * 旅客补偿信息缓存
	 */
	public static final String COMPENSATE_KEY_PREFIX = "COMPENSATE-KEY-";

	/*
	 * 机场信息查询缓存
	 */
	public static final String SEGMENT_DICTIONARY_QUERY_KEY_PREFIX = "SEGMENT-DICTIONARY-QUERY-KEY-";


}
