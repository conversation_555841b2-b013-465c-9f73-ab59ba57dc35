package net.jdair.specialrefund.bizz.utils.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtAlipayPayment;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg;
import net.jdair.specialrefund.bizz.domain.EtPayment;
import net.jdair.specialrefund.bizz.mapper.EtAlipayPaymentMapper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundPaymentMapper;
import net.jdair.specialrefund.bizz.mapper.EtPaymentMapper;
import net.jdair.specialrefund.bizz.utils.email.EmailServiceUtil;
import net.jdair.specialrefund.bizz.utils.sms.SmsServiceUtil;
import net.jdair.specialrefund.bizz.vo.DoAuditing;
import net.jdair.specialrefund.bizz.vo.PayStatus;
import net.jdair.specialrefund.bizz.vo.PayType;
import net.jdair.specialrefund.bizz.vo.PaymentLogStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 2. 国际票支付宝退款(含随票保险)
 */
@Component
@Slf4j
public class AlipayRefundHandlerGJWithInsurance extends AbstractRefundHandler {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private EtAlipayPaymentMapper etAlipayPaymentMapper;

    @Autowired
    private AlipayConfig alipayConfig;

    @Autowired
    private EmailServiceUtil emailServiceUtil;

    @Autowired
    private SmsServiceUtil smsServiceUtil;


    @Override
    @Transactional
    public void doHandler(Map map) throws Exception {
        PayType payType = (PayType) map.get(PayType.class.getName());
        EtPayment etPayment = (EtPayment) map.get(EtPayment.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) map.get(EtFltRefund.class.getName());
        if (isPayType(payType)
                && (("GJ".equals(etFltRefund.getOrderSource()) && etFltRefund.getRetrieveTimeFlag() == 1))) {
            log.info("AlipayRefundHandlerGJWithInsurance paymentNo:" + etPayment.getPaymentNo());
            //限制重复退款思路：锁定退票单，更改状态为退款处理中状态，在退款操作时候判断是否正在处理该退款单，否，才允许执行退款操作
            boolean canRefundFalg = false;
            // TODO 锁表
            // helper.lockRefund( fltRefund );
            Boolean canRefund = (Boolean) map.get("canRefund");
            canRefundFalg = canRefund.booleanValue();
            //如果退款单的状态还是PASS说明保存失败的退款记录失败了 就不让退款
            if ("PASS".equals(etFltRefund.getStatus())) {
                canRefundFalg = false;
            }

            String paymentNo = "";
            for (EtPayment payment : etFltRefund.getEtOutsidePaymentList()) {
                EtAlipayPayment alipayPayment = etAlipayPaymentMapper.findById(payment.getId());
                paymentNo = alipayPayment.getDealId();
                log.info("AlipayRefundHandlerGJWithInsurance dealid : " + paymentNo + " from " + payment.getPaymentNo());
                break;
            }

            if (canRefundFalg) {
                try {
                    String refundPaymentNo = (String) map.get("refundPaymentNo");
                    EtPayment tmpPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", refundPaymentNo));
                    if (tmpPayment == null) {
                        throw new Exception("退款失败：保存退款信息失败");
                    }
                    BigDecimal refundAmount = tmpPayment.getAmount();
                    if (tmpPayment.getPayStatus() == PayStatus.PAID.toString()) {
                        log.info("GJ with insurance alipay refund info.has refunded.refundPaymentNo: " + tmpPayment.getPaymentNo() + ", status: " + tmpPayment.getPayStatus().toString());
                        throw new Exception("已经退款");
                    }
                    //****************************************
                    // 支付宝配置
                    AlipayConfig.Config config = alipayConfig.getSource().get("GJWithInsurance");

                    AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",
                            config.getAppId(),
                            config.getAppPrivateKey(),
                            "json",
                            "GBK",
                            config.getAliPublicKey(),
                            "RSA2");
                    AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
                    JSONObject bizContent = new JSONObject();
                    bizContent.put("out_trade_no", refundPaymentNo);
                    bizContent.put("trade_no", paymentNo);
                    bizContent.put("refund_amount", refundAmount.doubleValue());
                    bizContent.put("out_request_no", etPayment.getPaymentNo());

                    //// 返回参数选项，按需传入
                    //JSONArray queryOptions = new JSONArray();
                    //queryOptions.add("refund_detail_item_list");
                    //bizContent.put("query_options", queryOptions);

                    request.setBizContent(bizContent.toString());

                    String resultString = "";
                    try {
                        AlipayTradeRefundResponse response = alipayClient.execute(request);
                        log.info("GJ with insurance original paymentNo:" + paymentNo + ", 支付宝退款请求结果：" + JSON.toJSONString(response));
                        if (response.isSuccess()) {
                            resultString = PaymentLogStatus.SUCCESS.toString();
                            // 退款成功判断说明：接口返回fund_change=Y为退款成功，fund_change=N或无此字段值返回时需通过退款查询接口进一步确认退款状态。详见退款成功判断指导。
                            // 注意，接口中code=10000，仅代表本次退款请求成功，不代表退款成功。
                            if ("Y".equals(response.getFundChange())) {
                                Map<String, Object> params = new HashMap<String, Object>();
                                // 交易成功
                                params.put(PayStatus.class.getName(), PayStatus.PAID);
                                params.put("paymentNo", etPayment.getPaymentNo());
                                params.put(EtFltRefund.class.getName(), etFltRefund);
                                this.processBizz(params);
                                map.put("returnResult", PaymentLogStatus.SUCCESS);
                                return;
                            }
                        } else {
                            resultString = PaymentLogStatus.ERROR.toString();
                            try {
                                // 交易相关ELK日志输出
                                log.info("{\n" +
                                        "\"操作类型\":\"退款\",\n" +
                                        "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                        "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                        "\"问题说明\":\"" + resultString + "\"\n" +
                                        "}");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    } catch (Exception e1) {
                        etPayment.setPayStatus(PayStatus.PENDICING.toString());
                        resultString = PaymentLogStatus.ERROR.toString();
                        log.error("GJ with insurance alipay refund fail.original paymentNo:" + paymentNo + ",err:" + e1.getMessage(), e1);
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + e1.getMessage() + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    if (PaymentLogStatus.SUCCESS.equals(map.get("returnResult"))) {
                        /*
                         * 退款请求成功
                         */
                        this.updateRefundStatus(etFltRefund, "PEND");
                    }
                    if (PaymentLogStatus.ERROR.toString().equals(resultString.trim())) {
                        /*
                         * 退款请求异常
                         */
                        this.updateRefundStatus(etFltRefund, "PENDICING");
                    }
                } catch (Exception e) {
                    log.error("GJ original paymentNo:" + paymentNo + ",refundId:" + etFltRefund.getId() + ",支付宝退款异常" + e.getMessage(), e);
                    log.info("GJ 支付宝退款异常" + e.getMessage());
                    e.printStackTrace();
                    /*
                     * 退款请求异常
                     */
                    this.updateRefundStatus(etFltRefund, "PENDICING");
                }
                map.put("returnResult", PaymentLogStatus.FAILED);
            }
        }
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (next != null) {
            next.doHandler(map);
        }
    }

    private boolean isPayType(PayType payType) {
        return PayType.ALIPAY == payType;
    }

    /**
     * 退款成功后的业务处理
     * @param params
     * @throws Exception
     */
    private void processBizz(Map<String, Object> params) throws Exception {
        String paymentNo = (String) params.get("paymentNo");
        PayStatus payStatus = (PayStatus) params.get(PayStatus.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) params.get(EtFltRefund.class.getName());
        //保存支付记录到数据库
        EtPayment payment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", paymentNo));
        if (null == payment) {
            log.info("Pay Call Back: Payment is not found. paymentNo is " + paymentNo);
            throw new Exception("Pay Call Back : Payment is not found. paymentNo is " + paymentNo);
        } else if (PayStatus.PAID.toString().equals(payment.getPayStatus()) || PayStatus.FAIL.toString().equals(payment.getPayStatus())) {
            log.info("Pay Call Back: Payment status has Been PAID. paymentNo is " + paymentNo);
            return;
        }
        log.info("refund no:" + etFltRefund.getRefundNo() + etFltRefund.getStatus());

        // 更新退票单状态
        this.updateRefundStatus(etFltRefund, payStatus.toString());
        if ("PAID".equals(payStatus.toString())) {
            if ("HWZ".equals(etFltRefund.getOrderSource())) {
                // 海外站退款成功后，给旅客发送邮件
                BigDecimal refundAmount = new BigDecimal(0);
                for (EtFltRefundPaxSeg fltRefundPaxSeg : etFltRefund.getEtFltRefundPaxSegList()) {
                    refundAmount = refundAmount.add(fltRefundPaxSeg.getActualRefundAmount());
                }
                emailServiceUtil.sendEmail4HWZ(etFltRefund.getUserName(), refundAmount.toString());
            } else if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                smsServiceUtil.sendToRefundSmsQueue(etFltRefund);
            }
        }

        if ("PAID".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus()) || "PENDICING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.PAID.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else if ("FAIL".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.FAIL.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else {
            throw new Exception("Pay Call Back: Success payment Status is fail by this process.");
        }

    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }

}
