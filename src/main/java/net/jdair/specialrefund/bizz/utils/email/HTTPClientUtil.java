package net.jdair.specialrefund.bizz.utils.email;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.util.*;


/**
 * 发送邮件 接口调用
 */
public class HTTPClientUtil {

    private static Log logger = LogFactory.getLog( HTTPClientUtil.class );

    private static CloseableHttpClient httpclient;

	private static final int TIMEMSEC = 5000;

    public static CloseableHttpClient getHttClient() throws Exception {
//        if (httpclient == null) {
//            httpclient = HttpClients.createDefault();
//        }
        httpclient = new SSLClient();
        logger.info("httpclient:"+httpclient);
        return httpclient;
    }
	private static String aiCc;
	private static String aiCt;
	private static String aiCp;
	private static String aiUrl;
	private static String flightinterface;

    static {
    	aiCc = "1";
    	aiCt = "1";
    	aiCp = "1.202.236.146";
    	aiUrl = "https://dsp.jdair.net";
    	flightinterface = "/flightinterface/uss/json/email/sendEmail.json";
    }

    public static String getEmailObject(Map<String, String> params){
		try {			
			logger.debug("sendEmail start!params:"+params);
			String result = HTTPClientUtil.sendEmail(params);
			logger.info("sendEmail end! result:"+result);
			//转成json数据
			JSONObject jsonArray = JSONObject.parseObject(result);
			if(null != jsonArray && !"".equals(jsonArray)){
				JSONObject emailResult = jsonArray.getJSONObject("result");
				if(jsonHasForKey(emailResult,"resultCode")){
					//resultCode:1000:成功
					if("1000".equals(emailResult.getString("resultCode")) || "1000" == emailResult.getString("resultCode")){
						if(jsonHasForKey(emailResult,"viewMessage")){
							logger.info("调试人员看的Message，仅供开发人员查看 sendEmail viewMessage:"+emailResult.getString("viewMessage"));
						}
						return "succ";
					}else{
						if(jsonHasForKey(emailResult,"viewMessage")){
							logger.info("调试人员看的Message，仅供开发人员查看  viewMessage:"+emailResult.getString("viewMessage"));
						}
						logger.error("调用发送邮箱接口; 错误信息：params:"+params);
						return null;			
					}
				}else{
					logger.error("调用发送邮箱接口,返回结果取值Code出错！ params:"+params);
					return null;
				}
			}else{
				logger.error("调用发送邮箱接口,返回结果为NULL！ params:"+params);
				return null;
			}
		} catch (Exception e) {
			logger.error("调用发送邮箱接口异常 params:"+params+"  Errormsg:"+e.getMessage());
			throw new RuntimeException("调用发送邮箱接口异常");
		}
	}
    
    /**
     * 发送邮件 接口调用
     * @return
     * @throws Exception 
     */
    public static String sendEmail(Map<String, String> rmiParams) throws Exception{
        rmiParams.put("ai.cc", aiCc);
        rmiParams.put("Ai.ct", aiCt);
        rmiParams.put("ai.cp", aiCp);
        String sing = getCRMSign(rmiParams);
        logger.info("调用CRM接口, 签名密文[" + sing + "]");
        rmiParams.put("ai.sign", sing);
        logger.info("调用CRM接口[" + aiUrl + flightinterface + "]; 参数[" + rmiParams + "]");
        return callRMIT(aiUrl + flightinterface, rmiParams);
    }
    
    public static String callRMIT(String rmiPath, Map<String, String> params) throws Exception{
    	String result = ""; 
    	CloseableHttpClient httpClient = getHttClient();
		HttpPost post = new HttpPost(rmiPath);
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(TIMEMSEC).setConnectTimeout(TIMEMSEC).build();//设置请求和传输超时时间
		post.setConfig(requestConfig);
		try {
			//放置请求参数
			List<BasicNameValuePair> formparams = new ArrayList<BasicNameValuePair>();  
			if(params != null && params.size() > 0){
	            for(String key : params.keySet()){
	            	formparams.add(new BasicNameValuePair(key, params.get(key))); 
	            }
	        }
			//设置编码
			UrlEncodedFormEntity uefEntity = new UrlEncodedFormEntity(formparams, "UTF-8");  
			post.setEntity(uefEntity);
			//发送请求
			CloseableHttpResponse sendResponse = httpClient.execute(post);
			result = EntityUtils.toString(sendResponse.getEntity(), "UTF-8");
			logger.info("result == "+result);
		}catch (Exception e) {
			logger.error("调用发送邮箱接口异常 params:"+params+"  Errormsg:"+e.getMessage());
		}	
		return result;  
    }
    
    /**
     * @Title: getCRMSign 
     * @Description: TODO(生产签名) 
     * 签名方式：各参数key按首字母排序，然后值进行MD5加密
     * @param: @param rmiParams
     * @param: @return 
     * @return: String 
     * @throws
     */
    public static String getCRMSign(Map<String,String> rmiParams){
        Map<String, String> sortMap = new TreeMap<String, String>(new Comparator<String>() {

            public int compare(String key1, String key2) {
                return key1.compareTo(key2);
            }

        });
        sortMap.putAll(rmiParams);
        StringBuilder signSorce = new StringBuilder();
        for(String key : sortMap.keySet()){
            signSorce.append(sortMap.get(key));
        }
        logger.info("调用邮箱接口, 签名明文[" + signSorce.toString() + "]");
        return MD5Util.md5Hex(signSorce.toString()).toUpperCase();
    }
   
    /**
	 * 判断返回json中是否存在当前的key
	 * @param json
	 * @param key
     * @return
     */
	public static boolean jsonHasForKey (JSONObject json,String key){
		boolean eCustName = json.containsKey(key);
		return eCustName;
	}
	
	
	/*public static void main(String[] args) throws Exception {
		BASE64Encoder base64 = new BASE64Encoder();
		Map<String, String> params = new HashMap<String, String>();
		//必填项
    	params.put("toEmail", "<EMAIL>");//收件邮箱，多个邮箱间用英文逗号隔开
		params.put("subject", "邮箱发件测试主题");//主题
		params.put("content", "邮箱发件测试内容");//内容
		params.put("fromEmail", "<EMAIL>");//发件邮箱
		params.put("fromName", "shfydd");//发件人内网账号
		params.put("fromPasswd", "ihm.794");//发件人邮箱密码
		//params.put("fromPasswd", base64.encode("qwe.321".getBytes("utf-8")));//发件人邮箱密码
		//非必填
		params.put("ccEmail", "");//抄送邮箱，多个邮箱间用英文逗号隔开
		params.put("bccEmail", "");//密送邮箱，多个邮箱间用英文逗号隔开
		params.put("contentType[0].name", "附件");//附件名称  如果多个附件增加中括号中的数字即可
		params.put("contentType[0].content", base64.encode("附件内容".getBytes("utf-8")));//附件文件的base64码 
    	String resultString = getEmailObject(params);
    	if("succ" == resultString || "succ".equals(resultString)){
    		System.err.println("成功！");
    	}
	}*/
}
