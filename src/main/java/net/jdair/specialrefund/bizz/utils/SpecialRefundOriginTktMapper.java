package net.jdair.specialrefund.bizz.utils;

import net.jdair.specialrefund.bizz.domain.EtSpecialRefundOriginTkt;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundOriginTkt;

public class SpecialRefundOriginTktMapper extends Mapper<DTOSpecialRefundOriginTkt, EtSpecialRefundOriginTkt> {

	private static SpecialRefundOriginTktMapper mapper;

	private SpecialRefundOriginTktMapper() {
		super(DTOSpecialRefundOriginTkt.class, EtSpecialRefundOriginTkt.class);
	}

	public static SpecialRefundOriginTktMapper getInstance() {
		if (mapper == null) {
			mapper = new SpecialRefundOriginTktMapper();
		}
		return mapper;
	}

	public EtSpecialRefundOriginTkt mapToEntity(DTOSpecialRefundOriginTkt value) {
		EtSpecialRefundOriginTkt p = super.mapToEntity(value);

		return p;
	}

	public DTOSpecialRefundOriginTkt mapToValue(EtSpecialRefundOriginTkt entity) {
		DTOSpecialRefundOriginTkt value = super.mapToValue(entity);

		return value;
	}
}