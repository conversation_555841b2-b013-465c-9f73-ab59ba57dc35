package net.jdair.specialrefund.bizz.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.jdair.specialrefund.bizz.domain.TsStation;
import net.jdair.specialrefund.bizz.feign.BaseinfoClient;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 基础信息查询类
 */
@Component
public class BaseInfoUtils {

    private static final Log LOG = LogFactory.getLog(BaseInfoUtils.class);

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private BaseinfoClient baseinfoClient;

    /**
     * 获取城市中文名
     * @param cityCode
     * @return
     */
    public String getCityChiName(String cityCode) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        String jsonString = operations.get(CacheConstant.SEGMENT_DICTIONARY_QUERY_KEY_PREFIX + cityCode);
        TsStation tsStation = JSON.parseObject(jsonString, new TypeReference<TsStation>() {});
        if (null == tsStation) {
            TsStation result = baseinfoClient.getTsStationByIataId(cityCode).getData();
            if (null != result) {
                try {
                    operations.set(CacheConstant.SEGMENT_DICTIONARY_QUERY_KEY_PREFIX + cityCode, JSON.toJSONString(result), CacheConstant.MEMC_TIME_MINUTES_30, TimeUnit.SECONDS);
                    return result.getCityCn();
                } catch (Exception e) {
                    LOG.error("put tsStation error|airportCode:" + cityCode + "|error:" + e.getMessage(), e);
                }
            }
        }
        if (tsStation != null) {
            return tsStation.getCityCn();
        } else {
            return "";
        }
    }
}
