package net.jdair.specialrefund.bizz.utils.file.service.impl;

import com.hna.eking.util.oss.utils.MinIOUtil;
import io.minio.GetObjectResponse;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.utils.file.service.FileService;
import net.jdair.specialrefund.bizz.utils.file.vo.OssFileInfo;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 文件服务接口实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Autowired
    private MinIOUtil minioUtil;

    /**
     * 文件上传
     *
     * @param multipartFile multipartFile
     * @return OssFileInfo
     */
    @Override
    public OssFileInfo ossUpload(MultipartFile multipartFile, String bucketName) {
        OssFileInfo ossFileUploadRs = new OssFileInfo();
        String filePath = minioUtil.upload(multipartFile, bucketName);
        ossFileUploadRs.setBucketName(bucketName);
        ossFileUploadRs.setFilePath(filePath);
        return ossFileUploadRs;
    }

    /**
     * 文件下载
     *
     * @param filePath            filePath
     * @param httpServletResponse httpServletResponse
     */
    @Override
    public void ossDownload(String filePath, HttpServletResponse httpServletResponse, String bucketName) {
        try {
            GetObjectResponse response = minioUtil.download(filePath, bucketName);
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filePath, "UTF-8"));
            IOUtils.copy(response, httpServletResponse.getOutputStream());
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new RuntimeException("下载文件失败");
        }
    }

    /**
     * 文件预览地址获取
     *
     * @param filePath filePath
     * @return String
     */
    @Override
    public String getPreviewUrl(String filePath, String bucketName) {
        return minioUtil.getPreviewUrl(filePath, bucketName);
    }


}
