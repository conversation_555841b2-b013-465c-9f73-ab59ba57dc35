package net.jdair.specialrefund.bizz.utils;


import net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundImg;

public class SpecialRefundImgMapper extends Mapper<DTOSpecialRefundImg, EtSpecialRefundImg> {
    private static SpecialRefundImgMapper mapper;

    private SpecialRefundImgMapper() {
        super(DTOSpecialRefundImg.class, EtSpecialRefundImg.class);
    }

    public static SpecialRefundImgMapper getInstance() {
        if (mapper == null) {
            mapper = new SpecialRefundImgMapper();
        }
        return mapper;
    }

    @Override
    public EtSpecialRefundImg mapToEntity(DTOSpecialRefundImg value) {
        EtSpecialRefundImg t = super.mapToEntity(value);

        if (value.getId() == null) {
            t.setId(null);
        }

        return t;
    }

    @Override
    public DTOSpecialRefundImg mapToValue(EtSpecialRefundImg entity) {
        DTOSpecialRefundImg value = super.mapToValue(entity);

        return value;
    }
}
