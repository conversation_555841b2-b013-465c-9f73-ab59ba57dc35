
package net.jdair.specialrefund.bizz.utils.sms;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置
 *
 # 短信配置
 sms:
 url: http://msg.jdair.net
 send: /ussinterface/uss/json/mobile/messSend.json
 sendTD: /ussinterface/uss/json/mobile/advertMessSend.json
 cc: 1
 cp: 202.100.200.62

 */
@Data
@Configuration
public class SmsConfig {
	
	/**
	 * 短信服务地址
	 */
	@Value("${sms.url}")
	private String url;

	/**
	 * 非营销短信地址
	 */
	@Value("${sms.send}")
	private String send;

	/**
	 * 营销短信地址
	 */
	@Value("${sms.sendTD}")
	private String sendTD;

	/**
	 *
	 */
	@Value("${sms.cc}")
	private String cc;
	/**
	 *
	 */
	@Value("${sms.cp}")
	private String cp;

	/**
	 * 国际票，审核通过，短信模板
	 */
	@Value("${jd.template.sms.refundPass}")
	private String refundPass;

	/**
	 * 国际票，审核未通过，短信模板
	 */
	@Value("${jd.template.sms.refundUnPass}")
	private String refundUnPass;

	/**
	 * 国际票，审核拒绝，短信模板
	 */
	@Value("${jd.template.sms.refundRefuse}")
	private String refundRefuse;

	/**
	 * 国际票，二审通过，金额为0的短信模板
	 */
	@Value("${jd.template.sms.gjRefundPass0}")
	private String gjRefundPass0;

	/**
	 * 国际票，审核未通过，金额为0的短信模板
	 */
	@Value("${jd.template.sms.gjRefundUnpass0}")
	private String gjRefundUnpass0;

	/**
	 * 退款成功短信模板
	 */
	@Value("${jd.template.sms.refundMoney}")
	private String refundMoney;

	/**
	 * 特殊退票一审拒绝
	 */
	@Value("${jd.template.sms.tsRefundFRejectMsg}")
	private String tsRefundFRejectMsg;
	/**
	 * 特殊退票退款成功
	 */
	@Value("${jd.template.sms.tsRefundMoneySuccessMsg}")
	private String tsRefundMoneySuccessMsg;
	/**
	 * 特殊退票退款失败
	 */
	@Value("${jd.template.sms.tsRefundFailFRejectMsg}")
	private String tsRefundFailFRejectMsg;

}
