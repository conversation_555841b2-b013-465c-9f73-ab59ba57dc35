package net.jdair.specialrefund.bizz.utils.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtEasyCardDFConfig;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtPayment;
import net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.AlipaySignature;
import net.jdair.specialrefund.bizz.utils.HttpConnectUtils;
import net.jdair.specialrefund.bizz.vo.DoAuditing;
import net.jdair.specialrefund.bizz.vo.PayStatus;
import net.jdair.specialrefund.bizz.vo.PayType;
import net.jdair.specialrefund.bizz.vo.PaymentLogStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 2. 特殊退票 易生代付退款
 */
@Component
@Slf4j
public class EasyCardDFRefundHandler extends AbstractRefundHandler {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private EtEasyCardDFConfigMapper etEasyCardDFConfigMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Override
    @Transactional
    public void doHandler(Map map) throws Exception {
        PayType payType = (PayType) map.get(PayType.class.getName());
        EtPayment etPayment = (EtPayment) map.get(EtPayment.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) map.get(EtFltRefund.class.getName());
        if (isPayType(payType) && ("TS".equals(etPayment.getSource()) || "TSGJ".equals(etPayment.getSource()))) {
            log.info("EasyCardDFRefundHandler paymentNo:" + etPayment.getPaymentNo());
            //限制重复退款思路：锁定退票单，更改状态为退款处理中状态，在退款操作时候判断是否正在处理该退款单，否，才允许执行退款操作
            boolean canRefundFalg = false;
            // TODO 锁表
            // helper.lockRefund( fltRefund );
            Boolean canRefund = (Boolean) map.get("canRefund");
            canRefundFalg = canRefund.booleanValue();
            //如果退款单的状态还是PASS说明保存失败的退款记录失败了 就不让退款
            if ("PASS".equals(etFltRefund.getStatus())) {
                canRefundFalg = false;
            }

            String paymentNo = "";
            for (EtPayment origPayment : etFltRefund.getEtOutsidePaymentList()) {
                paymentNo = origPayment.getRemark();
            }

            if (canRefundFalg) {
                try {
                    // 商户配置
                    EtEasyCardDFConfig config = etEasyCardDFConfigMapper.selectOne(new QueryWrapper<EtEasyCardDFConfig>().eq("purpose", "TS"));

                    // 银行卡信息
                    EtSpecialRefundBankInfo specialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(new QueryWrapper<EtSpecialRefundBankInfo>().eq("refund_no", etFltRefund.getRefundNo()));

                    String refundPaymentNo = (String) map.get("refundPaymentNo");
                    EtPayment tmpPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", refundPaymentNo));
                    if (tmpPayment == null) {
                        throw new RuntimeException("退款失败：保存退款信息失败");
                    }

                    BigDecimal refundAmount = tmpPayment.getAmount();
                    if (PayStatus.PAID.toString().equals(tmpPayment.getPayStatus())) {
                        log.info("TS easycard refund info.has refunded.refundPaymentNo:" + tmpPayment.getPaymentNo() + ",status:" + tmpPayment.getPayStatus().toString());
                        throw new RuntimeException("已经退款");
                    }

                    JSONObject sParaTemp = new JSONObject();
                    sParaTemp.put("merchant_id", config.getMerchantId());
                    sParaTemp.put("out_trade_no", refundPaymentNo);
                    if (StringUtils.hasText(specialRefundBankInfo.getNbkno())) {
                        sParaTemp.put("nbkno", specialRefundBankInfo.getNbkno()); //联行号
                    }
                    if (StringUtils.hasText(specialRefundBankInfo.getRemark())) {
                        sParaTemp.put("remark", specialRefundBankInfo.getRemark()); //备注信息
                    } else {
                        sParaTemp.put("remark", "首都航空付款"); //备注信息
                    }
                    sParaTemp.put("acc", specialRefundBankInfo.getCardNo());  //银行卡号
                    sParaTemp.put("name", specialRefundBankInfo.getCardHolder());    //账户姓名
                    // sParaTemp.put("idno", "123456789012345679");    //身份证号(持卡人身份证号, 特殊商户需传)
                    sParaTemp.put("acc_type", 2); //付款账户类型：1 结算账户 2 现金账户, 默认2现金账户
                    sParaTemp.put("amount", refundAmount.multiply(BigDecimal.valueOf(100)).setScale(0).toString());
                    sParaTemp.put("notify_url", config.getNotifyUrl());  // 通知地址，代付成功才通知，失败不通知。通知内容，和6.9代付查询接口，返回的结果一样。

                    String biz_content = sParaTemp.toString();

                    //加密类型，默认RSA
                    String sign_type = "RSA";
                    //编码类型
                    String charset = "UTF-8";
                    //根据请求参数生成的加密串
                    String sign = AlipaySignature.rsaSign(biz_content, config.getMerchantPrivateKey(), charset);
                    log.info("计算签名数据为：" + sign + "\n");
                    Map<String, String> reqMap = new HashMap<String, String>(6);
                    reqMap.put("biz_content", biz_content);
                    //接口文档中的方法名
                    String service = "trade.acc.dsfpay.newPay";
                    reqMap.put("service", service);
                    reqMap.put("partner", config.getPartnerId());
                    reqMap.put("sign_type", sign_type);
                    reqMap.put("charset", charset);
                    reqMap.put("sign", sign);

                    String resultString = "";
                    try {
                        StringBuilder resultStrBuilder = new StringBuilder();
                        log.info("易生代付退款请求参数：" + JSON.toJSONString(reqMap));
                        int ret = HttpConnectUtils.sendRequest(config.getPayUrl(), "UTF-8", reqMap, 30000, 60000, "POST", resultStrBuilder, null);
                        log.info("易生代付退款请求结果：" + resultStrBuilder);
                        //易生公钥验证返回签名
                        if (!this.rsaVerifySign(resultStrBuilder, config.getEasypayPublicKey())) {
                            throw new RuntimeException("返回签名验证失败！");
                        }
                        log.info("TS original paymentNo:" + paymentNo + " ,refundPaymentNo:" + refundPaymentNo +
                                "\n 请求地址为：" + config.getPayUrl() +
                                "\n 请求结果为：" + ret +
                                "\n 请求参数为：" + reqMap.toString() +
                                "\n 返回内容为：" + resultStrBuilder.toString() + "\n");
                        resultString = resultStrBuilder.toString();
                        Map resultMap = handleRefundResponse(etPayment, resultString, map);
                    } catch (Exception e1) {
                        etPayment.setPayStatus(PayStatus.PENDICING.toString());
                        resultString = PaymentLogStatus.ERROR.toString();
                        log.error("TS easycard refund fail. original paymentNo:" + paymentNo + ",err:" + e1.getMessage(), e1);
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + e1.getMessage() + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    if (PaymentLogStatus.SUCCESS.equals(map.get("returnResult"))) {
                        // 退款请求成功
                        this.updateRefundStatus(etFltRefund, "PEND");
                    }
                    if (PaymentLogStatus.ERROR.toString().equals(resultString.trim())) {
                        // 退款请求异常
                        this.updateRefundStatus(etFltRefund, "PENDICING");
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + resultString + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                } catch (Exception e) {
                    log.error("TS original paymentNo:" + paymentNo + ",refundId:" + etFltRefund.getId() + ",易生代付退款异常" + e.getMessage(), e);
                    log.info("TS 易生代付退款异常：" + e.getMessage());
                    e.printStackTrace();

                    // 退款请求异常
                    this.updateRefundStatus(etFltRefund, "PENDICING");
                }
            }
        }
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (next != null){
            next.doHandler(map);
        }
    }

    private boolean isPayType(PayType payType) {
        if (PayType.CHINA_EASY_CARD == payType) {
            return true;
        }
        return false;
    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }

    /**
     * 回调通知报文，验证签名
     * @param resultStrBuilder
     * @param easypay_pub_key
     * @throws Exception
     */
    public static boolean rsaVerifySign(StringBuilder resultStrBuilder, String easypay_pub_key) throws Exception {
        //同步返回签名，需要对字符串进行截取后，再验证签名
        String msg = resultStrBuilder.toString();
        String returnString = org.apache.commons.lang.StringUtils.substringBetween(msg, "response\":", ",\"sign\"");
        String returnSign = org.apache.commons.lang.StringUtils.substringBetween(msg, ",\"sign\":\"", "\"}");
        boolean isTrue = AlipaySignature.rsaCheckContent(returnString, returnSign, easypay_pub_key, "UTF-8");
        System.out.println("验证返回签名是否正确：" + isTrue);
        return isTrue;
    }

    /**
     * @param payment
     * @param returnStr
     * @param map
     * @return
     * @throws Exception
     * @description
     */
    private Map handleRefundResponse(EtPayment payment, String returnStr, Map map) throws Exception {
        try {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            JSONObject resultObject = jsonObject.getJSONObject("trade_acc_dsfpay_newPay_response");
            //错误代码	说明
            //00	业务受理成功
            //20	业务异常
            //40	参数检查失败
            //41	partner错误
            //42	签名错误
            //99	系统异常
            String code = resultObject.getString("code");

            if ("00".equals(code)) {
                log.info("易生代付业务受理情况描述：" + resultObject.getString("msg"));
                //交易状态	说明
                //INIT	初始化
                //SUCCESS	成功
                //FAIL	失败
                //UNKNOWN	交易未知
                //UNSENT	订单号不存在
                // 代付结果，必须通过代付查询接口6.9查询代付结果！！！！！代付提交状态SUCCESS代表代付提交成功,并不是代付成功。UNKNOWN不代表失败.
                String tradeStatus = resultObject.getString("trade_status");
                // If Refund Failed
                if ("FAIL".equalsIgnoreCase(tradeStatus)) {
                    if (!payment.getPayStatus().equals(PayStatus.PENDICING)) {
                        map.put("returnResult", PaymentLogStatus.FAILED);
                    }
                    payment.setPayStatus(PayStatus.FAIL.toString());
                } else if ("SUCCESS".equalsIgnoreCase(tradeStatus)) { // If Refund Succeeded
                    map.put("returnResult", PaymentLogStatus.SUCCESS);
                    payment.setPayStatus(PayStatus.PENDICING.toString());
                } else {
                    map.put("returnResult", PaymentLogStatus.ERROR);
                    payment.setPayStatus(PayStatus.PENDICING.toString());
                }
            } else {
                map.put("returnResult", PaymentLogStatus.ERROR);
                log.error("易生代付业务受理异常:" + resultObject.getString("msg"));
                payment.setPayStatus(PayStatus.PENDICING.toString());
                return map;
            }
        } catch (Exception ex) {
            throw ex;
        }

        return map;
    }

}
