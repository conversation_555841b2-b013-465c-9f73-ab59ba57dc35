package net.jdair.specialrefund.bizz.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class FlightRefundNumberGenerator {
	private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

	private FlightRefundNumberGenerator() {
	}

	public static String generate() {
		Date createTime = new Date();
		String changeNo = sdf.format(createTime);
		changeNo += NumberGenerator.generateRandomNumber(3);
		return changeNo;
	}
}
