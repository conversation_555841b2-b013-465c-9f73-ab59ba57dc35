package net.jdair.specialrefund.bizz.utils.pay;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtAlipayPayment;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtPayment;
import net.jdair.specialrefund.bizz.domain.EtWXpayPayment;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.pay.sdk.WXPay;
import net.jdair.specialrefund.bizz.utils.pay.sdk.WXPayConfig;
import net.jdair.specialrefund.bizz.utils.pay.sdk.WXPayConfigProperties;
import net.jdair.specialrefund.bizz.utils.sms.SmsServiceUtil;
import net.jdair.specialrefund.bizz.vo.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.protocol.Protocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 2. 国际票微信退款
 */
@Component
@Slf4j
public class WXpayRefundHandlerGJ extends AbstractRefundHandler {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtWXpayPaymentMapper etWXpayPaymentMapper;

    @Autowired
    private WXPayConfigProperties wxPayConfigProperties;

    @Autowired
    private SmsServiceUtil smsServiceUtil;

    @Override
    @Transactional
    public void doHandler(Map map) throws Exception {
        PayType payType = (PayType) map.get(PayType.class.getName());
        EtPayment etPayment = (EtPayment) map.get(EtPayment.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) map.get(EtFltRefund.class.getName());
        if (isPayType(payType) && ("GJ".equals(etFltRefund.getOrderSource()) || "HWZ".equals(etFltRefund.getOrderSource()))) {
            log.info("WXpayRefundHandlerGJ paymentNo:" + etPayment.getPaymentNo());
            //限制重复退款思路：锁定退票单，更改状态为退款处理中状态，在退款操作时候判断是否正在处理该退款单，否，才允许执行退款操作
            boolean canRefundFalg = false;
            // TODO 锁表
            // helper.lockRefund( fltRefund );
            Boolean canRefund = (Boolean) map.get("canRefund");
            canRefundFalg = canRefund.booleanValue();
            //如果退款单的状态还是PASS说明保存失败的退款记录失败了 就不让退款
            if ("PASS".equals(etFltRefund.getStatus())) {
                canRefundFalg = false;
            }

            String paymentNo = "";
            BigDecimal totalPayAmount = new BigDecimal("0");
            for (EtPayment payment : etFltRefund.getEtOutsidePaymentList()) {
                EtWXpayPayment etWXpayPayment = etWXpayPaymentMapper.findById(payment.getId());
                EtPayment etPayPayment = etPaymentMapper.selectById(payment.getId());
                paymentNo = etWXpayPayment.getDealId();
                totalPayAmount = etPayPayment.getAmount();
                log.info("WXpayRefundHandlerGJ dealid : " + paymentNo + " from " + payment.getPaymentNo());
                break;
            }

            if (canRefundFalg) {
                BigDecimal refundAmount;
                String refundPaymentNo = (String) map.get("refundPaymentNo");
                EtPayment tmpPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", refundPaymentNo));
                if (tmpPayment == null) {
                    throw new Exception("退款失败：保存退款信息失败");
                }
                refundAmount = tmpPayment.getAmount();
                if (tmpPayment.getPayStatus() == PayStatus.PAID.toString()) {
                    log.info("GJ wxpay refund info.has refunded.refundPaymentNo: " + tmpPayment.getPaymentNo() + ", status: " + tmpPayment.getPayStatus().toString());
                    throw new Exception("已经退款");
                }
                //****************************************
                //退款金额，以分为单位。格式"12300"表123元，"1"表1分。
                DecimalFormat df1 = new DecimalFormat("####");
                String ssString = df1.format(refundAmount.multiply(new BigDecimal("100")));
                String refundAmountStr = Integer.valueOf(ssString).toString();
                //支付总金额
                String totalFee = df1.format(totalPayAmount.multiply(new BigDecimal("100")));
                String totalFeetStr = Integer.valueOf(totalFee).toString();

                // 微信配置
                WXPayConfig config = wxPayConfigProperties.getSource().get(etFltRefund.getOrderSource());
                if (null == config) {
                    log.error("WXPayConfig is null");
                    throw new Exception("微信支付配置为空！");
                }

                //获取trans_id
                String trans_id = paymentNo;

                if (null == trans_id || "".equals(trans_id)) {
                    throw new Exception("微信支付流水号不能为空！");
                }
                log.info("WX trans_id is :" + trans_id);
                HashMap<String, String> data = new HashMap<String, String>();
                //微信流水号
                data.put("transaction_id", trans_id);
                //退款的支付流水号
                data.put("out_refund_no", tmpPayment.getPaymentNo());
                //支付总金额
                data.put("total_fee", totalFeetStr);
                //申请退款金额
                data.put("refund_fee", refundAmountStr);
                data.put("refund_fee_type", "CNY");
                data.put("op_user_id", config.getMchID());
                data.put("notify_url", config.getBackendPayCallBackUrl());
                log.info("payment.WXpayRefundHandlerGJ dtoPaymentNo:" + etPayment.getPaymentNo() + ",refundPaymentNo:" + tmpPayment.getPaymentNo() + ",refundAmount:" + refundAmountStr + "分");
                String returnMsg = "";
                String returnCode = "";
                try {
                    WXPay wxpay = new WXPay(config);
                    Map<String, String> r = wxpay.refund(data);
                    log.info("WXpayRefundHandlerGJ res:" + r);
                    returnCode = r.get("result_code");
                    returnMsg = r.get("err_code_des") == null ? returnCode : r.get("err_code_des");

                    tmpPayment.setPayTime(new Date());

                    // 0元返回内容 ：{return_code=FAIL, return_msg=0参数格式错误}
                    if (returnCode == null || "FAIL".equals(returnCode)) {
                        /*
                         * 退款请求失败
                         */
                        map.put("returnResult", PaymentLogStatus.FAILED);
                        tmpPayment.setPayStatus(PayStatus.FAIL.toString());
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + JSON.toJSONString(r) + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if ("SUCCESS".equals(returnCode)) {
                        map.put("returnResult", PaymentLogStatus.SUCCESS);
                        tmpPayment.setPayStatus(PayStatus.PAID.toString());
                        this.updateRefundStatus(etFltRefund, "PAID");
                        smsServiceUtil.sendToRefundSmsQueue(etFltRefund);
                    }
                } catch (Exception e) {
                    log.error("======== Error when posting to WXpay for refund. Please refer to stack trace ========" + e);
                    e.printStackTrace();
                    returnMsg = PaymentLogStatus.ERROR.toString();
                    map.put("returnResult", PaymentLogStatus.ERROR);
                    tmpPayment.setPayStatus(PayStatus.PENDICING.toString());
                    try {
                        // 交易相关ELK日志输出
                        log.info("{\n" +
                                "\"操作类型\":\"退款\",\n" +
                                "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                "\"问题说明\":\"" + e.getMessage() + "\"\n" +
                                "}");
                    } catch (Exception e1) {
                        e.printStackTrace();
                    }
                }
                // Refund Response is Returned
                map.put("returnMsg", returnMsg);
            }
        }
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (next != null){
            next.doHandler(map);
        }
    }

    private boolean isPayType(PayType payType) {
        if (PayType.WX_PAY == payType) {
            return true;
        }
        return false;
    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }

}
