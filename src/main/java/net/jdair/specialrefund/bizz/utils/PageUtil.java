package net.jdair.specialrefund.bizz.utils;


import net.jdair.specialrefund.bizz.controller.backend.page.PageParam;
import java.util.List;

public class PageUtil<T> {
    public PageParam<T> getPage(int pageNum, int pageSize, List<T> data) {
        PageParam<T> page = new PageParam<>();
        Integer pNum = 1;
        Integer pSize = 10;
        Integer fromIndex = 0;
        Integer toIndex = 10;
        pNum = pageNum;
        pSize = pageSize;

        int from = (pNum - 1) * pSize;
        fromIndex = from > data.size() ? data.size() : from;
        toIndex = (fromIndex + pSize) > data.size() ? data.size() : (fromIndex + pSize);
        page.setCurrentPage(pNum);
        page.setPageSize(pSize);
        Long total = (long) data.size();
        page.setTotal(total);
        page.setData(data.subList(fromIndex, toIndex));
        return page;
    }
}