package net.jdair.specialrefund.bizz.utils;

import java.io.Serializable;
import java.math.BigDecimal;

public class NumberUtils implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7559732463824590666L;

	public static double roundtoNearest(double value, double x) {
		BigDecimal valueBd = new BigDecimal(String.valueOf(value));
		BigDecimal multipleBd = new BigDecimal(String.valueOf(x));

		BigDecimal resultBd = valueBd.divide(multipleBd);
		resultBd = resultBd.setScale(0, BigDecimal.ROUND_HALF_UP);
		resultBd = resultBd.multiply(multipleBd);

		// Round to 2 decimal places
		resultBd = resultBd.setScale(2, BigDecimal.ROUND_HALF_UP);

//		System.out.println(value + " rounded to nearest " + x + " is " + resultBd.doubleValue());
		return resultBd.doubleValue();
	}

	/**
	 * Multiply the 2 given doubles. The arguments will be converted to BigDecimal and then perform multiplication by
	 * calling multiply method of BigDecimal. This will solve the decimal places problem with normal multiplication
	 * using * operator.
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static double multiply(double a, double b) {
		BigDecimal x = new BigDecimal(String.valueOf(a));
		BigDecimal y = new BigDecimal(String.valueOf(b));
		BigDecimal result = x.multiply(y);
//		System.out.println(a + " multiply " + b + " is " + result.doubleValue());
		return result.doubleValue();
	}
	
	public static double divide(double value, double divisor) {
		return divide(value, divisor, 10, BigDecimal.ROUND_HALF_UP);
	}
	
	public static double divide(double value, double divisor, int scale, int roundingMode) {
		BigDecimal v = new BigDecimal(String.valueOf(value));
		BigDecimal d = new BigDecimal(String.valueOf(divisor));
		BigDecimal result = v.divide(d, scale, roundingMode);
		return result.doubleValue();
	}

	public static void main(String args[]) {
		BigDecimal a = new BigDecimal("611.1");
		BigDecimal b = new BigDecimal("1450.0");
		BigDecimal r = a.divide(b, 10, BigDecimal.ROUND_HALF_UP);
		System.out.println(r.doubleValue());
	}
	
	public static double percentOf(double value, double percentage) {
		BigDecimal valueBd = new BigDecimal(String.valueOf(value));
		BigDecimal percentageBd = new BigDecimal(String.valueOf(percentage));
		percentageBd = percentageBd.divide(new BigDecimal("100"));
		BigDecimal resultBd = valueBd.multiply(percentageBd);

		return resultBd.doubleValue();
	}
}
