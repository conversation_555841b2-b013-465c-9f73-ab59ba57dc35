package net.jdair.specialrefund.bizz.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5 {
	//生产时该密钥串将以密钥文件的方式另行提供

	/**
	*  生成MD5字符串
	* 
	* @param str  待加密字符串
	* @return
	*/
	public static String generate(String str) { 
		MessageDigest messageDigest = null; 
		try { 
				messageDigest = MessageDigest.getInstance("MD5"); 
				messageDigest.reset(); 
				messageDigest.update(str.getBytes("UTF-8")); 
			} catch (NoSuchAlgorithmException e) { 
				e.printStackTrace(); 
			} catch (UnsupportedEncodingException e) { 
				e.printStackTrace(); 
			}
			if (messageDigest != null) {
				byte[] byteArray = messageDigest.digest();
				StringBuffer md5StrBuff = new StringBuffer();
				for (int i = 0; i < byteArray.length; i++) {
					if (Integer.toHexString(0xFF & byteArray[i]).length() == 1)
						md5StrBuff.append("0").append(Integer.toHexString(0xFF &  byteArray[i]));
					else
						md5StrBuff.append(Integer.toHexString(0xFF &  byteArray[i]));
				}
				return md5StrBuff.toString();
			}
			return "";
	}
		public static void main(String[] args) {
			System.out.println(MD5.generate("龙腾出行"));
		}
	}