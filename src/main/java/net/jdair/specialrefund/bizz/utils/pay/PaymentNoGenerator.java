package net.jdair.specialrefund.bizz.utils.pay;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Title : PaymentNoGenerator
 * Description : This is to generate the payment no
 *
 */
public class PaymentNoGenerator {
    private static Log log = LogFactory.getLog(PaymentNoGenerator.class);

    public static String generate(long sequenceNo) {

        String seqNo = String.valueOf(sequenceNo);
        if (seqNo.length() > 6) {
            seqNo = seqNo.substring(seqNo.length() - 6);
        }
        seqNo = padding(seqNo, 6) + seqNo;

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String dt = format.format(new Date());

        seqNo = dt + seqNo;

        log.debug("seqNo generatored= [" + seqNo + "]");

        return seqNo;
    }

    /**
     * <pre>生成股份优惠机票支付流水号: yyyyMMddHHmmss + 5 + 7位序列号</pre>
     *
     * @param sequenceNo
     * @return
     * @Auther 郑兴(zhengxing)
     * @Date 2013-12-12
     */
    public static String generate4YHJP(long sequenceNo) {
        String seqNo = String.valueOf("0000000" + sequenceNo);
        seqNo = "5" + seqNo.substring(seqNo.length() - 7);
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String dt = format.format(new Date());

        seqNo = dt + seqNo;

        log.debug("seqNo generatored= [" + seqNo + "]");

        return seqNo;
    }


    public static String generate4TS(long sequenceNo) {

        String seqNo = String.valueOf(sequenceNo);
        if (seqNo.length() > 1) {
            seqNo = seqNo.substring(seqNo.length() - 1);
        }
        seqNo = padding(seqNo, 1) + seqNo;

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dt = format.format(new Date());

        seqNo = dt + seqNo;

        log.debug("seqNo generatored= [" + seqNo + "]");

        return seqNo;
    }

    private static String padding(String s, int i) {
        StringBuffer sb = new StringBuffer();
        if (s.length() < i) {
            for (int j = 0, j_total = i - s.length(); j < j_total; j++)
                sb.append("0");
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        generate(11112);
    }
}
