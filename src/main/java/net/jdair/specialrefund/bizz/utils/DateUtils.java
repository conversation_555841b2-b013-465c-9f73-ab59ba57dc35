
package net.jdair.specialrefund.bizz.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static DateFormat dateTimeFormatter = new SimpleDateFormat(
            "yyyy-MM-dd HH:mm:ss");
    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    public static String dateToString(Date date) {
        if (date == null)
            return "";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date).trim();

    }

    public static String dateToString(Date date, String format) {
        if (date == null)
            return "";
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date).trim();

    }

    public static Date StringToDate(String date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date obj = null;
        try {
            obj = formatter.parse(date);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return obj;

    }

    public static Date StringToDate(String date, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        Date obj = null;
        try {
            obj = formatter.parse(date);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return obj;
    }

    public static Date StringTODateTime(String CST_time) {// add by zhouhui

        Date obj = null;

        try {

            SimpleDateFormat sdf = new SimpleDateFormat(
                    "EEE MMM dd HH:mm:ss 'CST' yyyy", Locale.US);

            obj = sdf.parse(CST_time);// 构造Date对象

        } catch (Exception e) {

            e.printStackTrace();

        }

        return obj;

    }

    /**
     * Compare two flight times up to the minute.
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean isSameFlightTime(Date time1, Date time2) {
        return (compare(time1, time2) == 0);
    }

    /**
     * Compare two dates up to the minute, ignoring seconds and milliseconds.
     *
     * @param date1
     * @param date2
     * @return the value 0 if date1 and date2 are equal; a value less than 0 if
     * date1 is before date2; and a value greater than 0 if date1 is
     * after date2.
     */
    public static int compare(Date date1, Date date2) {
        return compare(date1, date2, Calendar.MINUTE);
    }

    /**
     * 增加小时
     *
     * @param date
     * @param add
     * @return
     */
    public static Date addHour(Date date, int add) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY, add);
        return cal.getTime();

    }

    public static Date addDay(Date date, int add) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, add);
        return cal.getTime();

    }

    /**
     * Compare two dates up to the specified field.
     *
     * @param date1
     * @param date2
     * @param field the field from Calendar or SEMI_MONTH
     * @return the value 0 if date1 and date2 are equal; a value less than 0 if
     * date1 is before date2; and a value greater than 0 if date1 is
     * after date2.
     */
    public static int compare(Date date1, Date date2, int field) {
        Date d1 = org.apache.commons.lang.time.DateUtils.truncate(date1, field);
        Date d2 = org.apache.commons.lang.time.DateUtils.truncate(date2, field);

        return d1.compareTo(d2);
    }

    /**
     * 一个非常傻的方法
     * <p>
     * 将Date转换成String然后转回来，为了不让oracle出错，不知道是Hibernate的问题还是oracle的问题
     * <p>
     * 总之能解决就是好方法
     *
     * @param date
     * @return
     */
    public static Date convertOracleDate(Date date) {
        try {
            return dateTimeFormatter.parse(dateTimeFormatter.format(date));
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * @param date1
     * @param date2
     * @return
     * <AUTHOR>
     * @description
     * @date 2008-12-1
     */
    public static boolean isSameDay(Date date1, Date date2) {
        return org.apache.commons.lang.time.DateUtils.isSameDay(date1, date2);
    }

    /**
     * <AUTHOR> 2012-3-5 指定日期+天数
     */
    public static Date addDay(Date date, int daysum, Date fltDate) {
        Calendar calen = Calendar.getInstance();
        calen.setTime(date);
        calen.add(Calendar.DAY_OF_YEAR, daysum);
        Date redate = calen.getTime();
        if (redate.after(fltDate) == true) {
            return fltDate;
        } else if (redate.before(fltDate) == true) {
            return redate;
        } else {
            return fltDate;
        }
    }

    /**
     * 获取 当前日期 往后N天的 天数集合 例 {20131003,20131004,20131005}
     */
    public static String[] getDateStrsByDays(int days) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String[] strs = new String[days];
        for (int i = 1; i <= days; i++) {
            Calendar calen = Calendar.getInstance();
            calen.add(Calendar.DAY_OF_MONTH, i);
            Date d = calen.getTime();
            strs[i - 1] = sdf.format(d);
        }
        return strs;
    }

    public static long getDistanceBetweenDays(Date before, Date after) {
        try {
            long diff = after.getTime() - before.getTime();
            long days = diff / (1000 * 60 * 60 * 24);
            return days;
        } catch (Exception e) {
            return 0l;
        }
    }

    /**
     * 判断开始时间和结束时间之差是否超过指定的天数(distanceDays)
     *
     * @param startDateStr
     * @param endDateStr
     * @param distanceDays 相差的天数
     * @return true--超过  false--没有超过
     */
    public static boolean isOverDistanceBetweenDays(String startDateStr, String endDateStr, long distanceDays) {
        Date startDate = StringToDate(startDateStr);
        Date endDate = StringToDate(endDateStr);
        long distanceDaysTmp = getDistanceBetweenDays(startDate, endDate);
        if (distanceDaysTmp < 0 || distanceDaysTmp > distanceDays) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

}
