/**
*Copyright (c) HNA SYSTEMS CO.,LTD
*
*@date 2008-11-18
*
*Original Author: 王勇明(yongm_wang)
*
*ChangeLog:
*
* 
*/

package net.jdair.specialrefund.bizz.utils.pay;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 支付宝退款账号配置
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "pay.alipay")
public class AlipayConfig {

	private Map<String, Config> source;

	@Data
	public static class Config {
		private String accountId;
		private String merchantId;
		private String appId;
		private String appPrivateKey;
		private String appPublicKey;
		private String aliPublicKey;
		private String md5Key;
		private String payUrl;
		private String payCallBackUrl;
		private String backendPayCallBackUrl;
		private String alipaymentNotifyUrl;
		private String refundNotifyUrl;
		private String refundUrl;
		private String alipayNotifyUrl;
		private String effectiveDate;
	}

}
