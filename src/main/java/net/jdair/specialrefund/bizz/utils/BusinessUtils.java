package net.jdair.specialrefund.bizz.utils;


import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;

/**
 *
 */
@Slf4j
public class BusinessUtils {

	/**
	 * 参数签名
	 * @param params
	 * @return
	 */
	public static String generateSign(Map<String, Object> params) {
		try {
			Map<String, Object> sortMap = new TreeMap<String, Object>(new Comparator<String>() {
				@Override
				public int compare(String key1, String key2) {
					return key1.compareTo(key2);
				}
			});
			sortMap.putAll(params);
			String secret = "CrMh4N$A";
			StringBuilder origin = new StringBuilder(secret);
			for (String key : sortMap.keySet()) {
				origin.append(key);
				origin.append(sortMap.get(key));
			}
			origin.append(secret);
			log.debug("签名明文[" + origin.toString() + "]");
			String signed = MD5Util.md5Hex(URLEncoder.encode(origin.toString(), "UTF-8")).toUpperCase();
			log.debug("签名密文[" + signed + "]");
			return signed;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}
}
