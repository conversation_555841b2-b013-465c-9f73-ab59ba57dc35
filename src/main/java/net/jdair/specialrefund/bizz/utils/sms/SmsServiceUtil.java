package net.jdair.specialrefund.bizz.utils.sms;

import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.CustomerMapper;
import net.jdair.specialrefund.bizz.mapper.EtPassengerMapper;
import net.jdair.specialrefund.bizz.mapper.EtTicketMapper;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.utils.TextUtil;
import net.jdair.specialrefund.bizz.vo.DTOFltRefundPaxSeg;
import net.jdair.specialrefund.bizz.vo.DoAuditing;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Set;

@Component
@Slf4j
public class SmsServiceUtil {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private SmsClient smsClient;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    /**
     * 国际票审核操作发送短信
     *
     * @param fltRefund
     * @param isPass
     * @param amount2SendMsg
     */
    public void sendMessage4AuditRefundForGJ(EtFltRefund fltRefund, String isPass, double amount2SendMsg) {
        try {
            String message = "";
            String ticketNo = "";
            String name = "";
            HashSet<String> names = new HashSet<String>();
            for (EtPassengerSegment etPassengerSegment : fltRefund.getEtPassengerSegmentList()) {
                try {
                    EtPassenger passenger = etPassengerMapper.selectById(etPassengerSegment.getPassengerId());
                    Customer customer = customerMapper.selectById(passenger.getCustomerId());
                    EtTicket etTicket = etTicketMapper.selectById(etPassengerSegment.getTicketId());
                    if (passenger != null && !names.contains(customer.getName())) {
                        name += customer.getName() + "、";
                        names.add(customer.getName());
                    }

                    if (!ticketNo.contains(etTicket.getTicketNo())) {
                        if (ticketNo.length() == 0) {
                            ticketNo += etTicket.getIssCode() + "-" + etTicket.getTicketNo() + "/";
                        } else {
                            ticketNo += etTicket.getTicketNo() + "/";
                        }
                    }
                } catch (Exception e) {
                    log.info("sendMessage4AuditRefundForGJ get segment error: " + e.getMessage(), e);
                }
            }
            if (ticketNo.length() != 0) {
                ticketNo = ticketNo.substring(0, ticketNo.length() - 1);
            }
            if (name.length() != 0) {
                name = name.substring(0, name.length() - 1);
            }

            //1pass 2unpass
            if (isPass.equals("PASS")) {
                if (amount2SendMsg == 0) {
                    //您XXXX年XX月XX日提交的退票申请，经审核退票手续费高于票款总额，退款金额为零，如有问题可联系首都航空客服热线95375。
                    if (StringUtils.hasText(smsConfig.getGjRefundPass0())) {
                        message = smsConfig.getGjRefundPass0();
                        if (message.indexOf("{date}") != -1) {
                            message = message.replace("{date}", DateUtils.dateToString(fltRefund.getCreateTime(), "yyyy年MM月dd日"));
                        }
                    }
                } else {
                    if (StringUtils.hasText(smsConfig.getRefundPass())) {
                        message = smsConfig.getRefundPass();

                        if (message.indexOf("{name}") != -1) {
                            message = message.replace("{name}", name);
                        }
                        if (message.indexOf("{amout}") != -1) {
                            message = message.replace("{amout}", amount2SendMsg + "");
                        }
                        if (message.contains("{ticketNo}")) {
                            message = message.replace("{ticketNo}", ticketNo);
                        }
                    }
                }
            } else if (isPass.equals("UNPASS")) {
                if (amount2SendMsg == 0) {
                    //尊敬的旅客，您{date}提交的非自愿退票审核未通过，按自愿退票处理，经审核退票手续费高于票款总额，退款金额为零，如有问题可联系首都航空客服热线95375。
                    if (StringUtils.hasText(smsConfig.getGjRefundUnpass0())) {
                        message = smsConfig.getGjRefundUnpass0();
                        if (message.indexOf("{date}") != -1) {
                            message = message.replace("{date}", DateUtils.dateToString(fltRefund.getCreateTime(), "yyyy年MM月dd日"));
                        }
                    }
                } else {
                    if (StringUtils.hasText(smsConfig.getRefundUnPass())) {
                        message = smsConfig.getRefundUnPass();
                        if (message.contains("{ticketNo}")) {
                            message = message.replace("{ticketNo}", ticketNo);
                        }
                        if (message.indexOf("{amout}") != -1) {
                            message = message.replace("{amout}", amount2SendMsg + "");
                        }
                    }
                }
            } else {
                if (StringUtils.hasText(smsConfig.getRefundRefuse())) {
                    message = smsConfig.getRefundRefuse();
                    if (message.contains("{ticketNo}")) {
                        message = message.replace("{ticketNo}", ticketNo);
                    }
                }
                //message.append("尊敬的旅客，您"+DateUtils.dateToString(fltRefund.getCreateTime(),"yyyy年MM月dd日")+"提交的退票的病退材料尚不符合条件，详情可登陆首都航空官网【我的订单】进行查询。感谢您选择首都航空。首都航空唯一官方客服热线95071999（境外拨打8689895071999）。");
            }
            log.info("SmsServerUtil GJ refund message == " + message);

            for (EtPassengerSegment etPassengerSegment : fltRefund.getEtPassengerSegmentList()) {
                Long passengerId = etPassengerSegment.getPassengerId();
                if (passengerId != null) {
                    EtPassenger passenger = etPassengerMapper.selectById(passengerId);
                    if (passenger != null) {
                        String mobileNo = passenger.getMobilePhone();
                        if (StringUtils.hasText(mobileNo)) {
                            smsClient.sendSms(new String[]{mobileNo}, false, message.toString());
                            log.info(fltRefund.getRefundNo() + "'s refund audit SMS send success, to mobile " + mobileNo);
                            break;
                        } else {
                            log.error("Error during sending message to customer gj'" + fltRefund.getRefundNo() + "'" + mobileNo + " mobileNo has not found!");
                        }
                    } else {
                        log.error("Error during sending message to customer gj'" + fltRefund.getRefundNo() + "'" + passengerId + " has not found!");
                    }
                } else {
                    log.error("Error during sending message to customer gj'" + fltRefund.getRefundNo() + "'" + passengerId + " passenger has not found!");
                }
            }
        } catch (Exception e) {
            log.info("Error during sending message to customer gj'" + fltRefund.getRefundNo() + ":" + e.getMessage(), e);
        }
    }


    /**
     * @throws
     * @Title: sendToRefundSmsQueue
     * @Description: 发送退款成功短信
     * @param: @param refundId
     * @return: void
     */
    public void sendToRefundSmsQueue(EtFltRefund fr) {
        try {
            if ("BC".equals(fr.getOrderSource())
                    || "TS".equals(fr.getOrderSource())
                    || "TSGJ".equals(fr.getOrderSource())
            ) {
                return;
            }//旅客补偿和特殊退票不发短信

            StringBuffer name = new StringBuffer();
            StringBuffer ticket = new StringBuffer();
            StringBuffer segment = new StringBuffer();
            BigDecimal amout = new BigDecimal(0);
            HashSet<String> tickets = new HashSet<String>();
            HashSet<String> names = new HashSet<String>();
            HashSet<String> segments = new HashSet<String>();
            HashSet<String> mobileNos = new HashSet<String>();

            for (EtFltRefundPaxSeg etFltRefundPaxSeg : fr.getEtFltRefundPaxSegList()) {
                amout = amout.add(etFltRefundPaxSeg.getActualRefundAmount());
            }

            for (EtPassengerSegment etPassengerSegment : fr.getEtPassengerSegmentList()) {
                if (ticket.length() == 0) {
                    ticket.append(etPassengerSegment.getEtTicket().getIssCode() + "-" + etPassengerSegment.getEtTicket().getTicketNo());
                    tickets.add(etPassengerSegment.getEtTicket().getTicketNo());
                } else {
                    if (!tickets.contains(etPassengerSegment.getEtTicket().getTicketNo())) {
                        ticket.append("/" + etPassengerSegment.getEtTicket().getTicketNo());
                        tickets.add(etPassengerSegment.getEtTicket().getTicketNo());
                    }
                }

                mobileNos.add(etPassengerSegment.getEtPassenger().getMobilePhone());

                if (name.length() == 0) {
                    name.append(etPassengerSegment.getEtPassenger().getCustomer().getName());
                    names.add(etPassengerSegment.getEtPassenger().getCustomer().getName());
                } else {
                    if (!names.contains(etPassengerSegment.getEtPassenger().getCustomer().getName())) {
                        name.append("、" + etPassengerSegment.getEtPassenger().getCustomer().getName());
                        names.add(etPassengerSegment.getEtPassenger().getCustomer().getName());
                    }
                }

                ResourceBundle cityBundle = ResourceBundle.getBundle("pgs-city");

                if (segment.length() == 0) {
                    segment.append(DateUtils.dateToString(etPassengerSegment.getEtSegment().getDepTime(), "MM月dd日HH:mm"));
                    segment.append(cityBundle.getString(etPassengerSegment.getEtSegment().getDepCode()) + "-" + cityBundle.getString(etPassengerSegment.getEtSegment().getArrCode()));
                    segment.append(etPassengerSegment.getEtSegment().getFlightNo());
                    segments.add(cityBundle.getString(etPassengerSegment.getEtSegment().getDepCode()) + "-" + cityBundle.getString(etPassengerSegment.getEtSegment().getArrCode()));
                } else {
                    if (!segments.contains(cityBundle.getString(etPassengerSegment.getEtSegment().getDepCode()) + "-" + cityBundle.getString(etPassengerSegment.getEtSegment().getArrCode()))) {
                        segment.append("、" + DateUtils.dateToString(etPassengerSegment.getEtSegment().getDepTime(), "MM月dd日HH:mm"));
                        segment.append(cityBundle.getString(etPassengerSegment.getEtSegment().getDepCode()) + "-" + cityBundle.getString(etPassengerSegment.getEtSegment().getArrCode()));
                        segment.append(etPassengerSegment.getEtSegment().getFlightNo());
                        segments.add(cityBundle.getString(etPassengerSegment.getEtSegment().getDepCode()) + "-" + cityBundle.getString(etPassengerSegment.getEtSegment().getArrCode()));
                    }
                }
            }
            String message = smsConfig.getRefundMoney();

            if (message.indexOf("{name}") != -1) {
                message = message.replace("{name}", name.toString());
            }
            if (message.indexOf("{ticket}") != -1) {
                message = message.replace("{ticket}", ticket.toString());
            }
            if (message.indexOf("{amout}") != -1) {
                message = message.replace("{amout}", amout.setScale(1, BigDecimal.ROUND_DOWN).toString());
            }

//			您的9月15日22:30青岛-西安JD5240航班，票号898-8505973305/8505973306已申请退票并完成退款，退款金额398.0元，请您注意查收。如有疑问请致电客服热线95375
//			JD.refundMoney.message=旅客{name}，票号{ticket}的退票申请已完成退款，退款金额{amout}元，请您注意查收。
//			JD.autoRefundMoney.message=您的{segment}航班，票号{ticket}已申请退票并完成退款，退款金额{amout}元，请您注意查收。如有疑问请致电客服热线95375。
            //按公司账号发短信
            if ("GJ".equals(fr.getOrderSource()) || "HWZ".equals(fr.getOrderSource())) {//国际的
                for (String mobileNo : mobileNos) {
                    if (StringUtils.hasText(mobileNo)) {
                        smsClient.sendSms(new String[]{mobileNo}, false, message.toString());
                        log.info(fr.getRefundNo() + "'s issue SMS send success,to mobile " + mobileNo);
                        break;
                    } else {
                        log.info("Error during sending message to customer gj'" + fr.getRefundNo() + "'" + mobileNo + " mobileNo has not found!");
                    }
                }
            }
        } catch (Exception ex) {
            log.info("sendToRefundSmsQueue error: " + ex.getMessage(), ex);
        }
    }

    /**
     * 特殊退票 --- 发送短信
     *
     * @param fltRefund
     */
    public void sendMessage4SpecialRefund(EtFltRefund fltRefund, DoAuditing auditAction) {
        try {
            String message = "";
            String ticketNo = "";
            for (EtPassengerSegment etPassengerSegment : fltRefund.getEtPassengerSegmentList()) {
                ticketNo += etPassengerSegment.getEtTicket().getIssCode() + "-" + etPassengerSegment.getEtTicket().getTicketNo() + ",";
            }
            ticketNo = ticketNo.substring(0, ticketNo.length() - 1);

            String mobileNo = fltRefund.getEtSpecialRefundBankInfo().getMobileNo();

            // 退款失败，驳回为一审拒绝时
            if (DoAuditing.FAIL.getValue().equals(fltRefund.getStatus()) && auditAction == DoAuditing.FREJECT) {
                message = smsConfig.getTsRefundFailFRejectMsg();
                if (message.contains("{ticketNo}")) {
                    message = message.replace("{ticketNo}", ticketNo);
                }
                if (message.contains("{refundFailRejectRemark}")) {
                    message = message.replace("{refundFailRejectRemark}", fltRefund.getRemark());
                }
            }
            // 一审拒绝时
            else if (auditAction == DoAuditing.FREJECT) {
                message = smsConfig.getTsRefundFRejectMsg();
                if (message.contains("{ticketNo}")) {
                    message = message.replace("{ticketNo}", ticketNo);
                }
                if (message.contains("{firstRejectRemark}")) {
                    message = message.replace("{firstRejectRemark}", fltRefund.getRemark());
                }
            }
            // "退款成功"时
            else if (auditAction == DoAuditing.PAID) {
                message = smsConfig.getTsRefundMoneySuccessMsg();
                if (message.contains("{ticketNo}")) {
                    message = message.replace("{ticketNo}", ticketNo);
                }
            }

            log.info("FlightRefundServiceImpl sendMessage4SpecialRefund message==" + message);
            smsClient.sendSms(new String[]{mobileNo}, false, message.toString());
            log.debug(fltRefund.getRefundNo() + "'s refund audit SMS send success...");
        } catch (Exception e) {
            log.debug("Error during sending message to customer '" + fltRefund.getRefundNo() + "'");
            e.printStackTrace();
        }
    }

}
