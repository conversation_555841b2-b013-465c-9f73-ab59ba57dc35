package net.jdair.specialrefund.bizz.utils.pay;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 在线退款 -- 抽象处理类
 *
 * @param <T>
 */
@Component
public abstract class AbstractRefundHandler<T> {
    /**
     * 责任链，下一个链接节点
     */
    protected AbstractRefundHandler<T> next = null;

    /**
     * 内部逻辑
     *
     * @param map 参数
     */
    public abstract void doHandler(Map map) throws Exception;

    public void next(AbstractRefundHandler handler) {
        this.next = handler;
    }

    public static class Builder<T> {
        private AbstractRefundHandler<T> head;
        private AbstractRefundHandler<T> tail;

        public Builder<T> addHandler(AbstractRefundHandler handler) {
            if (this.head == null) {
                this.head = handler;
            } else {
                this.tail.next(handler);
            }
            this.tail = handler;
            return this;
        }

        public AbstractRefundHandler build() {
            return this.head;
        }
    }
}
