package net.jdair.specialrefund.bizz.utils.pay;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.vo.PayAction;
import net.jdair.specialrefund.bizz.vo.PayStatus;
import net.jdair.specialrefund.bizz.vo.PayType;
import net.jdair.specialrefund.common.exception.GlobalException;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 1. 国际票支付宝退款，预处理保存支付宝退款失败的信息
 */
@Component
@Slf4j
public class SaveAlipayFailedRefundPaymentHandlerGJ extends AbstractRefundHandler {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtAlipayPaymentMapper etAlipayPaymentMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private AlipayConfig alipayConfig;

    @Autowired
    private EtPaymentNoGeneratorMapper etPaymentNoGeneratorMapper;

    @Override
    @Transactional
    public void doHandler(Map map) throws Exception {
        PayType payType = (PayType) map.get(PayType.class.getName());
        EtPayment etPayment = (EtPayment) map.get(EtPayment.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) map.get(EtFltRefund.class.getName());
        if(isPayType(payType) && ("GJ".equals(etFltRefund.getOrderSource()) || "HWZ".equals(etFltRefund.getOrderSource()))) {
            boolean canRefundFalg = false;

            String paymentNo = etPayment.getPaymentNo();
            log.info("SaveAlipayFailedRefundPaymentHandlerGJ fltRefund=" + etFltRefund + "..........");
            if (etFltRefund.getEtRefundPaymentList() == null || etFltRefund.getEtRefundPaymentList().size() == 0) {
                if ("PASS".equals(etFltRefund.getStatus())) {
                    canRefundFalg = true;
                }
            } else {
                throw new Exception("该退票单已经退款");
            }

            map.put("canRefund", canRefundFalg);

            if (canRefundFalg) {
                try {
                    EtPayment tpPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", etPayment.getPaymentNo()));

                    // 支付宝配置
                    AlipayConfig.Config config;
                    if (etFltRefund.getRetrieveTimeFlag() != 1) {
                        if (etFltRefund.getOrderSource().equals("GJ") && tpPayment.getPayTime().after(DateUtils.StringToDate(alipayConfig.getSource().get("GJNew").getEffectiveDate()))) {
                            config = alipayConfig.getSource().get("GJNew");
                        } else {
                            config = alipayConfig.getSource().get(etFltRefund.getOrderSource());
                        }
                    } else {
                        config = alipayConfig.getSource().get("GJWithInsurance");
                    }
                    BigDecimal refundAmount;
                    refundAmount = this.calcRefundAmount(etFltRefund);

                    // long seqNo = etPaymentMapper.generatePaymentNoSeq();
                    EtPaymentNoGenerator etPaymentNoGenerator = etPaymentNoGeneratorMapper.selectOne(new QueryWrapper<EtPaymentNoGenerator>()
                            .eq("refund_no", etFltRefund.getRefundNo())
                            .eq("payment_no_status", "WAIT_USE")
                    );
                    etPaymentNoGenerator.setPaymentNoStatus("USED");
                    etPaymentNoGenerator.setUsedTime(new Date());
                    etPaymentNoGeneratorMapper.updateById(etPaymentNoGenerator);
                    String seqNum = etPaymentNoGenerator.getPaymentNo();
                    etPayment.setMerchantId(config.getMerchantId());
                    etPayment.setCurrency("RMB");
                    etPayment.setPaymentNo(seqNum);
                    etPayment.setBatch("0"); // currently set as not batch
                    etPayment.setPayTime(new Date());

                    etPayment.setPayStatus(PayStatus.PENDING.toString());
                    etPayment.setAction(PayAction.REFUND.toString());
                    etPayment.setAmount(refundAmount);
                    etPayment.setPayType(tpPayment.getPayType());

                    //先保存退款单信息
                    etFltRefund.setStatus("FAIL");
                    // 1.退票单状态先更新为失败
                    etFltRefundMapper.updateById(etFltRefund);
                    // 2.新增退款支付记录
                    EtAlipayPayment etAlipayPayment = new EtAlipayPayment();
                    try {
                        BeanUtils.copyProperties(etAlipayPayment, etPayment);
                    } catch (Exception e) {
                        throw new RuntimeException("Error copying properties of : " + etPayment.getClass().getName(), e);
                    }
                    etPaymentMapper.insert(etAlipayPayment);
                    etAlipayPaymentMapper.insert(etAlipayPayment);
                    // 3.新增退款记录中间表记录
                    EtFltRefundPayment etFltRefundPayment = new EtFltRefundPayment();
                    etFltRefundPayment.setFltRefundId(etFltRefund.getId());
                    etFltRefundPayment.setPaymentId(etAlipayPayment.getId());
                    etFltRefundPayment.setAmount(etPayment.getAmount());
                    etFltRefundPaymentMapper.insert(etFltRefundPayment);

                    map.put("refundPaymentNo", seqNum);

                } catch (Exception e) {
                    log.error("save failed alipay refund record failed.original paymentNo:" + paymentNo);
                    throw new Exception("保存退款信息失败");
                }
            }
        }
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (next != null){
            next.doHandler(map);
        }
    }

    private boolean isPayType(PayType payType) {
        if (PayType.ALIPAY == payType) {
            return true;
        }
        return false;
    }

    /**
     * 计算退款金额
     * @param etFltRefund
     * @return
     */
    private BigDecimal calcRefundAmount(EtFltRefund etFltRefund) {
        BigDecimal totalRefundAmount = new BigDecimal("0");

        for (EtFltRefundPaxSeg frps : etFltRefund.getEtFltRefundPaxSegList()) {
            totalRefundAmount = totalRefundAmount.add(frps.getActualRefundAmount());
        }

        return totalRefundAmount;
    }
}
