package net.jdair.specialrefund.bizz.utils.pay;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtAlipayPayment;
import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.domain.EtPayment;
import net.jdair.specialrefund.bizz.mapper.EtAlipayPaymentMapper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper;
import net.jdair.specialrefund.bizz.mapper.EtFltRefundPaymentMapper;
import net.jdair.specialrefund.bizz.mapper.EtPaymentMapper;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.vo.DoAuditing;
import net.jdair.specialrefund.bizz.vo.PayStatus;
import net.jdair.specialrefund.bizz.vo.PayType;
import net.jdair.specialrefund.bizz.vo.PaymentLogStatus;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.protocol.Protocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 2. 国际票支付宝退款
 */
@Component
@Slf4j
public class AlipayRefundHandlerGJ extends AbstractRefundHandler {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private EtAlipayPaymentMapper etAlipayPaymentMapper;

    @Autowired
    private AlipayConfig alipayConfig;

    @Override
    @Transactional
    public void doHandler(Map map) throws Exception {
        PayType payType = (PayType) map.get(PayType.class.getName());
        EtPayment etPayment = (EtPayment) map.get(EtPayment.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) map.get(EtFltRefund.class.getName());
        if (isPayType(payType)
                && (("GJ".equals(etFltRefund.getOrderSource()) && etFltRefund.getRetrieveTimeFlag() != 1) || "HWZ".equals(etFltRefund.getOrderSource()))) {
            log.info("AlipayRefundHandlerGJ paymentNo:" + etPayment.getPaymentNo());
            //限制重复退款思路：锁定退票单，更改状态为退款处理中状态，在退款操作时候判断是否正在处理该退款单，否，才允许执行退款操作
            boolean canRefundFalg = false;
            // TODO 锁表
            // helper.lockRefund( fltRefund );
            Boolean canRefund = (Boolean) map.get("canRefund");
            canRefundFalg = canRefund.booleanValue();
            //如果退款单的状态还是PASS说明保存失败的退款记录失败了 就不让退款
            if ("PASS".equals(etFltRefund.getStatus())) {
                canRefundFalg = false;
            }

            String paymentNo = "";
            Date payTime = null;
            for (EtPayment payment : etFltRefund.getEtOutsidePaymentList()) {
                EtAlipayPayment alipayPayment = etAlipayPaymentMapper.findById(payment.getId());
                paymentNo = alipayPayment.getDealId();
                payTime = payment.getPayTime();
                log.info("AlipayRefundHandlerGJ dealid : " + paymentNo + " from " + payment.getPaymentNo());
                break;
            }

            if (canRefundFalg) {
                try {
                    BigDecimal refundAmount;
                    String refundPaymentNo = (String) map.get("refundPaymentNo");
                    EtPayment tmpPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", refundPaymentNo));
                    if (tmpPayment == null) {
                        throw new Exception("退款失败：保存退款信息失败");
                    }
                    refundAmount = tmpPayment.getAmount();
                    if (tmpPayment.getPayStatus() == PayStatus.PAID.toString()) {
                        log.info("GJ alipay refund info.has refunded.refundPaymentNo: " + tmpPayment.getPaymentNo() + ", status: " + tmpPayment.getPayStatus().toString());
                        throw new Exception("已经退款");
                    }
                    //****************************************
                    // 支付宝配置
                    AlipayConfig.Config config = null;
                    if (etFltRefund.getOrderSource().equals("GJ") && payTime.after(DateUtils.StringToDate(alipayConfig.getSource().get("GJNew").getEffectiveDate()))) {
                        config = alipayConfig.getSource().get("GJNew");
                    } else {
                        config = alipayConfig.getSource().get(etFltRefund.getOrderSource());
                    }
                    String paygateway = config.getRefundUrl().trim(); //支付接口（不可修改）
                    String service = "refund_fastpay_by_platform_nopwd";//服务名称--退款服务（不可修改）

                    String sign_type = "MD5"; //签名方式（不可修改）
                    String input_charset = "utf-8";  //编码机制
                    //*****************************************************
                    //partner和key提取方法：登陆签约支付宝账户--->点击“商家服务”就可以看到

                    String partner = config.getMerchantId().trim(); //partner合作伙伴ID(必填)
                    String key = config.getMd5Key().trim(); //partner账户对应的支付宝安全校验码(必填)

                    String notify_url = config.getAlipaymentNotifyUrl().trim();//服务器通知url（Alipay_Notify.asp自己的服务所在绝对路经）
                    //======业务参数====
                    String batch_no = null;        //20141219	支付宝支付流水号与ET支付号生成规则很类似，所以用支付号作为流水号。这样改动方便了支付回调的处理。getDate() + getThree(); //批次号规则 “日期”+“12位随机码”20060702001123456789
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String refund_date = formatter.format(new Date()).trim(); //退款日期，可以选择当天  yyyy-MM-dd hh:mm:ss
                    String batch_num = "1";//退款总笔数

					/*
					退款详细数据:  交易退款信息$收费退款信息|分润退款信息|分润退款信息
					例子(单笔数据集)：2009042770320839^0.03^交易退款|<EMAIL>^^<EMAIL>^^0.01^退分润|<EMAIL>^^<EMAIL>^^0.01^退分润
					“退款详细数据”可以见开发文档
					*/
                    batch_no = refundPaymentNo;

                    String detail_data = paymentNo + "^" + refundAmount + "^";
                    //String detail_data = "2010072767359580"+"^"+"0.01"+"^";
                    String ItemUrl = CreateUrl(paygateway, input_charset,
                            service, partner, sign_type, batch_no, refund_date,
                            batch_num, detail_data, notify_url, key);

                    log.info("GJ original paymentNo:" + paymentNo + ", refundPaymentNo:" + refundPaymentNo + ", 支付宝退款请求URL：" + ItemUrl + partner);

                    Protocol myHttps = new Protocol("https", new MySSLProtocolSocketFactory(), 443);
                    Protocol.registerProtocol("https", myHttps);
                    HttpClient httpclient = new HttpClient();
                    httpclient.setConnectionTimeout(5000);
                    httpclient.setTimeout(5000);
                    httpclient.getHostConfiguration().setHost("mapi.alipay.com", 443, myHttps);

                    String resultString = "";
                    try {
                        HttpMethod method = new GetMethod(ItemUrl);

                        httpclient.executeMethod(method);
                        log.info("GJ original paymentNo:" + paymentNo + ",支付宝退款结果：" + method.getResponseBodyAsString());
                        resultString = method.getResponseBodyAsString();
                        Map resultMap = handleRefundResponse(etPayment, resultString, map);
                    } catch (Exception e1) {
                        etPayment.setPayStatus(PayStatus.PENDICING.toString());
                        resultString = PaymentLogStatus.ERROR.toString();
                        log.error("GJ alipay refund fail.original paymentNo:" + paymentNo + ",err:" + e1.getMessage(), e1);
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + e1.getMessage() + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    if (PaymentLogStatus.SUCCESS.equals(map.get("returnResult"))) {
                        /*
                         * 退款请求成功
                         */
                        this.updateRefundStatus(etFltRefund, "PEND");
                    }
                    if (PaymentLogStatus.ERROR.toString().equals(resultString.trim())) {
                        /*
                         * 退款请求异常
                         */
                        this.updateRefundStatus(etFltRefund, "PENDICING");
                        try {
                            // 交易相关ELK日志输出
                            log.info("{\n" +
                                    "\"操作类型\":\"退款\",\n" +
                                    "\"宙核单号\":\"" + etFltRefund.getRefundNo() + "\",\n" +
                                    "\"交易流水号\":\"" + paymentNo + "\",\n" +
                                    "\"问题说明\":\"" + resultString + "\"\n" +
                                    "}");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    log.error("GJ original paymentNo:" + paymentNo + ",refundId:" + etFltRefund.getId() + ",支付宝退款异常" + e.getMessage(), e);
                    log.info("GJ 支付宝退款异常" + e.getMessage());
                    e.printStackTrace();
                    /*
                     * 退款请求异常
                     */
                    this.updateRefundStatus(etFltRefund, "PENDICING");
                }
            }
        }
        // 判断是否还有下个责任链节点，没有的话，说明已经是最后一个节点
        if (next != null){
            next.doHandler(map);
        }
    }

    private boolean isPayType(PayType payType) {
        if (PayType.ALIPAY == payType) {
            return true;
        }
        return false;
    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }

    public static String CreateUrl(String paygateway, String input_charset, String service, String partner,
                                   String sign_type, String batch_no, String refund_date, String batch_num, String detail_data, String notify_url,
                                   String key) {
        Map params = new HashMap();
        params.put("_input_charset", input_charset);
        params.put("service", service);
        params.put("partner", partner);
        params.put("batch_no", batch_no);
        params.put("refund_date", refund_date);
        params.put("batch_num", batch_num);
        params.put("detail_data", detail_data);
        params.put("notify_url", notify_url);

        String prestr = "";
        prestr = prestr + key;

        String sign = com.alipay.util.Md5Encrypt.md5(getContent(params, key));
        String parameter = "";
        parameter = parameter + paygateway;

        List keys = new ArrayList(params.keySet());
        for (int i = 0; i < keys.size(); i++) {
            String value = (String) params.get(keys.get(i));
            if (value == null || value.trim().length() == 0) {
                continue;
            }
            try {
                parameter = parameter + keys.get(i) + "="
                        + URLEncoder.encode(value, input_charset) + "&";
            } catch (UnsupportedEncodingException e) {

                e.printStackTrace();
            }
        }
        parameter = parameter + "sign=" + sign + "&sign_type=" + sign_type;
        return parameter.trim();
    }

    private static String getContent(Map params, String privateKey) {
        List keys = new ArrayList(params.keySet());
        Collections.sort(keys);

        String prestr = "";

        boolean first = true;
        for (int i = 0; i < keys.size(); i++) {
            String key = (String) keys.get(i);
            String value = (String) params.get(key);
            if (value == null || value.trim().length() == 0) {
                continue;
            }
            if (first) {
                prestr = prestr + key + "=" + value;
                first = false;
            } else {
                prestr = prestr + "&" + key + "=" + value;
            }
        }
        return prestr + privateKey;
    }


    public Map handleRefundResponse(EtPayment payment, String returnXmlStr, Map map) throws Exception {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://javax.xml.xmlconstants/feature/secure-processing" , true);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl" , true);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities" , false);
            factory.setFeature("http://xml.org/sax/features/external-general-entities" , false);
            factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd" , false);
            factory.setXIncludeAware(false);
            factory.setExpandEntityReferences(false);
            DocumentBuilder db = factory.newDocumentBuilder();
            InputSource inStream = new InputSource();
            inStream.setCharacterStream(new StringReader(returnXmlStr));
            Document doc = db.parse(inStream);
            NodeList nodeList = doc.getElementsByTagName("alipay");

            for (int s = 0; s < nodeList.getLength(); s++) {
                Node firstNode = nodeList.item(s);
                if (firstNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element fstElmnt = (Element) firstNode;
                    NodeList fstNmElmntLst = fstElmnt.getElementsByTagName("is_success");
                    Element fstNmElmnt = (Element) fstNmElmntLst.item(0);
                    NodeList fstNm = fstNmElmnt.getChildNodes();
                    String result = ((Node) fstNm.item(0)).getNodeValue();

                    if (nodeList == null || nodeList.getLength() == 0) {
                        if (!payment.getPayStatus().equals(PayStatus.PENDICING)) {
                            map.put("returnResult", PaymentLogStatus.FAILED);
                        }
                        payment.setPayStatus(PayStatus.FAIL.toString());
                        return map;
                    }

                    // If Refund Failed
                    if ("F".equalsIgnoreCase(result)) {
                        if (!payment.getPayStatus().equals(PayStatus.PENDICING)) {
                            map.put("returnResult", PaymentLogStatus.FAILED);
                        }
                        payment.setPayStatus(PayStatus.FAIL.toString());
                    }
                    // If Refund Succeeded
                    if ("T".equalsIgnoreCase(result) || "P".equalsIgnoreCase(result)) {
                        map.put("returnResult", PaymentLogStatus.SUCCESS);
                        payment.setPayStatus(PayStatus.PENDICING.toString());
                    }
                }
            }
        } catch (Exception ex) {
            throw ex;
        }

        return map;
    }
}
