package net.jdair.specialrefund.bizz.utils;

import net.jdair.specialrefund.bizz.domain.EtFltRefund;
import net.jdair.specialrefund.bizz.vo.DTOFltRefund;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class FltRefundMapper extends Mapper<DTOFltRefund, EtFltRefund> {
	private static FltRefundMapper mapper;

	private FltRefundMapper() {
		super(DTOFltRefund.class, EtFltRefund.class);
	}

	public static FltRefundMapper getInstance() {
		if (mapper == null) {
			mapper = new FltRefundMapper();
		}
		return mapper;
	}

	@Override
	public EtFltRefund mapToEntity(DTOFltRefund value) {
		EtFltRefund fr = super.mapToEntity(value);

		if (value.getId() == null) {
			fr.setId(null);
		}
		
		return fr;
	}

	@Override
	public DTOFltRefund mapToValue(EtFltRefund entity) {
		DTOFltRefund value = super.mapToValue(entity);
		return value;
	}

	/**
	 * Map property values from the set of entities to the list of value beans
	 * for all cases where the property names are the same.
	 *
	 * @param entitySet
	 * @return
	 */
	@Override
	public List<DTOFltRefund> mapToValues(Set<EtFltRefund> entitySet) {
		if (entitySet != null) {
			List<DTOFltRefund> valueList = new ArrayList<DTOFltRefund>();
			for (EtFltRefund entity : entitySet) {
				valueList.add(mapToValue(entity));
			}
			return valueList;
		} else {
			return null;
		}
	}
}