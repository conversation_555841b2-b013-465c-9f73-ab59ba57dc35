package net.jdair.specialrefund.bizz.utils.file.service;

import net.jdair.specialrefund.bizz.utils.file.vo.OssFileInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件服务接口类
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 文件上传
     *
     * @param multipartFile 文件对象
     * @return OssFileInfo
     */
    OssFileInfo ossUpload(MultipartFile multipartFile, String bucketName);

    /**
     * 文件下载
     *
     * @param filePath filePath
     * @param response response
     */
    void ossDownload(String filePath, HttpServletResponse response, String bucketName);

    /**
     * 获取文件预览地址
     *
     * @param filePath filePath
     * @return String
     */
    String getPreviewUrl(String filePath, String bucketName);

}
