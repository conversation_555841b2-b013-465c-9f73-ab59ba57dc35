
package net.jdair.specialrefund.bizz.utils.email;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Configuration
public class TsEmailConfig {
	
	/**
	 * 发件人
	 */
	@Value("${email.TsSendEmail.from}")
	private String from;

	/**
	 * 密
	 */
	@Value("${email.TsSendEmail.pwd}")
	private String pwd;

	/**
	 * 收件人
	 */
	@Value("${email.TsSendEmail.receiver}")
	private String receiver;

}
