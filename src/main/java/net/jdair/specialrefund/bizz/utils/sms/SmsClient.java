package net.jdair.specialrefund.bizz.utils.sms;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class SmsClient {

    @Autowired
    private SmsConfig smsConfig;

    static CloseableHttpClient httpclient;

    public static CloseableHttpClient getHttClient() {
        if (httpclient == null) {
            httpclient = HttpClients.createDefault();
        }
        log.info("httpclient:" + httpclient);
        return httpclient;
    }

    /**
     * 异步发送短信
     * @param phoneNos 目的手机号
     * @param isAdvertSms 是否营销短信
     * @param smsContent 短信内容
     */
    @Async
    public void sendSms(String[] phoneNos, boolean isAdvertSms, String smsContent) {
        CloseableHttpClient httpClient = getHttClient();
        HttpPost post = null;
        if (isAdvertSms) {
            //营销短信接口
            post = new HttpPost(smsConfig.getUrl() + smsConfig.getSendTD());
        } else {
            //非营销短信
            post = new HttpPost(smsConfig.getUrl() + smsConfig.getSend());
        }
        List<String> phoneList = new ArrayList<String>();
        for (int i = 0; i < phoneNos.length; i++) {
            //批量发
            String phone = phoneNos[i];
            if (phone != null && phone.length() > 10) {
                phoneList.add(phone);
            }
        }
        String phonesStr = "";
        try {
            //放置请求参数
            phonesStr = phoneList.toString().replace("[", "").replace("]", "").replaceAll(" ", "").trim();
            if (StringUtils.isEmpty(phonesStr)) {
                log.error("SmsClient send phoneList: " + phoneNos + ", phones: " + phonesStr);
                // TODO 短信发送记录
                // smsSendService.update(ja.getTimestamp(), SMSSendStatus.FAIL.getAlias(), "phone error:"+phonesStr);
                return;
            }
            List<BasicNameValuePair> formparams = new ArrayList<BasicNameValuePair>();
            formparams.add(new BasicNameValuePair("mobile", phonesStr));
            formparams.add(new BasicNameValuePair("ai.cc", smsConfig.getCc()));
            formparams.add(new BasicNameValuePair("ai.cp", smsConfig.getCp()));
            String msgSend = smsContent.trim();
            if (msgSend.contains(";")) {
                msgSend = msgSend.replace(";", ".");
            }
            formparams.add(new BasicNameValuePair("msg", msgSend));
            //设置编码
            UrlEncodedFormEntity uefEntity = new UrlEncodedFormEntity(formparams, "UTF-8");
            post.setEntity(uefEntity);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(2000).setConnectTimeout(2000).build();//设置请求和传输超时时间
            post.setConfig(requestConfig);
            //发送请求
            log.info("SmsClient result = " + phonesStr);
            CloseableHttpResponse response = httpClient.execute(post);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            boolean isSucc = false;
            if (!StringUtils.isEmpty(result)) {
                log.info("SmsClient result = " + phonesStr + ":" + result);

                if (result.contains("502 Bad Gateway")) {
                    response = httpClient.execute(post);
                    result = EntityUtils.toString(response.getEntity(), "UTF-8");
                    log.info("SmsClient result = " + phonesStr + "retry:" + result);
                }

                //转成json数据
                JSONObject json = JSONObject.parseObject(result);
                //{"result":{"message":"succ","viewMessage":"成功","resultCode":"1000"},"data":{"messageCount":0,"mess":"调用成功"}}
                if (json.getJSONObject("result") != null) {
                    JSONObject json2 = json.getJSONObject("result");
                    if (json2.getString("message") != null) {
                        String msg = json2.getString("message");
                        if (msg.equals("succ")) {
                            isSucc = true;
                        }
                    }
                }
            }
            if (isSucc) {
                log.info("SmsClient send success:" + phonesStr);
                // smsSendService.update(ja.getTimestamp(), SMSSendStatus.SUCCESS.getAlias(), result);
            } else {
                log.error("SmsClient send fail:" + phonesStr + ",msg:" + result);
                // smsSendService.update(ja.getTimestamp(), SMSSendStatus.FAIL.getAlias(), result);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("SmsClient send fail:" + phonesStr + ",msg:" + e.getMessage());
            try {
                // smsSendService.update(ja.getTimestamp(), SMSSendStatus.FAIL.getAlias(), e.getMessage());
            } catch (Exception e1) {
                //server.log.2019-08-31.gz.3801:ERROR-Aug 31 08:40:18 19:ORA-00001: unique constraint (ET.ET_SMS_CREATE_TIME) violated
                log.error("sms update error:" + e1.getMessage(), e);
            }
        }
    }


}
