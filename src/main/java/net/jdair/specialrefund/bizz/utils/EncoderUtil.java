package net.jdair.specialrefund.bizz.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;


public class EncoderUtil {
	private final static Log log = LogFactory.getLog(EncoderUtil.class);

	public static final String DEFAULT_KEY = "$com.8jdair@";
	public static final String DEFAULT_METHOD = "DESede";

	public EncoderUtil() {
	}


	public static final String MD5(String src) {
		char[] hexDigits = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E',
				'F' };

		try {
			byte[] e = src.getBytes();
			MessageDigest mdInst = MessageDigest.getInstance("MD5");
			mdInst.update(e);
			byte[] md = mdInst.digest();
			int j = md.length;
			char[] str = new char[j * 2];
			int k = 0;

			for (int i = 0; i < j; ++i) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 >>> 4 & 15];
				str[k++] = hexDigits[byte0 & 15];
			}

			return new String(str);
		} catch (Exception var10) {
			log.error(var10.getMessage(), var10);
			return null;
		}
	}

	public static final String MD5WithSalt(String src, String saltKey) {
		StringBuffer sb = new StringBuffer(src);
		sb.append("{").append(saltKey).append("}");
		return MD5(sb.toString());
	}

	private static SecretKey generateKey(String secretKey, String method) throws NoSuchAlgorithmException {
		SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
		secureRandom.setSeed(secretKey.getBytes());
		KeyGenerator kg = null;

		try {
			kg = KeyGenerator.getInstance(method);
		} catch (NoSuchAlgorithmException var5) {
			;
		}

		kg.init(secureRandom);
		return kg.generateKey();
	}



	public static String decryptDefault(String str) throws Exception {
		SecretKey desKey = new SecretKeySpec((DEFAULT_KEY+DEFAULT_KEY).getBytes(), DEFAULT_METHOD);
		Cipher c1 = Cipher.getInstance("DESede/ECB/PKCS5Padding");
		c1.init(Cipher.DECRYPT_MODE, desKey);
		byte[] srcBytes = c1.doFinal(new sun.misc.BASE64Decoder().decodeBuffer(str));
		return new String(srcBytes, "UTF-8");
	}

	public static String encryptDefault(String str) throws Exception {
		SecretKey desKey = new SecretKeySpec((DEFAULT_KEY+DEFAULT_KEY).getBytes(), DEFAULT_METHOD);
		Cipher c1 = Cipher.getInstance("DESede/ECB/PKCS5Padding");
		c1.init(Cipher.ENCRYPT_MODE, desKey);
		byte[] srcBytes = c1.doFinal(str.getBytes());
		return new sun.misc.BASE64Encoder().encode(srcBytes);
	}

	/*public static void main(String[] args){
    	String s = "eking@2017";
    	String b = "QduTe7unBKmYSXTCAdK7Bw==";
    	String ss=encryptDefaultVal(s);
    	String bb=decryptDefaultVal(b);
    	System.out.println(ss);
    	System.out.println("bb:"+bb);
		
		
		
	}*/
	
	public static void main(String[] args){
		
    
		/*  String phone = "13123456789";
	        String phoneNumber = phone.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
	        System.out.println("正则phone中4*：" + phoneNumber);*/
		
//    	String s = "13488768162";
//    	String b = "QhnTsaelPqohqc6LvJe2/Q==";
//    	String ss=encryptDefaultVal(s);
//    	String bb=decryptDefaultVal(b);
//    	System.out.println("---"+ss);
//    	System.out.println("bb:"+bb);

    	//
		//XJKRxD6nD5eCmo+VVXvS317uUYpNUnIY+z2di7WsvA4=
		//RCW5muOEOft9cAcsEycVhDTQIi5+zsdBTgdLAiulITE=

		try {
			System.out.println(encryptDefault("15808936634"));
			System.out.println(encryptDefault("410502198304132516"));
			System.out.println(encryptDefault("********警"));
			System.out.println(decryptDefault(encryptDefault("15808936634")));
			System.out.println(decryptDefault(encryptDefault("410502198304132516")));
			System.out.println(decryptDefault(encryptDefault("********警")));
		} catch (Exception e) {
			e.printStackTrace();
		}

		//System.out.println("uuid"+UUID.generate());
    	// f=String.valueOf(Math.random());
//		//BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
		//System.out.println(bCryptPasswordEncoder.encode("wESD123UJ1800"));
		/*String echoToken = "ssssssssssssssssssss2111";
		String regex = "^[a-zA-Z0-9]{0,128}$";
		if(echoToken.matches(regex)){
			System.out.print("aa");
		}
		String weizhi="09D103:发票已用完";
		System.out.println(weizhi.indexOf("09D103"));*/
		
		/*GregorianCalendar cal = new GregorianCalendar(); 
		Date now = new Date();
		cal.setTime(now); 
		DatatypeFactory dtf = null; 
    	XMLGregorianCalendar gc = null; 
    	try {
    		dtf = DatatypeFactory.newInstance();
    		} catch (DatatypeConfigurationException e) {
    		}
    	
    	XMLGregorianCalendar dateType = dtf.newXMLGregorianCalendar(); 
    	dateType.setYear(cal.get(Calendar.YEAR));
    	//由于Calendar.MONTH取值范围为0~11,需要加1
    	dateType.setMonth(cal.get(Calendar.MONTH)+1);
    	dateType.setDay(cal.get(Calendar.DAY_OF_MONTH));
    	dateType.setHour(cal.get(Calendar.HOUR_OF_DAY));
    	dateType.setMinute(cal.get(Calendar.MINUTE));
    	dateType.setSecond(cal.get(Calendar.SECOND));
    	try {  
            gc = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);  
            System.out.println(dateType);
        } catch (Exception e) {  
             e.printStackTrace();  
        }  
		
		String s="aaaa-bbbb";
		String b=s.substring(0,s.indexOf("-"));
		String c=s.substring(s.indexOf("-")+1,s.length());
		System.out.print(b+"-"+c);*/
		
	}
	 
	 

}