package net.jdair.specialrefund.bizz.utils;


import net.jdair.specialrefund.bizz.domain.EtTsTicketnoBlacklist;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundTicketNoBlackList;

public class SpecialRefundTicketNoBlackListMapper extends Mapper<DTOSpecialRefundTicketNoBlackList, EtTsTicketnoBlacklist> {
    private static SpecialRefundTicketNoBlackListMapper mapper;

    private SpecialRefundTicketNoBlackListMapper() {
        super(DTOSpecialRefundTicketNoBlackList.class, EtTsTicketnoBlacklist.class);
    }

    public static SpecialRefundTicketNoBlackListMapper getInstance() {
        if (mapper == null) {
            mapper = new SpecialRefundTicketNoBlackListMapper();
        }
        return mapper;
    }
}
