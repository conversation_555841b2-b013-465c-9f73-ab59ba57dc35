package net.jdair.specialrefund.bizz.utils.pay.sdk;

import org.apache.log4j.Logger;
import org.springframework.boot.system.ApplicationHome;

import java.io.*;
import java.net.URL;


public class WXPayConfig {
	private static Logger log = Logger.getLogger(WXPayConfig.class);
	private String appID;
	private String mchID;
	private String key;
	private String certPath;
	private byte[] certData;  
	private String payCallBackUrl;
	private String backendPayCallBackUrl;
	private String refundUrl;
    /**
     * 获取商户证书内容
     *
     * @return 商户证书内容
     */
    private InputStream certStream;
    
    /**
     * 获取WXPayDomain, 用于多域名容灾自动切换
     * @return
     */
    private IWXPayDomain WXPayDomain;
    
    private WXPayConfig(){
    } 
    /**
     * 得到真实的路径
     * @param classpath
     * @return
     */
    public static String getRealPath(String classpath){
		ApplicationHome ah = new ApplicationHome(WXPayConfig.class);
		// 获取jar包所在目录
		String jarPath = ah.getSource().getParentFile().getAbsolutePath();
		StringBuffer s =new StringBuffer(jarPath + File.separator);
		String path=s.append(classpath).toString();
		if(path.indexOf("%20")>=0)
			path = path.replaceAll("%20", " ");
		return path;

	}
	public String getAppID() {
		return appID;
	}
	public void setAppID(String appID) {
		this.appID = appID;
	}
	public String getMchID() {
		return mchID;
	}
	public void setMchID(String mchID) {
		this.mchID = mchID;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	} 
    /**
     * HTTP(S) 连接超时时间，单位毫秒
     *
     * @return
     */
    public int getHttpConnectTimeoutMs() {
        return 6*1000;
    }

    /**
     * HTTP(S) 读数据超时时间，单位毫秒
     *
     * @return
     */
    public int getHttpReadTimeoutMs() {
        return 8*1000;
    }

    /**
     * 是否自动上报。
     * 若要关闭自动上报，子类中实现该函数返回 false 即可。
     *
     * @return
     */
    public boolean shouldAutoReport() {
        return true;
    }

    /**
     * 进行健康上报的线程的数量
     *
     * @return
     */
    public int getReportWorkerNum() {
        return 6;
    }


    /**
     * 健康上报缓存消息的最大数量。会有线程去独立上报
     * 粗略计算：加入一条消息200B，10000消息占用空间 2000 KB，约为2MB，可以接受
     *
     * @return
     */
    public int getReportQueueMaxSize() {
        return 10000;
    }

    /**
     * 批量上报，一次最多上报多个数据
     *
     * @return
     */
    public int getReportBatchSize() {
        return 10;
    }

    public InputStream getCertStream() {
        ByteArrayInputStream certBis;
        certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }
    
    IWXPayDomain getWXPayDomain() {
        return WXPayDomainSimpleImpl.instance();
    }

    public String getPrimaryDomain() {
        return "api.mch.weixin.qq.com";
    }

    public String getAlternateDomain() {
        return "api2.mch.weixin.qq.com";
    }
	public String getCertPath() {
		return certPath;
	}
	public void setCertPath(String certPath) {
		this.certPath = certPath;
		String certRealPath=getRealPath(certPath);
		log.info("cert path:"+certRealPath);
		File file = new File(certRealPath);
		try {
			InputStream certStream = new FileInputStream(file);
			this.certData = new byte[(int) file.length()];
			certStream.read(this.certData);
			certStream.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	public String getPayCallBackUrl() {
		return payCallBackUrl;
	}
	public void setPayCallBackUrl(String payCallBackUrl) {
		this.payCallBackUrl = payCallBackUrl;
	}
	public String getBackendPayCallBackUrl() {
		return backendPayCallBackUrl;
	}
	public void setBackendPayCallBackUrl(String backendPayCallBackUrl) {
		this.backendPayCallBackUrl = backendPayCallBackUrl;
	}
	public String getRefundUrl() {
		return refundUrl;
	}
	public void setRefundUrl(String refundUrl) {
		this.refundUrl = refundUrl;
	}
	public void setCertStream(InputStream certStream) {
		this.certStream = certStream;
	}
    
}
