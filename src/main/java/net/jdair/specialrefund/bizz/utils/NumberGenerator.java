package net.jdair.specialrefund.bizz.utils;

public class NumberGenerator {
	/**
	 * Generate random number of given length
	 * @param length
	 * @return
	 */
	public static String generateRandomNumber(int length) {
		int max = (int) Math.pow(10, length) - 1;
		String no = String.valueOf((int) (Math.random() * max));
		return pad(no, length);
	}

	/**
	 * Returns string with given length by padding zero in front of the string.
	 * 
	 * @param s
	 *            String to be padded with zero
	 * @param length
	 *            Target length
	 * @return String with given length by padding zero in front of the string.
	 */
	protected static String pad(String s, int length) {
		StringBuffer sb = new StringBuffer();

		if (s.length() < length) {
			int n = length - s.length();
			for (int i = 0; i < n; i++) {
				sb.append("0");
			}
		}
		sb.append(s);

		return sb.toString();
	}
}
