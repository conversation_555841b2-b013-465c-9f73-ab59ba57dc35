

package net.jdair.specialrefund.bizz.utils;

import org.xml.sax.SAXException;
import sun.misc.BASE64Decoder;

import java.beans.IntrospectionException;
import java.io.IOException;
import java.io.StringWriter;
import java.security.MessageDigest;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 *<AUTHOR>
 *@description
 *
 * TODO your comment for class
 *
 *@date 2009-4-14
 */
public class TextUtil
{

	/**
	 * 
	 *@param text
	 *@return
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * 判断字符串是否为空

	 *
	 *@date 2009-4-14
	 */
	public static boolean isEmpty(String text) {
		return (text == null) || (text.trim().equals(""));
	}
	
	/**
	 * 判断文本是否全是英文字母
	 * @param text
	 * @return
     */
	public static boolean isTextEnglish(String text){
		Pattern p = Pattern.compile("^[a-zA-Z]+$");
		Matcher m = p.matcher(text);
		return m.matches();
	}

	/**
	 * 
	 *@param text
	 *@return
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * 判断对象是否为空
	 *
	 *@date 2009-4-14
	 */
	public static boolean isEmpty(Object text) {
		return (text == null) || (text.toString().trim().equals(""));
	}

	/**
	 * 
	 *@param s
	 *@return
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * 判断char 是否为数字

	 *
	 *@date 2009-4-14
	 */
	public static boolean isNumber(char s) {
		return (s == '1' || s == '2' || s == '3' || s == '4' || s == '5'
				|| s == '6' || s == '7' || s == '8' || s == '9' || s == '0');
	}

	
	
	public static boolean isNumber(char[] nums) {
		if(nums==null)return false;
		for(int i=0;i<nums.length;i++)
		{
			if(!TextUtil.isNumber(nums[i]))
			{
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 

	 * 验证是否为数字（true-数字）
	 * 
	 * <AUTHOR>
	 * @2010-11-26
	 */
	public static boolean isNumber(String str) {
		Pattern pattern = Pattern.compile("[1-9][0-9]*");
		Matcher match = pattern.matcher(str);
		if (match.matches()) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 
	 *@param myInt
	 *@return
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * 整型数据转换为以逗号分开的字符串
	 *
	 *@date 2009-4-14
	 */
	public static String intArrayToString(int[] myInt) {

		String myString = "";

		for (int i = 0, j = 0; i < myInt.length; i++, j++) {
			myString += String.valueOf(myInt[i]);
			if (j < myInt.length - 1) {
				myString += ",";
			}// if
		}// for

		return myString;
	}

	/**
	 * 
	 *@param original
	 *@param patternString
	 *@return
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * List contain all the elements split by the pattern
	 *
	 *@date 2009-4-14
	 */
	public static List splitByPattern(String original, String patternString) {
		Pattern pattern = Pattern.compile(patternString);
		Matcher matcher = pattern.matcher(original);

		Set set = new TreeSet();
		while (matcher.find()) {
			set.add(new Integer(matcher.start()));
			set.add(new Integer(matcher.end()));
		}

		List result = new ArrayList();
		if (set.size() == 0) {
			result.add(original);
			return result;
		}

		Object[] oArray = set.toArray();
		Integer first = (Integer) oArray[0];

		if (first.intValue() != 0) {
			String firstElement = original.substring(0, first.intValue());
			appendStringToList(firstElement, result);
		}

		for (int i = 0; i < oArray.length - 1; i++) {
			Integer current = (Integer) oArray[i];
			Integer next = (Integer) oArray[i + 1];
			String element = original.substring(current.intValue(), next
					.intValue());
			appendStringToList(element, result);
		}

		Integer last = (Integer) oArray[oArray.length - 1];
		if (last.intValue() != original.length()) {
			String lastElement = original.substring(last.intValue(), original
					.length());
			appendStringToList(lastElement, result);
		}
		return result;
	}

	/**
	 * 
	 *@param string
	 *@param list
	 *
	 *<AUTHOR>
	 *@description
	 *
	 * append a string to the specified List
	 *
	 *@date 2009-4-14
	 */
	private static void appendStringToList(String string, List list) {
		if (!isEmpty(string))
			list.add(string.trim());
	}

	private static NumberFormat priceFormatter = new DecimalFormat("########");

	private static NumberFormat discountFormatter = new DecimalFormat("#.##");

	public static String formatPrice(double price) {
		return priceFormatter.format(price);
	}

	public static double formatDiscount(double discount) {
		return Double.parseDouble(discountFormatter.format(discount));
	}

	public static List splitByString(String original, String delim) {
		List result = new ArrayList();
		if (isEmpty(original))
			return result;
		StringTokenizer st = null;
		if (delim == null) {
			st = new StringTokenizer(original);
		} else {
			st = new StringTokenizer(original, delim);
		}
		while (st.hasMoreElements()) {
			result.add(((String) st.nextElement()).trim());
		}
		return result;
	}

	public static String getBASE64(String s) {
		if (s == null)
			return null;
		return (new sun.misc.BASE64Encoder()).encode(s.getBytes());
	}

	public static String getFromBASE64(String s) {
		if (s == null)
			return null;
		BASE64Decoder decoder = new BASE64Decoder();
		try {
			byte[] b = decoder.decodeBuffer(s);
			return new String(b);
		} catch (Exception e) {
			return null;
		}
	}

	public static String showBytesHex(String title, byte[] bytes) {
		StringBuffer buff = new StringBuffer(title + " sizie=" + bytes.length
				+ "\n---\n");
		int groupLength = 16;
		for (int i = 0; i < bytes.length; i++) {
			String b = Integer.toHexString(bytes[i]);
			//前补0
			b = b.length() == 1 ? "0" + b : b;
			//byte是两个字节的，而上面的Integer.toHexString会把字节扩展为4个字节

			b = b.length() > 2 ? b.substring(6, 8) : b;
			buff.append(b);
			//如果满groupLength,则换行

			if ((i + 1) % groupLength == 0) {
				buff.append("\n");
			} else if ((i + 1) % (groupLength / 2) == 0) {
				buff.append("  ");
			} else {
				buff.append(" ");
			}
		}
		//最后补上换行


		buff.replace(buff.length() - 1, buff.length(), "\n---\n");

		return buff.toString();
	}

	public static String showBytesDecimal(String title, byte[] bytes) {
		StringBuffer buff = new StringBuffer(title + " sizie=" + bytes.length
				+ "\n---\n");
		int groupLength = 16;
		for (int i = 0; i < bytes.length; i++) {
			String b = Integer.toString(bytes[i] < 0 ? 256 + bytes[i]
					: bytes[i]);
			// 前补0
			b = b.length() == 1 ? "00" + b : b;
			b = b.length() == 2 ? "0" + b : b;

			buff.append(b);
			//如果满groupLength,则换行

			if ((i + 1) % groupLength == 0) {
				buff.append("\n");
			} else if ((i + 1) % (groupLength / 2) == 0) {
				buff.append("   ");
			} else {
				buff.append(" ");
			}

		}
		//最后补上换行


		buff.replace(buff.length() - 1, buff.length(), "\n---\n");

		return buff.toString();
	}

	public static String showBytesBinary(String title, byte[] bytes) {
		StringBuffer buff = new StringBuffer(title + " sizie=" + bytes.length
				+ "\n---\n");
		int groupLength = 8;
		for (int i = 0; i < bytes.length; i++) {
			String b = Integer.toBinaryString(bytes[i]);
			//前补0
			b = ("00000000" + b).substring(b.length());

			buff.append(b);
			// 如果满groupLength，则换行
			if ((i + 1) % groupLength == 0) {
				buff.append("\n");
			} else if ((i + 1) % (groupLength / 2) == 0) {
				buff.append("   ");
			} else {
				buff.append(" ");
			}
		}
		// 最后补上换行


		buff.replace(buff.length() - 1, buff.length(), "\n---\n");

		return buff.toString();
	}


	public static String encryptPassword(String unencodeString,
			String encryptAlgorithm) {

		byte[] unencodedPassword = unencodeString.trim().getBytes();
		String encryptedPassword = "";
		MessageDigest md = null;
		try {
			// first create an instance, given the provider
			md = MessageDigest.getInstance(encryptAlgorithm);
		} catch (Exception e) {
			// log.error("Exception: " + e);

			return unencodeString;
		}

		md.reset();

		// call the update method one or more times
		// (useful when you don't know the size of your data, eg. stream)
		md.update(unencodedPassword);

		// now calculate the hash
		byte[] encodedPassword = md.digest();

		StringBuffer buf = new StringBuffer();

		for (int i = 0; i < encodedPassword.length; i++) {
			if ((encodedPassword[i] & 0xff) < 0x10) {
				buf.append("0");
			}

			buf.append(Long.toString(encodedPassword[i] & 0xff, 16));
		}

		return buf.toString();
	}

	public static boolean isClerkPatten(String words) {
		boolean isMatched = false;
		Pattern pattern = Pattern.compile("^[0-9]{4,5}[A-Za-z]{3}$");

		Matcher matcher = pattern.matcher(words);

		if (matcher.matches())
			isMatched = true;

		return isMatched;
	}

	public static String hz2Py(String hz) {
		StringBuffer buf = new StringBuffer();

		char[] hzArray = hz.toCharArray();

		for (int i = 0; i < hzArray.length; i++) {
			if (hzArray[i] > 32 && hzArray[i] < 127) {
				//字母和符号原样保留


				buf.append(hzArray[i]);
			} else {
				//累加拼音声母
				buf.append(getPyChar(String.valueOf(hzArray[i])));
			}
		}

		return buf.toString();

	}

	private static String getPyChar(String c) {
		byte[] array = c.getBytes();
		int i = (short) (array[0] - 48) * 256 + ((short) (array[1] - 48));

		if (i < 0xb0a1)
			return "*";
		if (i < 0xb0c5)
			return "a";
		if (i < 0xb2c1)
			return "b";
		if (i < 0xb4ee)
			return "c";
		if (i < 0xb6ea)
			return "d";
		if (i < 0xb7a2)
			return "e";
		if (i < 0xb8c1)
			return "f";
		if (i < 0xb9fe)
			return "g";
		if (i < 0xbbf7)
			return "h";
		if (i < 0xbfa6)
			return "g";
		if (i < 0xc0ac)
			return "k";
		if (i < 0xc2e8)
			return "l";
		if (i < 0xc4c3)
			return "m";
		if (i < 0xc5b6)
			return "n";
		if (i < 0xc5be)
			return "o";
		if (i < 0xc6da)
			return "p";
		if (i < 0xc8bb)
			return "q";
		if (i < 0xc8f6)
			return "r";
		if (i < 0xcbfa)
			return "s";
		if (i < 0xcdda)
			return "t";
		if (i < 0xcef4)
			return "w";
		if (i < 0xd1b9)
			return "x";
		if (i < 0xd4d1)
			return "y";
		if (i < 0xd7fa)
			return "z";

		return "*";
	}

	public static String replaceString(String orginalString,
			String stringToReplace, String replaceString) {
		// TODO Auto-generated method stub
		StringBuffer buf = new StringBuffer(orginalString);
		int index = 0;
		while (index != -1) {
			index = buf.indexOf(stringToReplace);
			if (index == -1) {
				break;
			} else {
				buf.replace(index, index + 3, replaceString);
			}
		}

		return buf.toString();
	}

	/**
	 * 判断字符串是否可识别
	 * 航信的字库用GB2312 16区以上的字符
	 * 也就是第一个字节>=0XB0(十进制为176)，第二个字节>=0XA0(十进制为160)
	 * @param src 字符串

	 * @param charSet 字符集 GB2312 或 GB2312-ICS
	 * @return
	 */
	public static boolean canBeChangeInto(String src, String charSet) {

		try {
			if (charSet.trim().equals("GB2312-ICS")) {
				byte[] srcByte = src.getBytes("GB2312");
				for (int i = 0; i < srcByte.length; i++) {
					if (srcByte[i] < 0) {
						if (srcByte[i] < -80) // 256-176=80
						{
							return false;
						} else if (srcByte[i + 1] < -96) // 256-160=96
						{
							return false;
						} else {
							i++;
						}
					} else if (srcByte[i] == 63) {
						return false;
					}
				}
				return true;
			} else {
				byte[] srcByte = src.getBytes(charSet);
				//System.out.println(srcByte);
				for (int j = 0; j < srcByte.length; j++) {
					/* '63'为未能识别字符的byte代号*/
					if (srcByte[j] == 63) {
						return false;
					}
				}
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	

    /**
     * @param line
     * @param oldString
     * @param newString
     * @return
     */
    public static String replace( String line, String oldString, String newString ) 

    { 

        if (line == null) { 

            return null; 

        } 

        int i=0; 

        if ( ( i=line.indexOf( oldString, i ) ) >= 0 ) { 

            char [] line2 = line.toCharArray(); 

            char [] newString2 = newString.toCharArray(); 

            int oLength = oldString.length(); 

            StringBuffer buf = new StringBuffer(line2.length); 

            buf.append(line2, 0, i).append(newString2); 

            i += oLength; 

            int j = i; 

            while( ( i=line.indexOf( oldString, i ) ) > 0 ) { 

                buf.append(line2, j, i-j).append(newString2); 

                i += oLength; 

                j = i; 

            } 

            buf.append(line2, j, line2.length - j); 

            return buf.toString(); 

        } 

        return line; 

    }
}
