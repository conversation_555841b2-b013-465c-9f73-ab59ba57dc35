package net.jdair.specialrefund.bizz.utils;

import net.jdair.specialrefund.bizz.domain.EtSegment;
import net.jdair.specialrefund.bizz.vo.DTOSegment;

import java.util.List;

public class SegmentMapper extends Mapper<DTOSegment, EtSegment>{
	
	private static SegmentMapper segmentMapper;
	
	private SegmentMapper() {}
	
	public static SegmentMapper getInstance() {
		if (segmentMapper == null) {
			segmentMapper = new SegmentMapper();
		}
		return segmentMapper;
	}

	@Override
	public EtSegment mapToEntity(DTOSegment value) {
		EtSegment p = super.mapToEntity(value);
		
		return p;
	}

	@Override
	public DTOSegment mapToValue(EtSegment entity) {
		DTOSegment dto = super.mapToValue(entity);		
		//dto.setSegments(entity.getSegments()); 
		
		dto.setDepCode(entity.getDepCode());
		dto.setArrCode(entity.getArrCode());
		
		return dto;
	}
	
	@Override
	public List<DTOSegment> mapToValues(List<EtSegment> entity) {
		List<DTOSegment> list = super.mapToValues(entity);
				
		return list;
	}

	@Override
	protected Class<EtSegment> getEntityClass() {
		return EtSegment.class;
	}

	@Override
	protected Class<DTOSegment> getValueClass() {
		return DTOSegment.class;
	}
}
