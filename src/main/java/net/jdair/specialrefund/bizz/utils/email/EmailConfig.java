
package net.jdair.specialrefund.bizz.utils.email;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Configuration
public class EmailConfig {
	
	/**
	 * IBE 服务器 IP地址
	 */
	@Value("${email.HWZSendEmail.from}")
	private String from;

	/**
	 * IBE 备份IP地址
	 */
	@Value("${email.HWZSendEmail.pwd}")
	private String pwd;

}
