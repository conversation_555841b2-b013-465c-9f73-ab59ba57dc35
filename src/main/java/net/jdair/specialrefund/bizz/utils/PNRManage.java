package net.jdair.specialrefund.bizz.utils;

import com.travelsky.ibe.client.IBEClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class PNRManage {

	private static IBEClientConfig config;

	@Autowired
	public void setConfig(IBEClientConfig config) {
		PNRManage.config = config;
	}

	private static Map<String,PNRManage> maps=new HashMap<String,PNRManage>();

	/**
	 * 可静态调用 的方法，设置 client 参数。。。
	 * @param client
	 * @param airline
	 */
	public static void configIBEClient(IBEClient client,String airline){
		//not set airline ,use default.
		if(!StringUtils.hasText(airline)){
			return;
		}
		Assert.notNull(client, "No ibe instance to set params");
		client.setAgentInfo(config.getOffice(), config.getCustomno(), config.getValidationno());
		client.setAppName(config.getApp());
		
		String ipAddress=config.getIp();

		log.info("use ip "+ipAddress+" to access travelsky ibe.");
		
		client.setConnectionInfo(ipAddress,Integer.parseInt(config.getPort()));
	}
}
