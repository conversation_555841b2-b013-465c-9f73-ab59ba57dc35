
package net.jdair.specialrefund.bizz.utils;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *
 */
@Data
@Configuration
public class IBEClientConfig {
	
	/**
	 * IBE 服务器 IP地址
	 */
	@Value("${ibe.config.ip}")
	private String ip;

	/**
	 * IBE 备份IP地址
	 */
	@Value("${ibe.config.backupIp}")
	private String backupIp;

	/**
	 * IBE 服务器 端口
	 */
	@Value("${ibe.config.port}")
	private String port;

	/**
	 * 应用程序
	 */
	@Value("${ibe.config.app}")
	private String app;

	/**
	 * Office号
	 */
	@Value("${ibe.config.office}")
	private String office;

	/**
	 * 客户号
	 */
	@Value("${ibe.config.customno}")
	private String customno;
	
	/**
	 * 验证码
	 */
	@Value("${ibe.config.validationno}")
	private String validationno;
	
	/**
	 * 打票机号多个用,分割
	 */
	@Value("${ibe.config.printNos}")
	private String printNos;

	@Value("${ibe.config.printInterNos}")
	private String printInterNos;

}
