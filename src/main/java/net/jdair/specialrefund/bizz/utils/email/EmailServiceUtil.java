package net.jdair.specialrefund.bizz.utils.email;

import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord;
import net.jdair.specialrefund.bizz.domain.InterRefundMonitorRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EmailServiceUtil {

    @Autowired
    private EmailConfig emailConfig;


    /**
     * 特殊退票退款监控预警发送邮件，场景一
     * @param receiver 收件人
     */
    @Async
    public void sendEmail4TSRefundMonitor1(String receiver, List<EtTsRefundMonitorRecord> list) {
        String refundNoStr = list.stream().map(EtTsRefundMonitorRecord::getRefundNo).collect(Collectors.joining(","));
        String content = "<html xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\"\n" +
                "      xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\">\n" +
                "<head>\n" +
                "    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n" +
                "    <meta name=\"Generator\" content=\"Microsoft Word 15 (filtered medium)\">\n" +
                "    <style><!--\n" +
                "    /* Font Definitions */\n" +
                "    @font-face {\n" +
                "        font-family: SimSun;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"Cambria Math\";\n" +
                "        panose-1: 2 4 5 3 5 4 6 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: DengXian;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: Calibri;\n" +
                "        panose-1: 2 15 5 2 2 2 4 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@DengXian\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@SimSun\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    /* Style Definitions */\n" +
                "    p.MsoNormal, li.MsoNormal, div.MsoNormal {\n" +
                "        margin: 0cm;\n" +
                "        font-size: 11.0pt;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "    }\n" +
                "\n" +
                "    span.EmailStyle17 {\n" +
                "        mso-style-type: personal-compose;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "        color: windowtext;\n" +
                "    }\n" +
                "\n" +
                "    .MsoChpDefault {\n" +
                "        mso-style-type: export-only;\n" +
                "    }\n" +
                "\n" +
                "    @page WordSection1 {\n" +
                "        size: 612.0pt 792.0pt;\n" +
                "        margin: 72.0pt 72.0pt 72.0pt 72.0pt;\n" +
                "    }\n" +
                "\n" +
                "    div.WordSection1 {\n" +
                "        page: WordSection1;\n" +
                "    }\n" +
                "\n" +
                "    --></style>\n" +
                "</head>\n" +
                "<body lang=\"en-CN\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n" +
                "<div class=\"WordSection1\">\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">" +
                "        特殊退票订单号" + refundNoStr + "存在二次退款，请核实审核是否正确。" +
                "        <o:p></o:p>\n" +
                "    </p>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        log.info("sendEmail4TSRefundMonitor1 | receiver: " + receiver);
        String sendMailFrom = emailConfig.getFrom();
        String sendMailPwd = emailConfig.getPwd();
        this.sendEmail("特殊退票退款预警通知", new StringBuilder(content), null, receiver, sendMailFrom, sendMailPwd);
    }


    /**
     * 特殊退票退款监控预警发送邮件, 场景二
     * @param receiver 收件人
     */
    @Async
    public void sendEmail4TSRefundMonitor2(String receiver, List<EtTsRefundMonitorRecord> list) {
        StringBuilder sb = new StringBuilder("");
        for (EtTsRefundMonitorRecord record : list) {
            StringBuilder s = new StringBuilder("");
            s.append("收款账号: ")
                    .append(record.getCardNo())
                    .append("，收款人: ")
                    .append(record.getCardHolder())
                    .append("，产生同金额退款（特殊退票订单为: ")
                    .append(record.getRefundNo())
                    .append("）, 退款金额: ")
                    .append(record.getAmount());

            sb.append("    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;\">" +
                            "        <o:p>" + s + "</o:p>\n" +
                            "    </p>\n");
        }
        String content = "<html xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\"\n" +
                "      xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\">\n" +
                "<head>\n" +
                "    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n" +
                "    <meta name=\"Generator\" content=\"Microsoft Word 15 (filtered medium)\">\n" +
                "    <style><!--\n" +
                "    /* Font Definitions */\n" +
                "    @font-face {\n" +
                "        font-family: SimSun;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"Cambria Math\";\n" +
                "        panose-1: 2 4 5 3 5 4 6 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: DengXian;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: Calibri;\n" +
                "        panose-1: 2 15 5 2 2 2 4 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@DengXian\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@SimSun\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    /* Style Definitions */\n" +
                "    p.MsoNormal, li.MsoNormal, div.MsoNormal {\n" +
                "        margin: 0cm;\n" +
                "        font-size: 11.0pt;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "    }\n" +
                "\n" +
                "    span.EmailStyle17 {\n" +
                "        mso-style-type: personal-compose;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "        color: windowtext;\n" +
                "    }\n" +
                "\n" +
                "    .MsoChpDefault {\n" +
                "        mso-style-type: export-only;\n" +
                "    }\n" +
                "\n" +
                "    @page WordSection1 {\n" +
                "        size: 612.0pt 792.0pt;\n" +
                "        margin: 72.0pt 72.0pt 72.0pt 72.0pt;\n" +
                "    }\n" +
                "\n" +
                "    div.WordSection1 {\n" +
                "        page: WordSection1;\n" +
                "    }\n" +
                "\n" +
                "    --></style>\n" +
                "</head>\n" +
                "<body lang=\"en-CN\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n" +
                "<div class=\"WordSection1\">\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">" +
                "        <o:p>" + sb + "</o:p>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto\"><span style=\"font-size:10.5pt\">请核实审核是否正确。<o:p></o:p></span>\n" +
                "    </p>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        log.info("sendEmail4TSRefundMonitor2 | receiver: " + receiver);
        String sendMailFrom = emailConfig.getFrom();
        String sendMailPwd = emailConfig.getPwd();
        this.sendEmail("特殊退票退款预警通知", new StringBuilder(content), null, receiver, sendMailFrom, sendMailPwd);
    }


    /**
     * 海外站退款成功后发送邮件
     * @param receiver 收件人
     * @param refundAmount 退款金额
     */
    @Async
    public void sendEmail4HWZ(String receiver, String refundAmount) {
        String content = "<html xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\"\n" +
                "      xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\">\n" +
                "<head>\n" +
                "    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n" +
                "    <meta name=\"Generator\" content=\"Microsoft Word 15 (filtered medium)\">\n" +
                "    <style><!--\n" +
                "    /* Font Definitions */\n" +
                "    @font-face {\n" +
                "        font-family: SimSun;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"Cambria Math\";\n" +
                "        panose-1: 2 4 5 3 5 4 6 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: DengXian;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: Calibri;\n" +
                "        panose-1: 2 15 5 2 2 2 4 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@DengXian\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@SimSun\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    /* Style Definitions */\n" +
                "    p.MsoNormal, li.MsoNormal, div.MsoNormal {\n" +
                "        margin: 0cm;\n" +
                "        font-size: 11.0pt;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "    }\n" +
                "\n" +
                "    span.EmailStyle17 {\n" +
                "        mso-style-type: personal-compose;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "        color: windowtext;\n" +
                "    }\n" +
                "\n" +
                "    .MsoChpDefault {\n" +
                "        mso-style-type: export-only;\n" +
                "    }\n" +
                "\n" +
                "    @page WordSection1 {\n" +
                "        size: 612.0pt 792.0pt;\n" +
                "        margin: 72.0pt 72.0pt 72.0pt 72.0pt;\n" +
                "    }\n" +
                "\n" +
                "    div.WordSection1 {\n" +
                "        page: WordSection1;\n" +
                "    }\n" +
                "\n" +
                "    --></style>\n" +
                "</head>\n" +
                "<body lang=\"en-CN\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n" +
                "<div class=\"WordSection1\">\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">Dear Customer,\n" +
                "        <o:p></o:p>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">Thank you for choosing\n" +
                "        Beijing Capital Airlines.\n" +
                "        <o:p></o:p>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">After verification, your\n" +
                "        final refund amount is " + refundAmount + " and refunded to your original paying account.\n" +
                "        <o:p></o:p>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">\n" +
                "        <o:p>&nbsp;</o:p>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto\"><span style=\"font-size:10.5pt\">************************************************<o:p></o:p></span>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto\"><span style=\"font-size:10.5pt\">Please note, this email was sent from a notification-only address that cannot accept incoming email. Please do not reply to this message.<o:p></o:p></span>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto\"><span style=\"font-size:10.5pt\">************************************************<o:p></o:p></span>\n" +
                "    </p>\n" +
                "    <p class=\"MsoNormal\">\n" +
                "        <o:p>&nbsp;</o:p>\n" +
                "    </p>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        log.info("sendEmail4HWZ| receiver: " + receiver);
        String sendMailFrom = emailConfig.getFrom();
        String sendMailPwd = emailConfig.getPwd();
        this.sendEmail("Confirmation for Refunded", new StringBuilder(content), null, receiver, sendMailFrom, sendMailPwd);
    }


    /**
     * 国际票海外站退款监控预警发送邮件
     * @param receiver 收件人
     */
    @Async
    public void sendEmail4InterRefundMonitor(String receiver, List<InterRefundMonitorRecord> list) {
        String refundNoStr = list.stream().map(InterRefundMonitorRecord::getRefundNo).collect(Collectors.joining(","));
        String content = "<html xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\"\n" +
                "      xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\">\n" +
                "<head>\n" +
                "    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n" +
                "    <meta name=\"Generator\" content=\"Microsoft Word 15 (filtered medium)\">\n" +
                "    <style><!--\n" +
                "    /* Font Definitions */\n" +
                "    @font-face {\n" +
                "        font-family: SimSun;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"Cambria Math\";\n" +
                "        panose-1: 2 4 5 3 5 4 6 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: DengXian;\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: Calibri;\n" +
                "        panose-1: 2 15 5 2 2 2 4 3 2 4;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@DengXian\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    @font-face {\n" +
                "        font-family: \"\\@SimSun\";\n" +
                "        panose-1: 2 1 6 0 3 1 1 1 1 1;\n" +
                "    }\n" +
                "\n" +
                "    /* Style Definitions */\n" +
                "    p.MsoNormal, li.MsoNormal, div.MsoNormal {\n" +
                "        margin: 0cm;\n" +
                "        font-size: 11.0pt;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "    }\n" +
                "\n" +
                "    span.EmailStyle17 {\n" +
                "        mso-style-type: personal-compose;\n" +
                "        font-family: \"Calibri\", sans-serif;\n" +
                "        color: windowtext;\n" +
                "    }\n" +
                "\n" +
                "    .MsoChpDefault {\n" +
                "        mso-style-type: export-only;\n" +
                "    }\n" +
                "\n" +
                "    @page WordSection1 {\n" +
                "        size: 612.0pt 792.0pt;\n" +
                "        margin: 72.0pt 72.0pt 72.0pt 72.0pt;\n" +
                "    }\n" +
                "\n" +
                "    div.WordSection1 {\n" +
                "        page: WordSection1;\n" +
                "    }\n" +
                "\n" +
                "    --></style>\n" +
                "</head>\n" +
                "<body lang=\"en-CN\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n" +
                "<div class=\"WordSection1\">\n" +
                "    <p class=\"MsoNormal\" style=\"mso-margin-top-alt:auto;margin-bottom:10.0pt;line-height:113%\">" +
                "        国际票海外站退票订单号" + refundNoStr + "存在二次退款，请核实审核是否正确。" +
                "        <o:p></o:p>\n" +
                "    </p>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>\n";
        log.info("sendEmail4InterRefundMonitor | receiver: " + receiver);
        String sendMailFrom = emailConfig.getFrom();
        String sendMailPwd = emailConfig.getPwd();
        this.sendEmail("国际票海外站退款预警通知", new StringBuilder(content), null, receiver, sendMailFrom, sendMailPwd);
    }


    /**
     * 发邮件
     * @param subject 邮件主题
     * @param content 邮件正文
     * @param attachmentPath 邮件附件路径
     * @param receiver 收件人（多个人使用逗号隔开）
     */
    private void sendEmail(String subject, StringBuilder content, String attachmentPath, String receiver, String sendMailFrom, String sendMailPwd) {
        log.info("***Begin auto function sendEmail()");
        Map<String, String> params = new HashMap<String, String>();
        //必填项
        params.put("toEmail", receiver);//收件邮箱，多个邮箱间用英文逗号隔开
        params.put("subject", subject);//主题
        params.put("content", content+"");//内容
        params.put("fromEmail", sendMailFrom + "@hnair.com");//发件邮箱
        params.put("fromName", sendMailFrom);//发件人内网账号 emailConfig.getFrom()
        params.put("fromPasswd", sendMailPwd);//发件人邮箱密码
        //非必填
        params.put("ccEmail", "");//抄送邮箱，多个邮箱间用英文逗号隔开
        params.put("bccEmail", "");//密送邮箱，多个邮箱间用英文逗号隔开
        if(null!=attachmentPath && !attachmentPath.equals("")){
            String fileName=attachmentPath.substring(attachmentPath.lastIndexOf("/")+1);
            try
            {
                params.put("contentType[0].name", fileName);      //附件名称
                params.put("contentType[0].content", readFile(attachmentPath));//发送附件  如果多个附件增加中括号中的数字即可
            } catch ( Exception e ){
                log.info(e.toString());
            }
        }
        int count=5;
        for(int k=0;k<count;k++){
            try {
                String resultString = HTTPClientUtil.getEmailObject(params);
                log.info("***response:"+resultString);
                if (resultString == "succ" || "succ".equals(resultString)) {
                    break;
                }
            } catch (Exception e) {
                log.info(e.toString());
            }
        }
    }

    /*
     * 读取文件base64内容
     */
    private static String readFile(String fileName) throws Exception {
        File f = new File(fileName);                    //创建文件对象
        FileInputStream fm = new FileInputStream(f);   //创建文件输入流
        int len = fm.available();                       //计算内容长度
        byte[] bytes = new byte[len];
        fm.read(bytes); //读取文件内容
        fm.close();     //关闭输入流
        String fileContent = (new BASE64Encoder()).encode(bytes); //字节数组转换为字符串
        //返回文件内容
        return fileContent;
    }

}
