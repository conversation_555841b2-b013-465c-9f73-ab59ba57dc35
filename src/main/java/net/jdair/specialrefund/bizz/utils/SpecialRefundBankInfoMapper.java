package net.jdair.specialrefund.bizz.utils;


import net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundBankInfo;

public class SpecialRefundBankInfoMapper extends Mapper<DTOSpecialRefundBankInfo, EtSpecialRefundBankInfo> {
    private static SpecialRefundBankInfoMapper mapper;

    private SpecialRefundBankInfoMapper() {
        super(DTOSpecialRefundBankInfo.class, EtSpecialRefundBankInfo.class);
    }

    public static SpecialRefundBankInfoMapper getInstance() {
        if (mapper == null) {
            mapper = new SpecialRefundBankInfoMapper();
        }
        return mapper;
    }

    @Override
    public EtSpecialRefundBankInfo mapToEntity(DTOSpecialRefundBankInfo value) {
        EtSpecialRefundBankInfo t = super.mapToEntity(value);

        if (value.getId() == null) {
            t.setId(null);
        }

        return t;
    }

    @Override
    public DTOSpecialRefundBankInfo mapToValue(EtSpecialRefundBankInfo entity) {
        DTOSpecialRefundBankInfo value = super.mapToValue(entity);

        return value;
    }
}
