/**
*Copyright (c) HNA SYSTEMS CO.,LTD
*
*@date 2008-11-18
*
*Original Author: 王勇明(yongm_wang)
*
*ChangeLog:
*
* 
*/

package net.jdair.specialrefund.bizz.utils.pay.sdk;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 支付宝退款账号配置
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "pay.wxpay")
public class WXPayConfigProperties {
	private Map<String, WXPayConfig> source;
}
