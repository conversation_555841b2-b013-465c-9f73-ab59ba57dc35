package net.jdair.specialrefund.bizz.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Title : AmountUtils
 * Description : This is the util class for amount
 * 
 * <AUTHOR>
 */
public class AmountUtils {

	static DecimalFormat myFormatter = new DecimalFormat("0");
	
	/**
	 * This method converts Big Decimal amount to amount string
	 *  
	 * @param amountBd
	 * @return
	 */
	public static String convertBigDecimalToAmountStr(BigDecimal amountBd) {
		String amountStr = "";
		
		if(amountBd!=new BigDecimal("0")) {
			amountStr = myFormatter.format(amountBd.multiply(new BigDecimal(100)));
		}
		
		return amountStr;
	}
	
	public static String convertBigDecimalToAmountStr(BigDecimal amountBd,String format) {
		DecimalFormat formatter=new DecimalFormat(format);
		String amountStr = formatter.format(amountBd.doubleValue());
		
		return amountStr;
		
	}
	
	public static String convertDoubleToAmountStr(double amountDb) {
		
		if("0.0".equals( String.valueOf( amountDb ) ))
		{
			return "0";
		}
		if("0.0010".equals( String.valueOf( amountDb ) ))
		{
			return "";
		}
		String amountStr = "0";
		
		if(amountDb>0) {
			amountStr = myFormatter.format(amountDb*100);
		}
		
		return amountStr;
	}
	
	/**
	 * This method convert amount str with 2 decimal places to double
	 * 
	 * @param amount
	 * @return
	 */
	public static double convertAmountStrToDouble(String amount) {
		
		double amountDb = 0.0;
		
		if(amount!=null && !"".equalsIgnoreCase(amount)) {
			amountDb = Double.parseDouble(amount)/100;
		}
		
		return amountDb;
	}
	
	/**
	 * This convert amount str with 2 decimal places to big decimal
	 * 
	 * @param amount
	 * @return
	 */
	public static BigDecimal convertAmountStrToBigDecimal(String amount) {
		
		DecimalFormat myFormatter = new DecimalFormat("#.00");
		BigDecimal amountBd = new BigDecimal("0");
		
		if(amount!=null) {
			amountBd = new BigDecimal(amount).divide(new BigDecimal(100));
			amountBd = new BigDecimal(myFormatter.format(amountBd));
		}
		
		return amountBd;
	}	
	
	public static void main(String[] args ){
		System.out.println(convertBigDecimalToAmountStr(new BigDecimal(20.1)));
	}
	
}
