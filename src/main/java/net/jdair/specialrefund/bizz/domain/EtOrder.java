package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_order
 */
@TableName(value ="et_order")
@Data
public class EtOrder implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String orderNo;

    /**
     * 
     */
    private String ticketType;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private String officeNo;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private String contactName;

    /**
     * 
     */
    private String contactTel;

    /**
     * 
     */
    private String contactMobile;

    /**
     * 
     */
    private String contactEmail;

    /**
     * 
     */
    private String contactAddress;

    /**
     * 
     */
    private String currency;

    /**
     * 
     */
    private Date timeLimit;

    /**
     * 
     */
    private String airlineCode;

    /**
     * 
     */
    private String pnrImport;

    /**
     * 
     */
    private BigDecimal mailFee;

    /**
     * 
     */
    private String ipAddress;

    /**
     * 
     */
    private Integer international;

    /**
     * 
     */
    private String source;

    /**
     * 
     */
    private BigDecimal onSaleAmount;

    /**
     * 
     */
    private BigDecimal quotaValue;

    /**
     * 
     */
    private String couponNo;

    /**
     * 
     */
    private String fullPoint;

    /**
     * 
     */
    private BigDecimal pointAmount;

    /**
     * 
     */
    private String couponType;

    /**
     * 
     */
    private Integer groupmark;

    /**
     * 
     */
    private Integer issurSmsSended;

    /**
     * 
     */
    private String cardRemark;

    /**
     * 
     */
    private String itinerary;

    /**
     * 
     */
    private String webSource;

    /**
     * 
     */
    private String encrypted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}