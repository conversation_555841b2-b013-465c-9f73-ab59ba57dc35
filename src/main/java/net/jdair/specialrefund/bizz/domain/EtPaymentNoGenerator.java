package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 退款支付流水号生成表
 * @TableName et_payment_no_generator
 */
@TableName(value ="et_payment_no_generator")
@Data
public class EtPaymentNoGenerator implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 退票单号
     */
    private String refundNo;

    /**
     * 退款支付流水号
     */
    private String paymentNo;

    /**
     * 退款支付流水号使用状态。（WAIT_USE: 待使用，USED: 已使用）
     */
    private String paymentNoStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 支付流水号更新时间（多次二审通过的情况）
     */
    private Date updateTime;

    /**
     * 使用时间
     */
    private Date usedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}