package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName et_payment_wxpay
 */
@TableName(value ="et_payment_wxpay")
@Data
public class EtWXpayPayment extends EtPayment implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private String dealId;

    /**
     * 
     */
    private String bankId;

    /**
     * 
     */
    private BigDecimal fee;

    /**
     * 
     */
    private String callbackinfo;

    /**
     * 
     */
    private String transname;

    /**
     * 
     */
    private String purchaserId;

    /**
     * 
     */
    private String attach;

    /**
     * 
     */
    private String cmdno;

    /**
     * 
     */
    private String signmsg;

    /**
     * 
     */
    private String busType;

    /**
     * 
     */
    private String busArgs;

    /**
     * 
     */
    private String busDesc;

    /**
     * 
     */
    private String payResult;

    /**
     * 
     */
    private String bankDealId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}