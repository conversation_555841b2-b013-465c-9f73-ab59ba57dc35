package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 
 * @TableName et_passenger_segment
 */
@TableName(value ="et_passenger_segment")
@Data
public class EtPassengerSegment implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long passengerId;

    /**
     * 
     */
    private Long segmentId;

    /**
     * 
     */
    private Long ticketId;

    /**
     * 
     */
    private BigDecimal marketFare;

    /**
     * 
     */
    private BigDecimal netFare;

    /**
     * 
     */
    private BigDecimal airportTax;

    /**
     * 
     */
    private BigDecimal fuelTax;

    /**
     * 
     */
    private BigDecimal otherTaxes;

    /**
     * 
     */
    private BigDecimal tax1;

    /**
     * 
     */
    private BigDecimal tax2;

    /**
     * 
     */
    private BigDecimal tax3;

    /**
     * 
     */
    private BigDecimal tax4;

    /**
     * 
     */
    private BigDecimal insurance;

    /**
     * 
     */
    private BigDecimal agentFeeRate;

    /**
     * 
     */
    private BigDecimal spFeeRate;

    /**
     * 
     */
    private BigDecimal agentFee;

    /**
     * 
     */
    private BigDecimal spFee;

    /**
     * 
     */
    private String fareBasis;

    /**
     * 
     */
    private Date notValidBefore;

    /**
     * 
     */
    private Date notValidAfter;

    /**
     * 
     */
    private String allow;

    /**
     * 
     */
    private String ticketStatus;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private Long productId;

    /**
     * 
     */
    private Integer sequence;

    /**
     * 
     */
    private Long point;

    /**
     * 
     */
    private String hasFreeTicket;

    /**
     * 
     */
    private String usedFreeTicket;

    /**
     * 
     */
    private String used;

    /**
     * 
     */
    private String payFreeTicket;

    /**
     * 
     */
    private String currency;

    /**
     * 
     */
    private BigDecimal couponPrice;

    /**
     * 
     */
    private BigDecimal oldNetFare;

    /**
     * 
     */
    private Long orderId;

    /**
     * 
     */
    private BigDecimal pointPrice;

    /**
     * 
     */
    private String refCabin;

    /**
     * 
     */
    private BigDecimal refFare;

    /**
     * 
     */
    private String oriProductCode;


    /**
     * 旅客航段中的旅客对象
     */
    @TableField(exist = false)
    private EtPassenger etPassenger;

    /**
     * 旅客航段中的航段对象
     */
    @TableField(exist = false)
    private EtSegment etSegment;

    /**
     * 旅客航段中的客票对象
     */
    @TableField(exist = false)
    private EtTicket etTicket;

    /**
     * 旅客航段中的产品对象
     */
    @TableField(exist = false)
    private EtProduct etProduct;

    /**
     * 特殊退票换开场景的原航段
     */
    @TableField(exist = false)
    private EtSpecialRefundOriginTkt specialRefundOriginTkt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}