package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName
 */
@Data
public class EtTsRefundMonitorRecord implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long preMaxPaymentId;
    private Date createTime;
    private Date updateTime;

    @TableField(exist = false)
    private String refundNo;

    @TableField(exist = false)
    private String cardNo;

    @TableField(exist = false)
    private String cardHolder;

    @TableField(exist = false)
    private String amount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}