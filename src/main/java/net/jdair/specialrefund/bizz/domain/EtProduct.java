package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_product
 */
@TableName(value ="et_product")
@Data
public class EtProduct implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String code;

    /**
     * 
     */
    private String airlineCode;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String description;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private String createId;

    /**
     * 
     */
    private Date createDate;

    /**
     * 
     */
    private String ei;

    /**
     * 
     */
    private String productType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}