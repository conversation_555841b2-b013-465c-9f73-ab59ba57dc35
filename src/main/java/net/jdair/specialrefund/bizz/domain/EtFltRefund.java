package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;
import net.jdair.specialrefund.bizz.vo.DTOFltRefundPaxSeg;
import net.jdair.specialrefund.bizz.vo.DTOPayment;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundBankInfo;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundImg;

/**
 * 
 * @TableName et_flt_refund
 */
@TableName(value ="et_flt_refund")
@Data
public class EtFltRefund implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String refundNo;

    /**
     * 
     */
    private Long orderId;

    /**
     * 
     */
    private String userName;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private Date payTime;

    /**
     * 
     */
    private String payMode;

    /**
     * 
     */
    private String airlineCode;

    /**
     * 
     */
    private String refundType;

    /**
     * 
     */
    private String officeNo;

    /**
     * 
     */
    private String ftStatus;

    /**
     * 
     */
    private String pointStatus;

    /**
     * 
     */
    private String userType;

    /**
     * 
     */
    private String orderSource;

    /**
     * 
     */
    private Date retrieveTime;

    /**
     * 
     */
    private Long changeId;

    /**
     * 临时使用，用于标识国际票退票单是否购买随票保险
     */
    private Integer retrieveTimeFlag;

    /**
     * 
     */
    private String couponNo;

    /**
     * 
     */
    private String couponType;

    /**
     * 
     */
    private String constraintReason;

    /**
     * 
     */
    private String fictitious;

    /**
     * 
     */
    private String reserveRemark;

    @TableField(exist = false)
    private String refundUser;

    @TableField(exist = false)
    private Date refundTime;

    @TableField(exist = false)
    private List<EtFltRefundPaxSeg> etFltRefundPaxSegList = new ArrayList<EtFltRefundPaxSeg>();

    /**
     * 退票单中的旅客航段对象
     */
    @TableField(exist = false)
    private List<EtPassengerSegment> etPassengerSegmentList;

    /**
     * 退票单中的退款记录中间表
     */
    @TableField(exist = false)
    private List<EtFltRefundPayment> etFltRefundPaymentList;

    /**
     * 退款支付记录表
     */
    @TableField(exist = false)
    private List<EtPayment> etRefundPaymentList;

    /**
     * 外部支付记录表
     */
    @TableField(exist = false)
    private List<EtPayment> etOutsidePaymentList;

    /**
     * 审核记录表
     */
    @TableField(exist = false)
    private List<EtFltRefundAudit> fltRefundAudit;

    /**
     * 特殊退票的银行信息
     */
    @TableField(exist = false)
    private EtSpecialRefundBankInfo etSpecialRefundBankInfo;

    /**
     * 特殊退票的用户上传的图片 url
     */
    @TableField(exist = false)
    private List<EtSpecialRefundImg> etSpecialRefundImgList;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}