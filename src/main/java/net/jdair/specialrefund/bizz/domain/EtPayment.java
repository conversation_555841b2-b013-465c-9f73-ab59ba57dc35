package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_payment
 */
@TableName(value ="et_payment")
@Data
public class EtPayment implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(exist = false)
    private Long refundId;

    /**
     * 
     */
    private String paymentNo;

    /**
     * 
     */
    private String payType;

    /**
     * 
     */
    private String action;

    /**
     * 
     */
    private BigDecimal amount;

    /**
     * 
     */
    private String payStatus;

    /**
     * 
     */
    private Date payTime;

    /**
     * 
     */
    private String currency;

    /**
     * 
     */
    private String payer;

    /**
     * 
     */
    private String batch;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private Integer settDate;

    /**
     * 
     */
    private String field1;

    /**
     * 
     */
    private String field2;

    /**
     * 
     */
    private String field3;

    /**
     * 
     */
    private String merchantId;

    /**
     * 
     */
    private String payMode;

    /**
     * 
     */
    private Date returnTime;

    /**
     * 
     */
    private String systemSsn;

    /**
     * 
     */
    private String source;

    /**
     * 
     */
    private String cardType;

    /**
     * 
     */
    private String bankId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}