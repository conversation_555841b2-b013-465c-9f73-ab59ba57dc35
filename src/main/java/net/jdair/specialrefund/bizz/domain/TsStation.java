package net.jdair.specialrefund.bizz.domain;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TsStation {

    private static final long serialVersionUID = 1L;

    private Long airportId;


    private String iataId;


    private String icaoCode;


    private String city;


    private String cityCn;


    private String cityEn;


    private String country;


    private String countryDescCn;


    private String countryDescEn;

    private String timeZone;


    private String focTimeZone;


    private String utc;

    private String airportType;

    private String airportName;

    private String airportNameCn;


    private String airportNameEn;


    private BigDecimal isDelete;


    private LocalDateTime updatedTime;

}
