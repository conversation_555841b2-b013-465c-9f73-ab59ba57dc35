package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName et_flt_refund_pax_seg
 */
@TableName(value ="et_flt_refund_pax_seg")
@Data
public class EtFltRefundPaxSeg implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long refundId;

    /**
     * 
     */
    private Long paxSegId;

    /**
     * 
     */
    private BigDecimal refundAmount = new BigDecimal(0);

    /**
     * 
     */
    private BigDecimal actualRefundAmount =  new BigDecimal(0);

    /**
     * 
     */
    private Integer refundPoint;

    /**
     * 
     */
    private BigDecimal amsRefundPoint;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}