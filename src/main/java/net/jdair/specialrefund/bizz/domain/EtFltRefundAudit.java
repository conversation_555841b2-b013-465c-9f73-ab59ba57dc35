package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_flt_refund_audit
 */
@TableName(value ="et_flt_refund_audit")
@Data
public class EtFltRefundAudit implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long refundId;

    /**
     * 
     */
    private String submitUsername;

    /**
     * 
     */
    private Date submitTime;

    /**
     * 
     */
    private String auditUsername;

    /**
     * 
     */
    private Date auditTime;

    /**
     * 
     */
    private String action;

    /**
     * 
     */
    private String notion;

    /**
     * 
     */
    private String amountstr;

    /**
     * 
     */
    private String auditResult;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}