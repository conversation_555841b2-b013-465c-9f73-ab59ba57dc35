package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_special_refund_origin_tkt
 */
@TableName(value ="et_special_refund_origin_tkt")
@Data
public class EtSpecialRefundOriginTkt implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long paxSegId;

    /**
     * 
     */
    private String passengerName;

    /**
     * 
     */
    private String passengerType;

    /**
     * 
     */
    private String certNo;

    /**
     * 
     */
    private String depCode;

    /**
     * 
     */
    private String arrCode;

    /**
     * 
     */
    private String airlineCode;

    /**
     * 
     */
    private String flightNo;

    /**
     * 
     */
    private String cabinClass;

    /**
     * 
     */
    private String ticketNo;

    /**
     * 
     */
    private Date depTime;

    /**
     * 
     */
    private Date arrTime;

    /**
     * 
     */
    private BigDecimal marketFare;

    /**
     * 
     */
    private BigDecimal netFare;

    /**
     * 
     */
    private BigDecimal airportTax;

    /**
     * 
     */
    private BigDecimal fuelTax;

    /**
     * 
     */
    private BigDecimal otherTaxes;

    /**
     * 
     */
    private BigDecimal tax1;

    /**
     * 
     */
    private BigDecimal tax2;

    /**
     * 
     */
    private BigDecimal tax3;

    /**
     * 
     */
    private BigDecimal tax4;

    /**
     * 
     */
    private String ticketStatus;

    /**
     * 
     */
    private Integer sequence;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}