package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_segment
 */
@TableName(value ="et_segment")
@Data
public class EtSegment implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long orderId;

    /**
     * 
     */
    private String depCode;

    /**
     * 
     */
    private String arrCode;

    /**
     * 
     */
    private String cabinClass;

    /**
     * 
     */
    private String flightNo;

    /**
     * 
     */
    private Date depTime;

    /**
     * 
     */
    private Date arrTime;

    /**
     * 
     */
    private String planeType;

    /**
     * 
     */
    private String groupNo;

    /**
     * 
     */
    private String airlineCode;

    /**
     * 
     */
    private String inventory;

    /**
     * 
     */
    private BigDecimal baseFare;

    /**
     * 
     */
    private String arrTerminal;

    /**
     * 
     */
    private String depTerminal;

    /**
     * 
     */
    private String stop;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}