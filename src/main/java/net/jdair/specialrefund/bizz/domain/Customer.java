package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName customer
 */
@TableName(value ="customer")
@Data
public class Customer implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String certificateno;

    /**
     * 
     */
    private String certificatetype;

    /**
     * 
     */
    private String frequenceno;

    /**
     * 
     */
    private String gender;

    /**
     * 
     */
    private Date birthday;

    /**
     * 
     */
    private String othername;

    /**
     * 
     */
    private String frequentflyerno;

    /**
     * 
     */
    private String firstname;

    /**
     * 
     */
    private String lastname;

    /**
     * 
     */
    private String nationality;

    /**
     * 
     */
    private String passportno;

    /**
     * 
     */
    private String expiredDate;

    /**
     * 
     */
    private String bornDate;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private String issueCountry;

    /**
     * 
     */
    private String depcity;

    /**
     * 
     */
    private String depprovince;

    /**
     * 
     */
    private String depcountry;

    /**
     * 
     */
    private String depstreet;

    /**
     * 
     */
    private String deppost;

    /**
     * 
     */
    private String arrcity;

    /**
     * 
     */
    private String arrprovince;

    /**
     * 
     */
    private String arrcountry;

    /**
     * 
     */
    private String arrpost;

    /**
     * 
     */
    private String arrstreet;

    /**
     * 
     */
    private String jjctype;

    /**
     * 
     */
    private String jjcno;

    /**
     * 
     */
    private String encrypted;

    /**
     * 
     */
    private Date updateDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}