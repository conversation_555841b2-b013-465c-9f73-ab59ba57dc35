package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 
 * @TableName et_passenger
 */
@TableName(value ="et_passenger")
@Data
public class EtPassenger implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long customerId;

    /**
     * 
     */
    private Long pnrId;

    /**
     * 
     */
    private Long orderId;

    /**
     * 
     */
    private String passengerType;

    /**
     * 
     */
    private Long accompany;

    /**
     * 
     */
    private Integer changeNameTime;

    /**
     * 
     */
    private Integer changeCertiTime;

    /**
     * 
     */
    private String xcdPrintStatus;

    /**
     * 
     */
    private String mobilePhone;

    /**
     * 
     */
    private Long isFlightsms;

    /**
     * 
     */
    private String coPromote;

    /**
     * 
     */
    private String memberPlusNo;

    /**
     * 
     */
    private String encrypted;


    /**
     * 旅客对象中的 Customer 对象
     */
    @TableField(exist = false)
    private Customer customer;

    /**
     * 旅客对象中的 Pnr 对象
     */
    @TableField(exist = false)
    private EtPnr etPnr;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}