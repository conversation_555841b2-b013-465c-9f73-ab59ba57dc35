package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 易生代付账户配置
 */
@Data
@TableName(value ="et_easycard_df_payment_config")
public class EtEasyCardDFConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商户与易生支付合作后在易生支付产生的商户ID
     * 合作伙伴在易生支付的用户ID，登录易生支付商户后台 商家服务查看商户编号
     */
    private String partnerId;

    /**
     * 合作伙伴在易生支付的用户ID，登录易生支付商户后台 商家服务查看商户编号
     */
    private String merchantId;

    /**
     * 商户私钥(签约时分配给商户的key)
     */
    private String merchantPrivateKey;

    /**
     * 易生公钥
     */
    private String easypayPublicKey;

    /**
     * 加密密钥
     */
    private String desEncodeKey;

    /**
     * 请求地址
     */
    private String payUrl;

    /**
     * 代扣结果的通知地址
     */
    private String notifyUrl;

    private String purpose;

}
