package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 国际票附件上传
 * @TableName et_refund_gj_img
 */
@TableName(value ="et_refund_gj_img")
@Data
public class EtRefundGjImg implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long fltRefundId;

    /**
     * 
     */
    private String imgUrl;

    /**
     * 
     */
    private String refundNo;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Long refundAuditId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}