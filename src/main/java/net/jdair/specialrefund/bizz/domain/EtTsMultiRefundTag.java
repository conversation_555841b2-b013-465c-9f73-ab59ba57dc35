package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 特殊退票重复退票熔断标识表
 * @TableName et_ts_multi_refund_tag
 */
@TableName(value ="et_ts_multi_refund_tag")
@Data
public class EtTsMultiRefundTag implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 在线退款开关。ACTIVE: 启用，INACTIVE: 禁用
     */
    private String onlineRefundStatus;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}