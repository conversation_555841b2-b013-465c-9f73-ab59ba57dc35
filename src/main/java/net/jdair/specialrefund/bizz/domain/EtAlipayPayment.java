package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName et_payment_alip
 */
@TableName(value ="et_payment_alip")
@Data
public class EtAlipayPayment extends EtPayment implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private String payerName;

    /**
     * 
     */
    private String payerContactType;

    /**
     * 
     */
    private String payerContact;

    /**
     * 
     */
    private String dealId;

    /**
     * 
     */
    private String bankId;

    /**
     * 
     */
    private String bankDealId;

    /**
     * 
     */
    private BigDecimal fee;

    /**
     * 
     */
    private String sellerEmail;

    /**
     * 
     */
    private String buyerEmail;

    /**
     * 
     */
    private String sellerId;

    /**
     * 
     */
    private String buyerId;

    /**
     * 
     */
    private String callbackinfo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}