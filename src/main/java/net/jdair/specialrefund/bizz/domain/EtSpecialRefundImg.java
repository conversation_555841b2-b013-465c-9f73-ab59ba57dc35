package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_special_refund_img
 */
@TableName(value ="et_special_refund_img")
@Data
public class EtSpecialRefundImg implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long fltRefundId;

    /**
     * 
     */
    private String imgUrl;
    /**
     *
     */
    private String imgOriginName;

    /**
     * 
     */
    private String refundNo;

    /**
     * 
     */
    private String source;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Long refundAuditId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}