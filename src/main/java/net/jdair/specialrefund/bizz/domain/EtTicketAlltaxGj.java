package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName et_ticket_alltax_gj
 */
@TableName(value ="et_ticket_alltax_gj")
@Data
public class EtTicketAlltaxGj implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long ticketId;

    /**
     * 
     */
    private String allTax;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}