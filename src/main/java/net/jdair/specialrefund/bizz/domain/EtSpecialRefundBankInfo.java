package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * ?????????
 * @TableName et_special_refund_bank_info
 */
@TableName(value ="et_special_refund_bank_info")
@Data
public class EtSpecialRefundBankInfo implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String cardNo;

    /**
     * 
     */
    private String cardHolder;

    /**
     * 
     */
    private String bankName;

    /**
     * 
     */
    private String mobileNo;

    /**
     * 
     */
    private String refundNo;

    /**
     * 
     */
    private Long fltRefundId;

    /**
     * 
     */
    private String nbkno;

    /**
     * 
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}