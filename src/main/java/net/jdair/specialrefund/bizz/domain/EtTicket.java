package net.jdair.specialrefund.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName et_ticket
 */
@TableName(value ="et_ticket")
@Data
public class EtTicket implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String issCode;

    /**
     * 
     */
    private String ticketNo;

    /**
     * 
     */
    private String serialNo;

    /**
     * 
     */
    private String serialNoCk;

    /**
     * 
     */
    private String printStatus;

    /**
     * 
     */
    private String bspType;

    /**
     * 
     */
    private String endorsement;

    /**
     * 
     */
    private String ck;

    /**
     * 
     */
    private String conjunctionTkt;

    /**
     * 
     */
    private String agentCode;

    /**
     * 
     */
    private String issuedBy;

    /**
     * 
     */
    private Date issuedDate;

    /**
     * 
     */
    private Date bookTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}