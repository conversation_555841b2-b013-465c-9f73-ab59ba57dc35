package net.jdair.specialrefund.bizz.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeFastpayRefundQueryRequest;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.backend.domain.FirstAuditReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.RefundAmountReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SecondAuditReq;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.DateUtil;
import net.jdair.specialrefund.bizz.utils.EncoderUtil;
import net.jdair.specialrefund.bizz.utils.HttpConnectUtils;
import net.jdair.specialrefund.bizz.utils.StringUtils;
import net.jdair.specialrefund.bizz.utils.email.EmailServiceUtil;
import net.jdair.specialrefund.bizz.utils.file.service.FileService;
import net.jdair.specialrefund.bizz.utils.file.vo.OssFileInfo;
import net.jdair.specialrefund.bizz.utils.pay.AlipayConfig;
import net.jdair.specialrefund.bizz.utils.pay.PayService;
import net.jdair.specialrefund.bizz.utils.pay.PaymentNoGenerator;
import net.jdair.specialrefund.bizz.utils.sms.SmsServiceUtil;
import net.jdair.specialrefund.bizz.vo.*;
import net.jdair.specialrefund.common.exception.GlobalException;
import net.jdair.specialrefund.common.response.CodeRefund;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FlightRefundServiceHelper {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;
    @Autowired
    private EtPaymentMapper etPaymentMapper;
    @Autowired
    private EtFltRefundOutsidePaymentMapper etFltRefundOutsidePaymentMapper;
    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;
    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private EtPnrMapper etPnrMapper;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    @Autowired
    private EtProductMapper etProductMapper;

    @Autowired
    private EtSegmentMapper etSegmentMapper;

    @Autowired
    private EtPassengerSegmentMapper etPassengerSegmentMapper;

    @Autowired
    private EtSpecialRefundOriginTktMapper etSpecialRefundOriginTktMapper;

    @Autowired
    private EtFltRefundPaxSegMapper etFltRefundPaxSegMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtAlipayPaymentMapper etAlipayPaymentMapper;

    @Autowired
    private EtWXpayPaymentMapper etWXpayPaymentMapper;

    @Autowired
    private EtTicketAlltaxGjMapper etTicketAlltaxGjMapper;

    @Autowired
    private EmailServiceUtil emailServiceUtil;

    @Autowired
    private SmsServiceUtil smsServiceUtil;

    @Autowired
    private PayService payService;

    @Autowired
    private AlipayConfig alipayConfig;

    @Autowired
    private EtEasyCardDFConfigMapper etEasyCardDFConfigMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private EtPaymentNoGeneratorMapper etPaymentNoGeneratorMapper;


    public List<EtFltRefund> findSpecialRefundByParams(String issCode, String ticketNo, String refundType, List<String> orderSourceList) {

        SearchRefundOrderListReq req = new SearchRefundOrderListReq();
        req.setIssCode(issCode);
        req.setTicketNo(ticketNo);
        req.setRefundType(refundType);
        req.setOrderSourceList(orderSourceList);
        return etFltRefundMapper.findRefundByParams(req);
    }


    /**
     * 查询退票单列表
     *
     * @return
     */
    public IPage<EtFltRefund> findRefundByParamsForPage(Page page, SearchRefundOrderListReq req) {
        IPage<EtFltRefund> etFltRefundList = etFltRefundMapper.findRefundListByParamsForPage(page, req);
        if (CollectionUtils.isEmpty(etFltRefundList.getRecords())) {
            return new Page<>();
        }

        // 2. 获取退票单ID列表
        List<Long> refundIds = etFltRefundList.getRecords().stream()
                .map(EtFltRefund::getId)
                .collect(Collectors.toList());

        // 3. 批量查询关联详情
        List<EtFltRefund> details = etFltRefundMapper.findRefundDetailByIdsForPage(refundIds);

        // 4. 将详情按退票单ID分组
        Map<Long, List<EtFltRefund>> detailMap = details.stream()
                .collect(Collectors.groupingBy(EtFltRefund::getId));

        List<EtFltRefundPayment> etFltRefundPaymentList = etFltRefundPaymentMapper.selectList(new QueryWrapper<EtFltRefundPayment>().in("flt_refund_id", details.stream().map(EtFltRefund::getId).collect(Collectors.toList())));
        List<EtPayment> etPaymentList = new ArrayList<>();
        if (etFltRefundPaymentList != null & etFltRefundPaymentList.size() > 0) {
            etPaymentList = etPaymentMapper.selectList(new QueryWrapper<EtPayment>().in("id", etFltRefundPaymentList.stream().map(EtFltRefundPayment::getPaymentId).collect(Collectors.toList())));
            for (EtFltRefundPayment etFltRefundPayment : etFltRefundPaymentList) {
                for (EtPayment etPayment : etPaymentList) {
                    if (etFltRefundPayment.getPaymentId().equals(etPayment.getId())) {
                        etFltRefundPayment.setEtRefundPayment(etPayment);
                    }
                }
            }
        }

        for (EtFltRefund record : etFltRefundList.getRecords()) {
            record.setEtFltRefundPaxSegList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtFltRefundPaxSegList());
            record.setEtPassengerSegmentList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtPassengerSegmentList());
            record.setEtFltRefundPaymentList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtFltRefundPaymentList());
            record.setEtRefundPaymentList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtRefundPaymentList());
            record.setEtOutsidePaymentList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtOutsidePaymentList());
            record.setFltRefundAudit(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getFltRefundAudit());
            record.setEtSpecialRefundBankInfo(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtSpecialRefundBankInfo());
            record.setEtSpecialRefundImgList(detailMap.getOrDefault(record.getId(), Collections.emptyList()).get(0).getEtSpecialRefundImgList());
            List<EtFltRefundPayment> refundPaymentList = new ArrayList<>();
            if (etFltRefundPaymentList != null && etFltRefundPaymentList.size() > 0) {
                for (EtFltRefundPayment etFltRefundPayment : etFltRefundPaymentList) {
                    if (etFltRefundPayment.getFltRefundId().equals(record.getId())) {
                        refundPaymentList.add(etFltRefundPayment);
                    }
                }
                // 存在多笔退款记录，倒序排列，取最近一条退款
                Collections.sort(refundPaymentList, new Comparator<EtFltRefundPayment>() {
                    public int compare(EtFltRefundPayment arg0, EtFltRefundPayment arg1) {
                        return arg1.getEtRefundPayment().getPayTime().compareTo(arg0.getEtRefundPayment().getPayTime());
                    }
                });
                if (refundPaymentList != null && refundPaymentList.size() > 0) {
                    EtPayment etPayment = refundPaymentList.get(0).getEtRefundPayment();
                    if (etPayment != null) {
                        if (org.springframework.util.StringUtils.hasText(etPayment.getPayer())) {
                            String refundUser = etPayment.getPayer();
                            record.setRefundUser(refundUser);
                        }
                        if (etPayment.getPayTime() != null) {
                            Date refundTime = etPayment.getPayTime();
                            record.setRefundTime(refundTime);
                        }
                    }
                }
            }

        }

        return etFltRefundList;
    }


    /**
     * 查询退票单列表
     *
     * @return
     */
    public List<EtFltRefund> findRefundByParams(SearchRefundOrderListReq req) {
        List<EtFltRefund> etFltRefundList = etFltRefundMapper.findRefundByParams(req);
        for (EtFltRefund etFltRefund : etFltRefundList) {
            if (etFltRefund.getStatus().equals(DoAuditing.PAID.getValue())) {
                List<EtFltRefundPayment> etFltRefundPaymentList = etFltRefundPaymentMapper.selectList(new QueryWrapper<EtFltRefundPayment>().eq("flt_refund_id", etFltRefund.getId()));
                for (EtFltRefundPayment etFltRefundPayment : etFltRefundPaymentList) {
                    EtPayment etPayment = etPaymentMapper.selectById(etFltRefundPayment.getPaymentId());
                    etFltRefundPayment.setEtRefundPayment(etPayment);
                }
                etFltRefund.setEtFltRefundPaymentList(etFltRefundPaymentList);
            }
            if (StringUtils.isNotEmpty(req.getDepCode()) || StringUtils.isNotEmpty(req.getArrCode())) {
                List<EtPassengerSegment> etPassengerSegmentList = etPassengerSegmentMapper.findByRefundId(etFltRefund.getId());
                etFltRefund.setEtPassengerSegmentList(etPassengerSegmentList);
            }
            if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
                EtSpecialRefundBankInfo etSpecialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(
                        new QueryWrapper<EtSpecialRefundBankInfo>().eq("flt_refund_id", etFltRefund.getId())
                );
                List<EtSpecialRefundImg> etSpecialRefundImg = etSpecialRefundImgMapper.selectList(
                        new QueryWrapper<EtSpecialRefundImg>().eq("flt_refund_id", etFltRefund.getId())
                );
                etFltRefund.setEtSpecialRefundBankInfo(etSpecialRefundBankInfo);
                etFltRefund.setEtSpecialRefundImgList(etSpecialRefundImg);
            }
            List<EtFltRefundAudit> etFltRefundAuditList = etFltRefundAuditMapper.selectList(
                    new QueryWrapper<EtFltRefundAudit>().eq("refund_id", etFltRefund.getId())
            );
            etFltRefund.setFltRefundAudit(etFltRefundAuditList);
        }
        return etFltRefundList;
    }

    /**
     * 根据 ID 查询退票单明细
     *
     * @param id
     * @return
     */
    public EtFltRefund findRefundDetailById(Long id) {
        EtFltRefund etFltRefund = etFltRefundMapper.selectById(id);
        if (etFltRefund == null) {
            return null;
        }
        List<EtFltRefundPaxSeg> etFltRefundPaxSegList = etFltRefundPaxSegMapper.selectList(
                new QueryWrapper<EtFltRefundPaxSeg>().eq("refund_id", etFltRefund.getId())
        );
        List<EtPassengerSegment> etPassengerSegmentList = etPassengerSegmentMapper.selectList(
                new QueryWrapper<EtPassengerSegment>().in("id", etFltRefundPaxSegList.stream().map(EtFltRefundPaxSeg::getPaxSegId).collect(Collectors.toList()))
        );

        List<EtPassenger> etPassengerList = etPassengerMapper.selectList(
                new QueryWrapper<EtPassenger>().in("id", etPassengerSegmentList.stream().map(EtPassengerSegment::getPassengerId).collect(Collectors.toList()))
        );
        List<Customer> customerList = customerMapper.selectList(
                new QueryWrapper<Customer>().in("id", etPassengerList.stream().map(EtPassenger::getCustomerId).collect(Collectors.toList()))
        );
        for (Customer customer : customerList) {
            try {
                customer.setCertificateno(EncoderUtil.decryptDefault(customer.getCertificateno()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        List<EtPnr> etPnrList = etPnrMapper.selectList(
                new QueryWrapper<EtPnr>().in("id", etPassengerList.stream().map(EtPassenger::getPnrId).collect(Collectors.toList()))
        );

        List<EtSegment> etSegmentList = etSegmentMapper.selectList(
                new QueryWrapper<EtSegment>().in("id", etPassengerSegmentList.stream().map(EtPassengerSegment::getSegmentId).collect(Collectors.toList()))
        );

        List<EtTicket> etTicketList = etTicketMapper.selectList(
                new QueryWrapper<EtTicket>().in("id", etPassengerSegmentList.stream().map(EtPassengerSegment::getTicketId).collect(Collectors.toList()))
        );

        List<EtFltRefundAudit> etFltRefundAuditList = etFltRefundAuditMapper.selectList(
                new QueryWrapper<EtFltRefundAudit>().eq("refund_id", etFltRefund.getId()).orderByAsc("audit_time")
        );

        List<EtFltRefundOutsidePayment> etFltRefundOutsidePaymentList = etFltRefundOutsidePaymentMapper.selectList(
                new QueryWrapper<EtFltRefundOutsidePayment>().eq("flt_refund_id", etFltRefund.getId())
        );

        List<EtPayment> etOutsidePaymentList = etPaymentMapper.selectList(
                new QueryWrapper<EtPayment>().in("id", etFltRefundOutsidePaymentList.stream().map(EtFltRefundOutsidePayment::getPaymentId).collect(Collectors.toList()))
        );

        List<EtFltRefundPayment> etFltRefundPaymentList = etFltRefundPaymentMapper.selectList(
                new QueryWrapper<EtFltRefundPayment>().eq("flt_refund_id", etFltRefund.getId())
        );

        if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
            EtSpecialRefundBankInfo etSpecialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(
                    new QueryWrapper<EtSpecialRefundBankInfo>().eq("flt_refund_id", etFltRefund.getId())
            );
            List<EtSpecialRefundImg> etSpecialRefundImg = etSpecialRefundImgMapper.selectList(
                    new QueryWrapper<EtSpecialRefundImg>().eq("flt_refund_id", etFltRefund.getId())
            );
            etFltRefund.setEtSpecialRefundBankInfo(etSpecialRefundBankInfo);
            etFltRefund.setEtSpecialRefundImgList(etSpecialRefundImg);
        }

        List<EtPayment> etRefundPaymentList = null;
        if (etFltRefundPaymentList.size() != 0) {
            etRefundPaymentList = etPaymentMapper.selectList(
                    new QueryWrapper<EtPayment>().in("id", etFltRefundPaymentList.stream().map(EtFltRefundPayment::getPaymentId).collect(Collectors.toList()))
            );
        }

        for (EtPassenger etPassenger : etPassengerList) {
            for (Customer customer : customerList) {
                if (etPassenger.getCustomerId().equals(customer.getId())) {
                    etPassenger.setCustomer(customer);
                }
            }
            for (EtPnr etPnr : etPnrList) {
                if (etPassenger.getPnrId().equals(etPnr.getId())) {
                    etPassenger.setEtPnr(etPnr);
                }
            }
        }

        for (EtPassengerSegment etPassengerSegment : etPassengerSegmentList) {
            for (EtPassenger etPassenger : etPassengerList) {
                if (etPassengerSegment.getPassengerId().equals(etPassenger.getId())) {
                    etPassengerSegment.setEtPassenger(etPassenger);
                }
            }
            for (EtSegment etSegment : etSegmentList) {
                if (etPassengerSegment.getSegmentId().equals(etSegment.getId())) {
                    etPassengerSegment.setEtSegment(etSegment);
                }
            }
            for (EtTicket etTicket : etTicketList) {
                if (etPassengerSegment.getTicketId().equals(etTicket.getId())) {
                    etPassengerSegment.setEtTicket(etTicket);
                }
            }
            if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
                EtSpecialRefundOriginTkt etSpecialRefundOriginTkt = etSpecialRefundOriginTktMapper.selectOne(
                        new QueryWrapper<EtSpecialRefundOriginTkt>().eq("pax_seg_id", etPassengerSegment.getId())
                );
                etPassengerSegment.setSpecialRefundOriginTkt(etSpecialRefundOriginTkt);
            }
        }

        etFltRefund.setEtFltRefundPaxSegList(etFltRefundPaxSegList);
        etFltRefund.setEtPassengerSegmentList(etPassengerSegmentList);
        etFltRefund.setFltRefundAudit(etFltRefundAuditList);
        etFltRefund.setEtOutsidePaymentList(etOutsidePaymentList);
        etFltRefund.setEtRefundPaymentList(etRefundPaymentList);

        return etFltRefund;
    }


    /**
     * 一审通过
     *
     * @param req
     * @return
     */
    @Transactional
    public void firstAuditPass(FirstAuditReq req) {
        EtFltRefund etFltRefund = findRefundDetailById(req.getRefundId());

        if (DoAuditing.FPASS.getValue().equals(etFltRefund.getStatus())
                || DoAuditing.PASS.getValue().equals(etFltRefund.getStatus())) {
            throw new GlobalException(CodeRefund.FIRST_PASS_EXCEPTION, null);
        }

        EtFltRefundAudit etFltRefundAudit = new EtFltRefundAudit();
        etFltRefundAudit.setId(null);
        etFltRefundAudit.setRefundId(req.getRefundId());
        etFltRefundAudit.setAuditTime(new Date());
        etFltRefundAudit.setAction(DoAuditing.FIRSTAUDIT.getValue());
        etFltRefundAudit.setAuditUsername(req.getAuditUserName());
        etFltRefundAudit.setSubmitUsername(null);
        etFltRefundAudit.setNotion(StringUtils.isEmpty(req.getAuditRemark()) ? "一审通过" : req.getAuditRemark());
        etFltRefundAudit.setAuditResult(DoAuditing.FPASS.getValue());

        etFltRefundAuditMapper.insert(etFltRefundAudit);

        if (req.getConstraintReason() == null) {
            req.setConstraintReason(etFltRefund.getConstraintReason());
        }
        etFltRefund.setConstraintReason(req.getConstraintReason());
        if (req.getRefundType() == null) {
            req.setRefundType(etFltRefund.getRefundType());
        }
        etFltRefund.setRefundType(req.getRefundType());
        if (DoAuditing.UNCONSTRAINT.getValue().equals(req.getRefundType())) {
            etFltRefund.setConstraintReason("");
        }

        //实退金额值，合法性判断
        List<Double> list = req.getActualAmountList();
        List<Long> listIndex = req.getAmountIndex();
        for (EtPassengerSegment paxseg : etFltRefund.getEtPassengerSegmentList()) {
            for (int i = 0; i < list.size(); i++) {
                long paxsegId = listIndex.get(i);
                if (paxseg.getId().equals(paxsegId)) {
                    double amount = list.get(i);
                    // 特殊退票，实际退款金额不能大于实售价+税
                    if ("TS".equals(etFltRefund.getOrderSource())) {
                        if (!"FLT_CHANGED".equals(etFltRefund.getRefundType())) {
                            if (amount > paxseg.getNetFare().doubleValue() + paxseg.getAirportTax().doubleValue() + paxseg.getFuelTax().doubleValue()) {
                                throw new GlobalException(CodeRefund.OVER_AMOUNT_EXCEPTION, null);
                            }
                        }
                    }
                }
            }
        }

        // 更新设置退票航段的实退金额
        for (EtFltRefundPaxSeg frps : etFltRefund.getEtFltRefundPaxSegList()) {
            for (int i = 0; i < req.getAmountIndex().size(); i++) {
                if (frps.getPaxSegId().equals(req.getAmountIndex().get(i))) {
                    frps.setActualRefundAmount(BigDecimal.valueOf(req.getActualAmountList().get(i)));
                    etFltRefundPaxSegMapper.updateById(frps);
                    break;
                }
            }
        }

        // 更新退票单状态为【一审通过】
        etFltRefund.setStatus(DoAuditing.FPASS.getValue());
        etFltRefundMapper.updateById(etFltRefund);

        // 特殊退票附件处理
        if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
            dealImg(etFltRefund.getRefundNo(), etFltRefundAudit.getId(), req.getResources(), req.getAuditUserName());
        }
    }


    /**
     * 一审拒绝
     *
     * @param req
     * @return
     */
    @Transactional
    public void firstAuditReject(FirstAuditReq req) {
        EtFltRefund etFltRefund = findRefundDetailById(req.getRefundId());

        if (DoAuditing.FPASS.getValue().equals(etFltRefund.getStatus())
                || DoAuditing.PASS.getValue().equals(etFltRefund.getStatus())) {
            throw new GlobalException(CodeRefund.FIRST_PASS_EXCEPTION, null);
        }

        EtFltRefundAudit etFltRefundAudit = new EtFltRefundAudit();
        etFltRefundAudit.setId(null);
        etFltRefundAudit.setRefundId(req.getRefundId());
        etFltRefundAudit.setAuditTime(new Date());
        etFltRefundAudit.setAction(DoAuditing.FIRSTAUDIT.getValue());
        etFltRefundAudit.setAuditUsername(req.getAuditUserName());
        etFltRefundAudit.setSubmitUsername(null);
        etFltRefundAudit.setNotion(StringUtils.isEmpty(req.getAuditRemark()) ? "一审拒绝" : req.getAuditRemark());
        etFltRefundAudit.setAuditResult(DoAuditing.FREJECT.getValue());  // 一审拒绝

        etFltRefundAuditMapper.insert(etFltRefundAudit);

        if (req.getConstraintReason() == null) {
            req.setConstraintReason(etFltRefund.getConstraintReason());
        }
        etFltRefund.setConstraintReason(req.getConstraintReason());
        if (req.getRefundType() == null) {
            req.setRefundType(etFltRefund.getRefundType());
        }
        etFltRefund.setRefundType(req.getRefundType());
        if (DoAuditing.UNCONSTRAINT.getValue().equals(req.getRefundType())) {
            etFltRefund.setConstraintReason("");
        }

        // 更新退票单状态为【一审拒绝】
        etFltRefund.setStatus(DoAuditing.FREJECT.getValue());
        etFltRefundMapper.updateById(etFltRefund);

        // 特殊退票发短信
        if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
            dealImg(etFltRefund.getRefundNo(), etFltRefundAudit.getId(), req.getResources(), req.getAuditUserName());
            // 特殊退票-一审决绝发短信
            etFltRefund.setRemark(etFltRefundAudit.getNotion());
            smsServiceUtil.sendMessage4SpecialRefund(etFltRefund, DoAuditing.FREJECT);
        }

    }


    /**
     * 二审通过
     *
     * @param req
     * @return
     */
    @Transactional
    public void     secondAuditPass(SecondAuditReq req) {
        EtFltRefund etFltRefund = findRefundDetailById(req.getRefundId());

        EtFltRefundAudit etFltRefundAudit = new EtFltRefundAudit();
        etFltRefundAudit.setId(null);
        etFltRefundAudit.setRefundId(req.getRefundId());
        etFltRefundAudit.setAuditTime(new Date());
        etFltRefundAudit.setAction(DoAuditing.SECONDAUDIT.getValue());
        etFltRefundAudit.setAuditUsername(req.getAuditUserName());
        etFltRefundAudit.setSubmitUsername(null);
        etFltRefundAudit.setNotion(StringUtils.isEmpty(req.getAuditRemark()) ? "二审通过" : req.getAuditRemark());
        etFltRefundAudit.setAuditResult(DoAuditing.PASS.getValue());

        etFltRefundAuditMapper.insert(etFltRefundAudit);

        //保存退票实际金额
        double amount2SendMsg = 0.00;
        //实退金额值，合法性判断
        List<Double> list = req.getActualAmountList();
        List<Long> listIndex = req.getAmountIndex();
        for (EtPassengerSegment paxseg : etFltRefund.getEtPassengerSegmentList()) {
            for (int i = 0; i < list.size(); i++) {
                long paxsegId = listIndex.get(i);
                if (paxseg.getId().equals(paxsegId)) {
                    double amount = list.get(i);
                    amount2SendMsg += Double.parseDouble(new DecimalFormat("#.00").format(amount));
                    // 特殊退票，实际退款金额不能大于实售价+税
                    if ("TS".equals(etFltRefund.getOrderSource())) {
                        if (!"FLT_CHANGED".equals(etFltRefund.getRefundType())) {
                            if (amount > paxseg.getNetFare().doubleValue() + paxseg.getAirportTax().doubleValue() + paxseg.getFuelTax().doubleValue()) {
                                throw new GlobalException(CodeRefund.OVER_AMOUNT_EXCEPTION, null);
                            }
                        }
                    }
                }
            }
        }

        // 更新设置退票航段的实退金额
        for (EtFltRefundPaxSeg frps : etFltRefund.getEtFltRefundPaxSegList()) {
            for (int i = 0; i < req.getAmountIndex().size(); i++) {
                if (frps.getPaxSegId().equals(req.getAmountIndex().get(i))) {
                    frps.setActualRefundAmount(BigDecimal.valueOf(req.getActualAmountList().get(i)));
                    etFltRefundPaxSegMapper.updateById(frps);
                    break;
                }
            }
        }

        // 更新退票单状态为【二审通过】
        etFltRefund.setStatus(DoAuditing.PASS.getValue());
        etFltRefundMapper.updateById(etFltRefund);

        // 国际票发短息
        if ("GJ".equals(etFltRefund.getOrderSource())) {
            smsServiceUtil.sendMessage4AuditRefundForGJ(etFltRefund, "PASS", amount2SendMsg);
        }

        // 特殊退票附件处理
        if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
            dealImg(etFltRefund.getRefundNo(), etFltRefundAudit.getId(), req.getResources(), req.getAuditUserName());
        }

        // 生成退款支付流水号
        this.generatorPaymentNo(etFltRefund.getRefundNo());
    }

    /**
     * 二审拒绝
     *
     * @param req
     * @return
     */
    @Transactional
    public void secondAuditReject(SecondAuditReq req) {
        EtFltRefund etFltRefund = findRefundDetailById(req.getRefundId());

        EtFltRefundAudit etFltRefundAudit = new EtFltRefundAudit();
        etFltRefundAudit.setId(null);
        etFltRefundAudit.setRefundId(req.getRefundId());
        etFltRefundAudit.setAuditTime(new Date());
        etFltRefundAudit.setAction(DoAuditing.SECONDAUDIT.getValue());
        etFltRefundAudit.setAuditUsername(req.getAuditUserName());
        etFltRefundAudit.setSubmitUsername(null);
        etFltRefundAudit.setNotion(StringUtils.isEmpty(req.getAuditRemark()) ? "二审拒绝" : req.getAuditRemark());
        etFltRefundAudit.setAuditResult(DoAuditing.REJECT.getValue());  // 二审拒绝

        etFltRefundAuditMapper.insert(etFltRefundAudit);

        // 更新退票单状态为【二审拒绝】
        etFltRefund.setStatus(DoAuditing.REJECT.getValue());
        etFltRefundMapper.updateById(etFltRefund);

        // 特殊退票附件处理
        if ("TS".equals(etFltRefund.getOrderSource()) || "TSGJ".equals(etFltRefund.getOrderSource())) {
            dealImg(etFltRefund.getRefundNo(), etFltRefundAudit.getId(), req.getResources(), req.getAuditUserName());
        }
    }


    /**
     * 线下退款
     *
     * @param req
     * @return
     */
    @Transactional
    public void refundAmountOffline(RefundAmountReq req) {
        EtFltRefund etFltRefund = findRefundDetailById(req.getRefundId());
        // TODO 锁表
        etFltRefund.setPayMode(DoAuditing.OFFLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(DoAuditing.PAID.getValue());

        // 海外站退款给旅客发送邮件
        if ("HWZ".equals(etFltRefund.getOrderSource())) {
            BigDecimal refundAmount = new BigDecimal(0);
            for (EtFltRefundPaxSeg fltRefundPaxSeg : etFltRefund.getEtFltRefundPaxSegList()) {
                refundAmount = refundAmount.add(fltRefundPaxSeg.getActualRefundAmount());
            }
            emailServiceUtil.sendEmail4HWZ(etFltRefund.getUserName(), refundAmount.toString());
        }

        etFltRefundMapper.updateById(etFltRefund);
    }

    /**
     * 在线退款
     *
     * @param req
     * @return
     */
//    @Transactional
    public void refundAmountOnline(RefundAmountReq req) {
        String paymentNo = "";
        String tradeNoString = "";
        try {
            EtFltRefund refund = this.findRefundDetailById(req.getRefundId());
            log.info("RefundAmountOnline:" + refund.getRefundNo() + refund.getStatus());
            if (!"PASS".equals(refund.getStatus())) {
                throw new GlobalException(CodeRefund.NOT_PASS_EXCEPTION, null);
            }

            List<EtPayment> payments = refund.getEtRefundPaymentList();

            if (payments != null && payments.size() > 0) {
                if (refund.getOrderSource().equals("TS") || refund.getOrderSource().equals("TSGJ")) {
                    // 允许多次退款，判断与上次退款发起时间的间隔不能小于 72h
                    Collections.sort(payments, new Comparator<EtPayment>() {
                        public int compare(EtPayment arg0, EtPayment arg1) {
                            return arg1.getPayTime().compareTo(arg0.getPayTime());
                        }
                    });

                    EtPayment payment = payments.get(0);

                    if (DateUtil.timeTotalMinutes(new Date(), payment.getPayTime()) < 72 * 60) {
                        throw new SpecialRefundException("此次退款据上次操作在线退款时间不满足72小时间隔限制，请稍后重试");
                    } else {
                        // 查询易生退款结果
                        try {
                            JSONObject jsonObject = this.queryEasycardDFRefundResult(payment.getPaymentNo());
                            JSONObject response = jsonObject.getJSONObject("trade_acc_dsfpay_query_response");
                            String resResult = JSONObject.toJSONString(response);
                            String tradeStatus = JSON.parseObject(resResult).getString("trade_status");
                            String code = JSON.parseObject(resResult).getString("code");
                            if ("00".equals(code) && "SUCCESS".equals(tradeStatus)) {
                                throw new SpecialRefundException("此订单已成功进行过一次退款，退款流水：" + payment.getPaymentNo());
                            }
                        } catch (Exception e) {
                            throw new SpecialRefundException("查询易生退款结果异常，请稍后重试");
                        }
                    }

                } else {
                    throw new GlobalException(CodeRefund.REFUNDED_EXCEPTION, null);
                }
            }

            //支付来源
            String source = "";

            List<EtPayment> dtoPaymentList = refund.getEtOutsidePaymentList();
            if (dtoPaymentList == null || dtoPaymentList.size() != 1) {
                throw new GlobalException(CodeRefund.NO_PAID_RECORD_EXCEPTION, null);
            }

            //支付类型
            PayType payType = null;
            Date payTime = new Date();
            for (EtPayment payment : refund.getEtOutsidePaymentList()) {
                if (PayStatus.PAID.toString().equals(payment.getPayStatus())) {
                    paymentNo = payment.getPaymentNo();
                    tradeNoString = payment.getPaymentNo();
                    //获取支付类型
                    payType = PayType.valueOfAlias(payment.getPayType());
                    source = payment.getSource();
                    payTime = payment.getPayTime();
                    break;
                }
            }

            if ("".equals(paymentNo)) {
                throw new GlobalException(CodeRefund.NO_PAID_RECORD_EXCEPTION, null);
            }

            /**
             * 组装参数
             */
            EtPayment dtoPayment = new EtPayment();
            dtoPayment.setPaymentNo(paymentNo);
            dtoPayment.setRefundId(refund.getId());
            dtoPayment.setPayer(req.getLoginId());
            dtoPayment.setPayType(payType.toString());
            dtoPayment.setAction(PayAction.REFUND.name());
            dtoPayment.setPayTime(payTime);
            //支付来源,主要是渠道来源
            dtoPayment.setSource(source);

            log.info("refund money online.paymentNo:" + paymentNo + ",refundId:" + req.getRefundId() + ",payType:" + payType.toString() + ",paymentSource:" + source + ",oper:" + req.getLoginId());
            log.info("Begin to refund | paymentNo = " + paymentNo);
            Map payResult = payService.onlineRefund(refund, dtoPayment, payType);
            log.info("mars refund end | returnResult = " + payResult.get("returnResult"));
            PaymentLogStatus returnResult = PaymentLogStatus.FAILED;
            if (payResult.get("returnResult") != null) {
                returnResult = (PaymentLogStatus) payResult.get("returnResult");
            }

            if (PaymentLogStatus.SUCCESS != returnResult) {
                throw new GlobalException(CodeRefund.PAY_HANDLE_EXCEPTION, null);
            } else {
                if (refund.getOrderSource().equals("TS") || refund.getOrderSource().equals("TSGJ")) {
                    // 特殊退票 - "退款成功"发短信
                    smsServiceUtil.sendMessage4SpecialRefund(refund, DoAuditing.PAID);
                }
            }
        } catch (Exception e) {
            log.error("refund amount online error.original paymentNo:" + paymentNo + ", refundId:" + req.getRefundId() + ", err:" + e.getMessage(), e);
            throw new SpecialRefundException(e.getMessage());
        }
    }

    /**
     * 定时任务查询并处理未退款成功（一天内）的支付宝线上退款
     */
    public void alipayRefundQuery() {
        DateTime startTime = new DateTime();
        startTime = startTime.minusDays(1);
        DateTime endTime = new DateTime();
        endTime = endTime.minusSeconds(10);
        List<EtPayment> pendEtPaymentList = etPaymentMapper.selectList(
                new QueryWrapper<EtPayment>()
                        .eq("pay_type", "ALIP")
                        .eq("action", "REFUND")
                        .eq("pay_status", "PEND")
                        .apply("UNIX_TIMESTAMP(pay_time) >= UNIX_TIMESTAMP('" + startTime.toString(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")) + "')")
                        .apply("UNIX_TIMESTAMP(pay_time) <= UNIX_TIMESTAMP('" + endTime.toString(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")) + "')")
        );

        for (EtPayment etPayment : pendEtPaymentList) {
            log.info("alipay wait refund paymentNo: {}", etPayment.getPaymentNo());
            EtFltRefundPayment etFltRefundPayment = etFltRefundPaymentMapper.selectOne(new QueryWrapper<EtFltRefundPayment>().eq("payment_id", etPayment.getId()));
            EtFltRefund etFltRefund = this.findRefundDetailById(etFltRefundPayment.getFltRefundId());
            EtAlipayPayment etAlipayPayment = new EtAlipayPayment();
            for (EtPayment payment : etFltRefund.getEtOutsidePaymentList()) {
                etAlipayPayment = etAlipayPaymentMapper.findById(payment.getId());
                break;
            }
            try {
                this.processAlipayRefundedBizz(etFltRefund, etPayment, etAlipayPayment);
            } catch (Exception e) {
                log.error("processAlipayRefundedBizz error, refundNo: {}, refundPaymentNo: {}", etFltRefund.getRefundNo(), etPayment.getPaymentNo());
                e.printStackTrace();
            }
        }

    }


    /**
     * 特殊退票退款 -- 易生代付退款回调
     *
     * @param easycardJSONResponse
     * @param manualQuery
     * @param outTradeNo
     */
    public boolean refundCallbackEasycardDF(String easycardJSONResponse, boolean manualQuery, String outTradeNo) {
        try {
            // 商户配置
            EtEasyCardDFConfig config = etEasyCardDFConfigMapper.selectOne(new QueryWrapper<EtEasyCardDFConfig>().eq("purpose", "TS"));
            com.alibaba.fastjson.JSONObject jsonObject;

            EtPayment etPayment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", outTradeNo));

            EtFltRefundPayment etFltRefundPayment = etFltRefundPaymentMapper.selectOne(new QueryWrapper<EtFltRefundPayment>().eq("payment_id", etPayment.getId()));
            EtFltRefund etFltRefund = this.findRefundDetailById(etFltRefundPayment.getFltRefundId());

            if (manualQuery) {
                log.info("easycardDF query refund result, out_trade_no: " + outTradeNo);
                // 手工查询退款结果
                jsonObject = this.queryEasycardDFRefundResult(outTradeNo);
            } else {
                // 易生自动回调
                if (!this.rsaVerifySignForCallback(new StringBuilder(easycardJSONResponse), config.getEasypayPublicKey())) {
                    throw new RuntimeException("返回签名验证失败！");
                }
                return handleEasycardDFCallback(easycardJSONResponse, etFltRefund, etPayment);
            }

            com.alibaba.fastjson.JSONObject resultObject = jsonObject.getJSONObject("trade_acc_dsfpay_query_response");
            //错误代码	说明
            //00	业务受理成功
            //20	业务异常
            //40	参数检查失败
            //41	partner错误
            //42	签名错误
            //99	系统异常
            String code = resultObject.getString("code");
            if ("00".equals(code)) {
                // 交易状态	说明
                //INIT	初始化
                //SUCCESS	成功
                //FAIL	失败
                //UNKNOWN	交易未知
                //UNSENT	订单号不存在
                String trade_status = resultObject.getString("trade_status");
                if ("SUCCESS".equals(trade_status)) {
                    // 交易成功
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put(PayStatus.class.getName(), PayStatus.PAID);
                    params.put("paymentNo", etPayment.getPaymentNo());
                    params.put(EtFltRefund.class.getName(), etFltRefund);
                    try {
                        this.processBizz(params);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    return true;
                } else if ("FAIL".equals(trade_status)) {
                    // 进行了退款交易，但处理失败。
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put(PayStatus.class.getName(), PayStatus.FAIL);
                    params.put("paymentNo", etPayment.getPaymentNo());
                    params.put(EtFltRefund.class.getName(), etFltRefund);
                    try {
                        this.processBizz(params);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    return true;
                } else {
                    log.error(outTradeNo + " easycardDF Refund Call Back : trade_status is unexpect: " + easycardJSONResponse);
                }
            } else {
                log.error(outTradeNo + " easycardDF Refund Call Back : trade_status is unexpect: " + easycardJSONResponse);
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    private void processAlipayRefundedBizz(EtFltRefund etFltRefund, EtPayment etPayment, EtAlipayPayment etAlipayPayment) throws Exception {
        // 支付宝配置
        AlipayConfig.Config config = alipayConfig.getSource().get("GJWithInsurance");
        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",
                config.getAppId(),
                config.getAppPrivateKey(),
                "json",
                "GBK",
                config.getAliPublicKey(),
                "RSA2");
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("trade_no", etAlipayPayment.getDealId());
        bizContent.put("out_request_no", etPayment.getPaymentNo());

        //// 返回参数选项，按需传入
        //JSONArray queryOptions = new JSONArray();
        //queryOptions.add("refund_detail_item_list");
        //bizContent.put("query_options", queryOptions);

        request.setBizContent(bizContent.toString());
        AlipayTradeFastpayRefundQueryResponse response = alipayClient.execute(request);
        if (response.isSuccess()) {
            if ("REFUND_SUCCESS".equals(response.getRefundStatus())) {
                Map<String, Object> params = new HashMap<String, Object>();
                params.put(PayStatus.class.getName(), PayStatus.PAID);
                params.put("paymentNo", etPayment.getPaymentNo());
                params.put(EtFltRefund.class.getName(), etFltRefund);
                this.processBizz(params);
            } else {
                log.info("AlipayTradeFastpayRefundQueryRequest result not REFUND_SUCCESS, refundPaymentNo: {}", etPayment.getPaymentNo());
            }
        } else {
            log.error("调用失败");
        }
    }

    private void processBizz(Map<String, Object> params) throws Exception {
        String paymentNo = (String) params.get("paymentNo");
        PayStatus payStatus = (PayStatus) params.get(PayStatus.class.getName());
        EtFltRefund etFltRefund = (EtFltRefund) params.get(EtFltRefund.class.getName());
        //保存支付记录到数据库
        EtPayment payment = etPaymentMapper.selectOne(new QueryWrapper<EtPayment>().eq("payment_no", paymentNo));
        if (null == payment) {
            log.info("Pay Call Back: Payment is not found. paymentNo is " + paymentNo);
            throw new Exception("Pay Call Back : Payment is not found. paymentNo is " + paymentNo);
        } else if (PayStatus.PAID.toString().equals(payment.getPayStatus()) || PayStatus.FAIL.toString().equals(payment.getPayStatus())) {
            log.info("Pay Call Back: Payment status has Been PAID. paymentNo is " + paymentNo);
            return;
        }
        log.info("refund no:" + etFltRefund.getRefundNo() + etFltRefund.getStatus());

        // 更新退票单状态
        this.updateRefundStatus(etFltRefund, payStatus.toString());
        if ("PAID".equals(payStatus.toString())) {
            if ("HWZ".equals(etFltRefund.getOrderSource())) {
                // 海外站退款成功后，给旅客发送邮件
                BigDecimal refundAmount = new BigDecimal(0);
                for (EtFltRefundPaxSeg fltRefundPaxSeg : etFltRefund.getEtFltRefundPaxSegList()) {
                    refundAmount = refundAmount.add(fltRefundPaxSeg.getActualRefundAmount());
                }
                emailServiceUtil.sendEmail4HWZ(etFltRefund.getUserName(), refundAmount.toString());
            } else if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                smsServiceUtil.sendToRefundSmsQueue(etFltRefund);
            }
        }

        if ("PAID".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus()) || "PENDICING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.PAID.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else if ("FAIL".equals(payStatus.toString())) {
            if ("PEND".equals(payment.getPayStatus()) || "REFUNDING".equals(payment.getPayStatus())) {
                payment.setPayStatus(PayStatus.FAIL.toString());
                // 更新退款支付记录状态
                etPaymentMapper.updateById(payment);
            }
        } else {
            throw new Exception("Pay Call Back: Success payment Status is fail by this process.");
        }

    }

    private void updateRefundStatus(EtFltRefund etFltRefund, String status) {
        etFltRefund.setPayMode(DoAuditing.ONLINE.getValue());
        etFltRefund.setPayTime(new Date());
        etFltRefund.setStatus(status);
        etFltRefundMapper.updateById(etFltRefund);
    }


    private void dealImg(String refundNo, Long refundAuditId, MultipartFile[] resources, String loginUserName) {
        String bucketName = "special-refund";
        EtSpecialRefundImg refundImg = new EtSpecialRefundImg();
        if (resources != null && resources.length > 0) {
            for (MultipartFile file : resources) {
                HashMap<String, Object> res = new HashMap<>();
                try {
                    String originalFileName = file.getOriginalFilename();
                    String fileType = originalFileName.substring(Objects.requireNonNull(originalFileName).lastIndexOf(".") + 1);
                    if (!"png,jpg,jpeg,bmp,gif".contains(fileType)) {
                        throw new SpecialRefundException("请上传jpg、jpeg、bmp、gif、png格式文件！");
                    }
                    String preName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                    if (org.apache.commons.lang3.StringUtils.isBlank(preName)) {
                        throw new SpecialRefundException("文件名不能为空。");
                    }
                    OssFileInfo ossFileInfo = fileService.ossUpload(file, bucketName);

                    refundImg.setFltRefundId(0L);
                    refundImg.setImgUrl(ossFileInfo.getFilePath());
                    refundImg.setImgOriginName(originalFileName);
                    refundImg.setStatus(SpecialRefundImgStatus.ACTIVE.name());
                    refundImg.setCreateTime(new Date());
                    refundImg.setOperator(loginUserName);
                    refundImg.setRefundNo(refundNo);
                    refundImg.setSource(SpecialRefundImgSource.AUDITOR.name());
                    refundImg.setRefundAuditId(refundAuditId);
                    //save
                    etSpecialRefundImgMapper.insert(refundImg);
                } catch (Exception e) {
                    log.error("附件上传处理异常：" + e);
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 查询易生代付退款查询接口的返回结果
     *
     * @param refundPaymentNo
     * @return
     */
    public JSONObject queryEasycardDFRefundResult(String refundPaymentNo) {
        // {"trade_acc_dsfpay_query_response":{"code":"00","msg":"订单不存在，如有疑问，请联系客服查询","merchant_id":"100000000000124",
        // "out_trade_no":"20200804091927328977","trade_status":"UNSENT"},
        // "sign":"qjNVx5/InhaeO66+lV2D+JSJHAJi9Kj4nwSmvDwj3+v/+OV1hh+xR4eEFBPS5bWlSodSPjrL4OKTeiZSQy16azN7BpQIJ1Pyjz+wxP+gdSVu2CkPxHZPGVdvp5zsXVWHo
        // /qj3tzDjvcpwHrw+NFrpQakSD4agO1HqSxJHnM4+bM="}
        String easycardDFQueryResult = "";
        try {
            // 商户配置
            EtEasyCardDFConfig config = etEasyCardDFConfigMapper.selectOne(new QueryWrapper<EtEasyCardDFConfig>().eq("purpose", "TS"));
            com.alibaba.fastjson.JSONObject sParaTemp = new com.alibaba.fastjson.JSONObject();

            sParaTemp.put("merchant_id", config.getMerchantId());
            sParaTemp.put("out_trade_no", refundPaymentNo);
            String biz_content = sParaTemp.toString();

            //加密类型，默认RSA
            String sign_type = "RSA";
            //编码类型
            String charset = "UTF-8";
            //根据请求参数生成的加密串
            String sign = AlipaySignature.rsaSign(biz_content, config.getMerchantPrivateKey(), charset);
            log.info("计算签名数据为：" + sign + "\n");
            Map<String, String> reqMap = new HashMap<String, String>(6);
            reqMap.put("biz_content", biz_content);
            //接口文档中的方法名
            String service = "trade.acc.dsfpay.query";
            reqMap.put("service", service);
            reqMap.put("partner", config.getPartnerId());
            reqMap.put("sign_type", sign_type);
            reqMap.put("charset", charset);
            reqMap.put("sign", sign);

            String resultString = "";

            StringBuilder resultStrBuilder = new StringBuilder();
            int ret = HttpConnectUtils.sendRequest(config.getPayUrl(), "UTF-8", reqMap, 30000, 60000, "POST", resultStrBuilder, null);
            //易生公钥验证返回签名
            if (!this.rsaVerifySign(resultStrBuilder, config.getEasypayPublicKey())) {
                throw new RuntimeException("返回签名验证失败！");
            }
            log.info("\n 易生代付退款结果查询：请求地址为：" + config.getPayUrl() +
                    "\n 请求结果为：" + ret +
                    "\n 请求参数为：" + reqMap.toString() +
                    "\n 返回内容为：" + resultStrBuilder.toString() + "\n");
            resultString = resultStrBuilder.toString();
            easycardDFQueryResult = resultString;
        } catch (Exception e1) {
            log.error("TS easycardDF refund query fail. err:" + e1.getMessage(), e1);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(easycardDFQueryResult);
        return jsonObject;
    }

    /**
     * 易生代付退款回调处理
     *
     * @param easycardJSONResponse
     * @return
     */
    private boolean handleEasycardDFCallback(String easycardJSONResponse, EtFltRefund etFltRefund, EtPayment etPayment) {
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(easycardJSONResponse);
        com.alibaba.fastjson.JSONObject resultObject = jsonObject.getJSONObject("biz_content");
        String outTradeNo = resultObject.getString("out_trade_no");
        String trade_status = resultObject.getString("trade_status");
        if ("SUCCESS".equals(trade_status)) {
            // 交易成功
            Map<String, Object> params = new HashMap<String, Object>();
            params.put(PayStatus.class.getName(), PayStatus.PAID);
            params.put("paymentNo", etPayment.getPaymentNo());
            params.put(EtFltRefund.class.getName(), etFltRefund);
            try {
                this.processBizz(params);
            } catch (Exception e) {
                e.printStackTrace();
            }

            return true;
        } else if ("FAIL".equals(trade_status)) {
            // 进行了退款交易，但处理失败。
            Map<String, Object> params = new HashMap<String, Object>();
            params.put(PayStatus.class.getName(), PayStatus.FAIL);
            params.put("paymentNo", etPayment.getPaymentNo());
            params.put(EtFltRefund.class.getName(), etFltRefund);
            try {
                this.processBizz(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        } else {
            log.error(outTradeNo + " easycardDF Refund auto Call Back : trade_status is unexpect: " + easycardJSONResponse);
        }
        log.info(outTradeNo + " easycardDF Refund auto Call Back : trade_status is " + trade_status + ": " + easycardJSONResponse);
        return true;
    }

    /**
     * 查询代付结果报文，验证签名
     *
     * @param resultStrBuilder
     * @param easypay_pub_key
     * @throws Exception
     */
    private boolean rsaVerifySign(StringBuilder resultStrBuilder, String easypay_pub_key) throws Exception {
        //同步返回签名，需要对字符串进行截取后，再验证签名
        String msg = resultStrBuilder.toString();
        String returnString = org.apache.commons.lang.StringUtils.substringBetween(msg, "response\":", ",\"sign\"");
        String returnSign = org.apache.commons.lang.StringUtils.substringBetween(msg, ",\"sign\":\"", "\"}");
        boolean isTrue = AlipaySignature.rsaCheckContent(returnString, returnSign, easypay_pub_key, "UTF-8");
        System.out.println("验证返回签名是否正确：" + isTrue);
        return isTrue;
    }


    /**
     * 回调通知报文，验证签名
     *
     * @param resultStrBuilder
     * @param easypay_pub_key
     * @throws Exception
     */
    private boolean rsaVerifySignForCallback(StringBuilder resultStrBuilder, String easypay_pub_key) throws Exception {
        log.info("rsaVerifySignForCallback: " + resultStrBuilder.toString());
        //同步返回签名，需要对字符串进行截取后，再验证签名
        String msg = resultStrBuilder.toString();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(msg);
        String returnString = jsonObject.getString("biz_content");
        String returnSign = jsonObject.getString("sign");
        boolean isTrue = AlipaySignature.rsaCheckContent(returnString, returnSign, easypay_pub_key, "UTF-8");
        System.out.println("验证返回签名是否正确：" + isTrue);
        return isTrue;
    }

    /**
     * 生成退款支付流水号
     * 维护一张支付流水号生成表，点击二审通过时，判断支付流水号生成表中是否有该笔退票单号对应的待使用的支付流水号，没有插入一条待使用的记录，如果有就更新为本次新生成的支付流水号（二次退款的情况）。
     */
    private void generatorPaymentNo(String refundNo) {
        EtPaymentNoGenerator exist = etPaymentNoGeneratorMapper.selectOne(new QueryWrapper<EtPaymentNoGenerator>()
                .eq("refund_no", refundNo)
                .eq("payment_no_status", "WAIT_USE")
                .last("FOR UPDATE")
        );
        long seqNo = etPaymentMapper.generatePaymentNoSeq();
        String paymentNo = PaymentNoGenerator.generate4TS(seqNo);
        if (exist == null) {
            EtPaymentNoGenerator etPaymentNoGenerator = new EtPaymentNoGenerator();
            etPaymentNoGenerator.setPaymentNo(paymentNo);
            etPaymentNoGenerator.setRefundNo(refundNo);
            etPaymentNoGenerator.setCreateTime(new Date());
            etPaymentNoGenerator.setPaymentNoStatus("WAIT_USE");
            etPaymentNoGeneratorMapper.insert(etPaymentNoGenerator);
        } else {
            exist.setPaymentNo(paymentNo);
            exist.setUpdateTime(new Date());
            etPaymentNoGeneratorMapper.updateById(exist);
        }
    }


}
