package net.jdair.specialrefund.bizz.helper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.util.QDateTime;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.service.EtRefundGjImgService;
import net.jdair.specialrefund.bizz.utils.*;
import net.jdair.specialrefund.bizz.vo.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;


@Component
@Slf4j
public class FlightRefundWithoutOrderServiceHelper {

    @Autowired
    private EtOrderMapper etOrderMapper;
    @Autowired
    private EtFltRefundMapper etFltRefundMapper;
    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;
    @Autowired
    private EtPaymentMapper etPaymentMapper;
    @Autowired
    private EtFltRefundOutsidePaymentMapper etFltRefundOutsidePaymentMapper;
    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private EtPnrMapper etPnrMapper;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    @Autowired
    private EtProductMapper etProductMapper;

    @Autowired
    private EtSegmentMapper etSegmentMapper;

    @Autowired
    private EtPassengerSegmentMapper etPassengerSegmentMapper;

    @Autowired
    private EtSpecialRefundOriginTktMapper etSpecialRefundOriginTktMapper;

    @Autowired
    private EtFltRefundPaxSegMapper etFltRefundPaxSegMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtAlipayPaymentMapper etAlipayPaymentMapper;

    @Autowired
    private EtWXpayPaymentMapper etWXpayPaymentMapper;

    @Autowired
    private EtTicketAlltaxGjMapper etTicketAlltaxGjMapper;

    @Autowired
    private EtRefundGjImgService etRefundGjImgService;



    /**
     * 生成特殊退票退单
     *
     * @param dtoFltRefund
     * @param dtoPayment
     * @param dtoSegmentList
     * @param dtoTicketList
     * @param dtoPassengerList
     * @param ticketPrices
     * @param taxPrices
     * @param dtoSpecialRefundBankInfo
     * @param dtoSpecialRefundImgList
     * @return
     */
    @Transactional
    public String createSpecialFltRefund(DTOFltRefund dtoFltRefund, DTOPayment dtoPayment, List<DTOSegment> dtoSegmentList, List<DTOTicket> dtoTicketList,
                                         List<DTOPassenger> dtoPassengerList,
                                         Map<String, String> currencyMap, Map<String, String> originCurrencyMap,
                                         List<BigDecimal> ticketPrices, List<BigDecimal> taxPrices, DTOSpecialRefundBankInfo dtoSpecialRefundBankInfo, List<DTOSpecialRefundImg> dtoSpecialRefundImgList) {

        try {
            EtFltRefund fltRefund = new EtFltRefund();
            fltRefund.setOrderId(2L);
            fltRefund.setRefundNo(FlightRefundNumberGenerator.generate());
            fltRefund.setRefundType(dtoFltRefund.getRefundType());//自愿、非自愿
            fltRefund.setConstraintReason(dtoFltRefund.getConstraintReason());//非自愿原因
            fltRefund.setCreateTime(dtoFltRefund.getRefundTime());
            fltRefund.setStatus("NEW");//退票单初始状态
            fltRefund.setUserName(dtoFltRefund.getUserName());
            fltRefund.setAirlineCode("JD");
            fltRefund.setOrderSource(dtoFltRefund.getOrderSource());//订单来源
            fltRefund.setReserveRemark(dtoFltRefund.getReserveRemark());
            fltRefund.setRemark(dtoFltRefund.getRemark());
            fltRefund.setFictitious(dtoFltRefund.getFictitious());  //用于记录退单类型

            // 1.保存 et_flt_refund 表
            long refundId = etFltRefundMapper.insert(fltRefund);

            log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 1:" + fltRefund.getRefundNo());

            //审核记录
            EtFltRefundAudit audit = new EtFltRefundAudit();
            audit.setSubmitTime(new Date());
            audit.setAuditTime(new Date());
            audit.setRefundId(fltRefund.getId());
            audit.setSubmitUsername(dtoFltRefund.getRefundUser());
            audit.setNotion(dtoFltRefund.getRemark());
            audit.setAction("FIRSTAUDIT");
            // 2.保存 et_flt_refund_audit 表
            etFltRefundAuditMapper.insert(audit);

            //外部支付记录
            EtPayment outPayment = new EtPayment();
            outPayment.setPaymentNo(FlightRefundNumberGenerator.generate());//退款流水号
            outPayment.setAmount(dtoPayment.getAmount());
            outPayment.setPayType(dtoPayment.getPayType().toString());
            outPayment.setPayStatus(PayStatus.PAID.toString());
            outPayment.setSource("TS");
            outPayment.setPayTime(dtoPayment.getPayTime());
            outPayment.setAction(PayAction.PAY.toString());
            outPayment.setCurrency("RMB");

            // 3.保存 et_payment
            etPaymentMapper.insert(outPayment);

            log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 2:" + fltRefund.getRefundNo());


            List<EtPassenger> passengerList = new ArrayList<EtPassenger>();
            for (DTOPassenger dtoPassenger : dtoPassengerList) {
                Long customerId = null;
                Customer customer = null;
                Customer ce = customerMapper.selectOne(new QueryWrapper<Customer>()
                        .eq("name", dtoPassenger.getName())
                        .eq("certificateno", EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo()))
                        .eq("certificatetype", dtoPassenger.getCertificateType())
                );
                if (ce == null) {
                    customer = new Customer();
                    customer.setName(dtoPassenger.getName());
                    customer.setCertificatetype(dtoPassenger.getCertificateType());
                    try {
                        customer.setCertificateno(EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo()));
                        customer.setEncrypted("1");
                    } catch (Exception e) {
                        customer.setCertificateno(dtoPassenger.getCertificateNo());
                        log.error("EncoderUtil.encryptDefault error:" + e.getMessage());
                    }
                    customer.setGender(Gender.MALE.toString());
                    customerId = Long.valueOf(customerMapper.insert(customer));
                } else {
                    customer = ce;
                    customerId = ce.getId();
                }

                EtPassenger passenger = new EtPassenger();
                passenger.setPassengerType(dtoPassenger.getPassengerType().getAlias());

                // 有编码的时候，保存关联编码
                if (dtoPassenger.getPnrValue() != null && StringUtils.hasText(dtoPassenger.getPnrValue().getPnrNo())) {
                    EtPnr pnr = new EtPnr();
                    pnr.setPnrNo(dtoPassenger.getPnrValue().getPnrNo());
                    etPnrMapper.insert(pnr);
                    passenger.setPnrId(pnr.getId());
                }
                passenger.setCustomerId(customer.getId());
                long passengerId = etPassengerMapper.insert(passenger);
                passengerList.add(passenger);
                log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 3:" + fltRefund.getRefundNo());
            }

            //把外部支付记录关联到退单
            EtFltRefundOutsidePayment refundPayment = new EtFltRefundOutsidePayment();

            refundPayment.setAmount(BigDecimal.ZERO);
            refundPayment.setFltRefundId(fltRefund.getId());
            if (outPayment != null) {
                refundPayment.setPaymentId(outPayment.getId());
            }

            // fltRefund.setFltRefundOutSidePayments(fltRefundOutSidePayments);//退票单关联外部支付记录
            etFltRefundOutsidePaymentMapper.insert(refundPayment);


            List<EtTicket> ticketList = new ArrayList<EtTicket>();
            //提取票状态，后续考虑去掉 @TODO
            Map<String, DETRTKTResult> ibeTicketMsg = new HashMap<String, DETRTKTResult>();
            for (int i = 0; i < dtoTicketList.size(); i++) {
                DTOTicket dtoTicket = dtoTicketList.get(i);
                EtTicket t = new EtTicket();
                EtTicket ticket = etTicketMapper.selectOne(new QueryWrapper<EtTicket>()
                        .eq("iss_code", dtoTicket.getIssCode())
                        .eq("ticket_no", dtoTicket.getTicketNo()));
                if (ticket == null) {
                    String passengerName = dtoPassengerList.get(i).getName();
                    Date issueDate = getIssueDateFromDETRHistory(dtoTicket.getIssCode() + "-" + dtoTicket.getTicketNo(), passengerName);
                    t.setIssCode(dtoTicket.getIssCode());
                    t.setTicketNo(dtoTicket.getTicketNo());
                    t.setBspType("1");
                    t.setBookTime(issueDate);
                    t.setIssuedDate(issueDate);
                    etTicketMapper.insert(t);
                } else {
                    t = ticket;
                }

                ticketList.add(t);
                log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 4:" + fltRefund.getRefundNo());
                try {
                    String passengerName = dtoPassengerList.get(i).getName();
                    String secondFactorCode = "NM";
                    String secondFactorValue = passengerName;
                    // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                    secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                    // 双因素处理无陪儿童
                    secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                    DETRTKTResult result = ShowTicket.getInstance("JD").getResult2F(t.getIssCode() + "-" + t.getTicketNo(), secondFactorCode, secondFactorValue);
                    ibeTicketMsg.put(dtoTicket.getTicketNo(), result);
                    log.info("DETRTKTResult:" + t.getIssCode() + t.getTicketNo() + ":" + result);
                } catch (Exception e) {
                    log.error("FlightRefundWithoutOrderServiceHelper getTicketInfoByTktNo error:" + e.getMessage());
                }
            }

            Set<EtFltRefundPaxSeg> fltRefundPaxSegs = new HashSet<EtFltRefundPaxSeg>();
            Set<EtPassengerSegment> passengerSegments = new HashSet<EtPassengerSegment>();

            Set<String> lineSet = new HashSet<String>();
            int i = 0;
            int segmentIndex = 1;
            int count = 0;
            //
            EtProduct productTSTP = etProductMapper.selectOne(new QueryWrapper<EtProduct>()
                    .eq("code", "TSTP")
                    .eq("airline_code", "JD")); //找个模拟产品

            log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 5:" + productTSTP.getCode() + productTSTP.getStatus() + fltRefund.getRefundNo());

            EtSegment goSegment = null;
            EtSegment backSegment = null;
            long goSegmentId = 0l;
            long backSegmentId = 0l;
            boolean hasChangedPassenger = true;
            for (DTOSegment dtoSegment : dtoSegmentList) {
                String line = dtoSegment.getDepCode() + dtoSegment.getArrCode();
                if (lineSet.contains(line)) {
                    i++;
                    hasChangedPassenger = true;
                    segmentIndex = 1; //换人之后航段顺序重置
                    lineSet.clear();//防治第二个人的第二段也重复，清空一下第一组航段
                }
                if (lineSet.size() == 0) {
                    hasChangedPassenger = true;
                } else {
                    hasChangedPassenger = false;
                }
                EtTicket t = ticketList.get(count);
                if (segmentIndex == 1 && goSegment == null) {
                    //保存航段信息
                    goSegment = SegmentMapper.getInstance().mapToEntity(dtoSegment);
                    goSegment.setInventory(Inventory.IBE.toString());
                    goSegment.setDepCode(dtoSegment.getDepCode());
                    goSegment.setArrCode(dtoSegment.getArrCode());
                    goSegmentId = etSegmentMapper.insert(goSegment);
                }
                if (segmentIndex == 2 && backSegment == null) {
                    //保存航段信息
                    backSegment = SegmentMapper.getInstance().mapToEntity(dtoSegment);
                    backSegment.setInventory(Inventory.IBE.toString());
                    backSegment.setDepCode(dtoSegment.getDepCode());
                    backSegment.setArrCode(dtoSegment.getArrCode());
                    backSegmentId = etSegmentMapper.insert(backSegment);
                }

                //多个要退的航段 TODO
                EtPassengerSegment pax = new EtPassengerSegment();
                pax.setTicketId(t.getId());
                pax.setSegmentId(segmentIndex == 1 ? goSegment.getId() : backSegment.getId());
                pax.setPassengerId(passengerList.get(count).getId());

                pax.setMarketFare(ticketPrices.get(count));
                pax.setNetFare(ticketPrices.get(count));
                pax.setFuelTax(BigDecimal.ZERO);
                pax.setAirportTax(taxPrices.get(count));
                pax.setOtherTaxes(pax.getFuelTax().add(pax.getAirportTax()));

                pax.setTicketStatus(TicketStatus.REFUNDED.getAlias());

                EtSpecialRefundOriginTkt specialRefundOriginTkt = null;
                if (ibeTicketMsg == null || ibeTicketMsg.size() == 0) {
                    pax.setStatus(SegmentStatus.NO.name());
                } else {
                    DETRTKTResult detrtktResult = ibeTicketMsg.get(t.getTicketNo());
                    if (detrtktResult == null) {
                        pax.setStatus(SegmentStatus.NO.name());
                    } else {
                        pax.setFuelTax(new BigDecimal(getFuelTax(detrtktResult) / detrtktResult.getSegmentCount()));
                        pax.setOtherTaxes(pax.getFuelTax().add(pax.getAirportTax()));
                        int currSegIndex = -1;
                        for (Object airSeg : detrtktResult.getAirSeg()) {
                            DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                            if (detrtktSegment.getDepAirportCode().equals(dtoSegment.getDepCode())
                                    && detrtktSegment.getArrAirportCode().equals(dtoSegment.getArrCode())) {
                                currSegIndex = detrtktSegment.getSegmentIndex();
                                break;
                            }
                        }
                        String exchangedTicketNo = detrtktResult.getExchangeInfo();
                        // 获取换开的原客票
                        specialRefundOriginTkt = handleSpecialRefundOriginTkt(exchangedTicketNo, detrtktResult.getPassengerName(), currSegIndex);
                        try {
                            for (Object airSeg : detrtktResult.getAirSeg()) {
                                log.error("-----" + airSeg.getClass());
                                DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                                log.error("-----" + detrtktSegment.getDepAirportCode() + dtoSegment.getDepCode());
                                log.error("-----" + detrtktSegment.getArrAirportCode() + dtoSegment.getArrCode());
                                String ticketStatus = detrtktSegment.getTicketStatus();
                                log.info("-----" + "FlightRefundWithoutOrderServiceImpl ticketStatus:" + ticketStatus + fltRefund.getRefundNo());

                                //USED/FLOWN","REFUNDED","OPEN FOR USE","CHECKED IN","SUSPEND"

                                if (ticketStatus != null && ticketStatus.indexOf("OPEN FOR USE") > -1) {
                                    pax.setTicketStatus(TicketStatus.OPEN_FOR_USE.getAlias());
                                    pax.setStatus(SegmentStatus.RR.name());
                                } else if (ticketStatus != null && ticketStatus.indexOf("REFUNDED") > -1) {
                                    pax.setTicketStatus(TicketStatus.REFUNDED.getAlias());
                                    pax.setStatus(SegmentStatus.XX.name());
                                } else if (ticketStatus != null && ticketStatus.indexOf("USED") > -1) {
                                    pax.setTicketStatus(TicketStatus.USED_OR_FLOWN.getAlias());
                                    pax.setStatus(SegmentStatus.RR.name());
                                } else if (ticketStatus != null && ticketStatus.indexOf("CHECKED") > -1) {
                                    pax.setTicketStatus(TicketStatus.CHECKED_IN.getAlias());
                                    pax.setStatus(SegmentStatus.RR.name());
                                } else {
                                    pax.setTicketStatus(TicketStatus.SUSPENDED.getAlias());
                                    pax.setStatus(SegmentStatus.RR.name());
                                }
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            pax.setStatus(SegmentStatus.NO.name());
                        }
                    }
                }
                pax.setProductId(productTSTP.getId());
                pax.setSequence(segmentIndex++);
                pax.setPoint(0l);
                pax.setCouponPrice(BigDecimal.ZERO);
                pax.setOldNetFare(BigDecimal.ZERO);
                pax.setCurrency(currencyMap.get(t.getIssCode() + "-" + t.getTicketNo()));

                long paxId = etPassengerSegmentMapper.insert(pax);
                passengerSegments.add(pax);

                // 保存换开的原客票
                if (specialRefundOriginTkt != null) {
                    pax.setCurrency(originCurrencyMap.get(t.getIssCode() + "-" + t.getTicketNo()));
                    specialRefundOriginTkt.setPaxSegId(pax.getId());
                    etSpecialRefundOriginTktMapper.insert(specialRefundOriginTkt);
                }

                lineSet.add(line);
                count++;
            }


            Set<EtPassengerSegment> goPaxSegs = new HashSet<EtPassengerSegment>();
            Set<EtPassengerSegment> backPaxSegs = new HashSet<EtPassengerSegment>();

            for (EtPassengerSegment passengerSegment : passengerSegments) {
                if (passengerSegment.getSequence() == 1) {
                    goPaxSegs.add(passengerSegment);
                } else {
                    backPaxSegs.add(passengerSegment);
                }
            }

            fltRefund.setOrderSource(dtoFltRefund.getOrderSource());

            //更新一下其他字段
            etFltRefundMapper.updateById(fltRefund);
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 8:" + fltRefund.getRefundNo());


            for (EtPassengerSegment passengerSegment : passengerSegments) {
                EtFltRefundPaxSeg fltRefundPaxSeg = new EtFltRefundPaxSeg(); //关联退票的航段
                fltRefundPaxSeg.setRefundAmount(BigDecimal.ZERO);
                fltRefundPaxSeg.setRefundId(fltRefund.getId());
                fltRefundPaxSegs.add(fltRefundPaxSeg);
                fltRefundPaxSeg.setPaxSegId(passengerSegment.getId());
                etFltRefundPaxSegMapper.insert(fltRefundPaxSeg);

                log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 7:" + fltRefund.getRefundNo());

            }


            // 保存银行卡信息
            dtoSpecialRefundBankInfo.setRefundNo(fltRefund.getRefundNo());
            EtSpecialRefundBankInfo specialRefundBankInfo = SpecialRefundBankInfoMapper.getInstance().mapToEntity(dtoSpecialRefundBankInfo);
            specialRefundBankInfo.setFltRefundId(fltRefund.getId());
            etSpecialRefundBankInfoMapper.insert(specialRefundBankInfo);
            log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 9:" + fltRefund.getRefundNo());

            // 保存用户上传图片信息
            for (DTOSpecialRefundImg dtoSpecialRefundImg : dtoSpecialRefundImgList) {
                dtoSpecialRefundImg.setRefundNo(fltRefund.getRefundNo());
            }
            List<EtSpecialRefundImg> specialRefundImgList = SpecialRefundImgMapper.getInstance().mapToEntities(dtoSpecialRefundImgList);
            for (EtSpecialRefundImg specialRefundImg : specialRefundImgList) {
                specialRefundImg.setRefundAuditId(audit.getId());
                specialRefundImg.setFltRefundId(fltRefund.getId());
            }
            for (EtSpecialRefundImg etSpecialRefundImg : specialRefundImgList) {
                etSpecialRefundImgMapper.insert(etSpecialRefundImg);
            }
            log.info("FlightRefundWithoutOrderServiceHelper.createSpecialFltRefund - 10:" + fltRefund.getRefundNo());

            return fltRefund.getRefundNo();
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new SpecialRefundException();
        }
    }


    /**
     * 国际票特殊退票，生成退票单接口
     * @param dtoFltRefund
     * @param dtoPayment
     * @param dtoSegmentList
     * @param dtoTicketList
     * @param dtoPassengerList
     * @param netFareMap  票面map 898-********** -> 2500.0
     * @param taxMap  税费map 898-********** -> YQ:52.0,J9:15.0,YR:2400.0,CN:90.0,YP:150.0,PT:59.0
     * @param originTktNetFareMap  原票票面map 898-********** -> 2500.0
     * @param originTktTaxMap  原票税费map 898-********** -> YQ:52.0,J9:15.0,YR:2400.0,CN:90.0,YP:150.0,PT:59.0
     * @param dtoSpecialRefundBankInfo
     * @return
     */
    @Transactional
    public String createSpecialFltRefundGJ(DTOFltRefund dtoFltRefund, DTOPayment dtoPayment, List<DTOSegment> dtoSegmentList, List<DTOTicket> dtoTicketList,
                                           List<DTOPassenger> dtoPassengerList,
                                           Map<String, String> netFareMap, Map<String, String> taxMap, Map<String, String> currencyMap,
                                           Map<String, String> originTktNetFareMap, Map<String, String> originTktTaxMap, Map<String, String> originCurrencyMap,
                                           DTOSpecialRefundBankInfo dtoSpecialRefundBankInfo, List<DTOSpecialRefundImg> dtoSpecialRefundImgList) {
        try {
            EtFltRefund fltRefund = new EtFltRefund();
            fltRefund.setOrderId(2L);
            fltRefund.setRefundNo(FlightRefundNumberGenerator.generate());
            fltRefund.setRefundType(dtoFltRefund.getRefundType());//自愿、非自愿
            fltRefund.setConstraintReason(dtoFltRefund.getConstraintReason());//非自愿原因
            fltRefund.setCreateTime(dtoFltRefund.getRefundTime());
            fltRefund.setStatus("NEW");//退票单初始状态
            fltRefund.setUserName(dtoFltRefund.getUserName());
            fltRefund.setAirlineCode("JD");
            fltRefund.setOrderSource(dtoFltRefund.getOrderSource());//订单来源
            fltRefund.setReserveRemark(dtoFltRefund.getReserveRemark());
            fltRefund.setRemark(dtoFltRefund.getRemark());
            fltRefund.setFictitious(dtoFltRefund.getFictitious());  //用于记录退单类型

            // 1.保存 et_flt_refund 表
            long refundId = etFltRefundMapper.insert(fltRefund);

            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefundGJ - 1:" + fltRefund.getRefundNo());

            //审核记录
            Set<EtFltRefundAudit> fltRefundAudit = new HashSet<EtFltRefundAudit>();
            EtFltRefundAudit audit = new EtFltRefundAudit();
            audit.setSubmitTime(new Date());
            audit.setAuditTime(new Date());
            audit.setRefundId(fltRefund.getId());
            audit.setSubmitUsername(dtoFltRefund.getRefundUser());
            audit.setNotion(dtoFltRefund.getRemark());
            audit.setAction("FIRSTAUDIT");
            fltRefundAudit.add(audit);
            // 2.保存 et_flt_refund_audit 表
            etFltRefundAuditMapper.insert(audit);

            //外部支付记录
            EtPayment outPayment = new EtPayment();
            outPayment.setPaymentNo(FlightRefundNumberGenerator.generate());//退款流水号
            outPayment.setAmount(dtoPayment.getAmount());
            outPayment.setPayType(dtoPayment.getPayType().toString());
            outPayment.setPayStatus(PayStatus.PAID.toString());
            outPayment.setSource("TSGJ");
            outPayment.setPayTime(dtoPayment.getPayTime());
            outPayment.setAction(PayAction.PAY.toString());
            outPayment.setCurrency("RMB");
            // payment.setRemark(dtoPayment.getPaymentNo());
            // 3.保存 et_payment
            etPaymentMapper.insert(outPayment);
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefundGJ - 2:" + fltRefund.getRefundNo());


            List<EtPassenger> passengerList = new ArrayList<EtPassenger>();
            for (DTOPassenger dtoPassenger : dtoPassengerList) {
                Long customerId = null;
                Customer customer = null;
                Customer ce = customerMapper.selectOne(new QueryWrapper<Customer>()
                        .eq("name", dtoPassenger.getName())
                        .eq("certificateno", EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo()))
                        .eq("certificatetype", dtoPassenger.getCertificateType())
                );
                if (ce == null) {
                    customer = new Customer();
                    customer.setName(dtoPassenger.getName());
                    customer.setCertificatetype(dtoPassenger.getCertificateType());
                    try {
                        customer.setCertificateno(EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo()));
                        customer.setEncrypted("1");
                    } catch (Exception e) {
                        customer.setCertificateno(dtoPassenger.getCertificateNo());
                        log.error("EncoderUtil.encryptDefault error:"+e.getMessage());
                    }
                    customer.setGender(Gender.MALE.toString());
                    customerId = Long.valueOf(customerMapper.insert(customer));
                } else {
                    customer = ce;
                    customerId = ce.getId();
                }

                EtPassenger passenger = new EtPassenger();
                passenger.setCustomer(customer);
                passenger.setPassengerType(dtoPassenger.getPassengerType().toString());

                // 有编码的时候，保存关联编码
                if (dtoPassenger.getPnrValue() != null && StringUtils.hasText(dtoPassenger.getPnrValue().getPnrNo())) {
                    EtPnr pnr = new EtPnr();
                    pnr.setPnrNo(dtoPassenger.getPnrValue().getPnrNo());
                    etPnrMapper.insert(pnr);
                    passenger.setPnrId(pnr.getId());
                }
                passenger.setCustomerId(customer.getId());

                long passengerId = etPassengerMapper.insert(passenger);
                passengerList.add(passenger);
                log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefundGJ - 3:" + fltRefund.getRefundNo());
            }

            //把外部支付记录关联到退单
            EtFltRefundOutsidePayment refundPayment = new EtFltRefundOutsidePayment();

            refundPayment.setAmount(BigDecimal.ZERO);
            refundPayment.setFltRefundId(fltRefund.getId());
            if (outPayment != null) {
                refundPayment.setPaymentId(outPayment.getId());
            }

            // fltRefund.setFltRefundOutSidePayments(fltRefundOutSidePayments);//退票单关联外部支付记录
            etFltRefundOutsidePaymentMapper.insert(refundPayment);


            List<EtTicket> ticketList = new ArrayList<EtTicket>();
            Map<String,TicketAndTaxe> ticketNoToTaxes = new HashMap<String,TicketAndTaxe>();

            //提取票状态，后续考虑去掉 @TODO
            Map<String, DETRTKTResult> ibeTicketMsg = new HashMap<String, DETRTKTResult>();
            for (int i = 0; i < dtoTicketList.size(); i++) {
                DTOTicket dtoTicket = dtoTicketList.get(i);
                EtTicket t = new EtTicket();
                EtTicket ticket = etTicketMapper.selectOne(new QueryWrapper<EtTicket>()
                        .eq("iss_code", dtoTicket.getIssCode())
                        .eq("ticket_no", dtoTicket.getTicketNo()));

                EtTicketAlltaxGj ticketAllTaxGJ =  new EtTicketAlltaxGj();
                String allTaxsDesc = taxMap.get(dtoTicket.getIssCode() + "-" + dtoTicket.getTicketNo());

                //所有税费明细
                String allTaxeDetail = "";
                Map<String,BigDecimal> allTaxeDetailMap = new HashMap<String,BigDecimal>();//相同的税费要累加
                //摘出机建和燃油
                TicketAndTaxe ticketAndTaxe = new TicketAndTaxe();
                boolean hasFoundCn = false;
                boolean hasFoundYr = false;
                boolean hasFoundYq = false;
                BigDecimal otherAllFare = BigDecimal.ZERO;

                if (allTaxsDesc != null) {
                    log.info("createFltRefundMsg allTaxsDesc:"+allTaxsDesc);
                    for(String taxKeyValue : allTaxsDesc.split(",")) {
                        if (StringUtils.hasText(taxKeyValue)) {
                            String[] keyValue = taxKeyValue.split(":");
                            if ("CN".equals(keyValue[0])) {
                                ticketAndTaxe.setCn(keyValue[1]);
                                hasFoundCn = true;
                            } else if ("YR".equals(keyValue[0])) {
                                ticketAndTaxe.setYr(keyValue[1]);
                                hasFoundYr = true;
                            } else if ("YQ".equals(keyValue[0])) {
                                ticketAndTaxe.setYq(keyValue[1]);
                                hasFoundYq = true;
                            } else {
                                //排除CN、YR、YQ，其他税加一起。例如GB:-703.0;UB:-428.0;YR:-967.0;YQ:-28.0
                                otherAllFare = otherAllFare.add(new BigDecimal(keyValue[1]));//-1131(GB+UB)
                                log.info("otherAllFare-1:"+otherAllFare.intValue());
                            }
                            //所有的税都要放入明细
                            if (allTaxeDetailMap.containsKey(keyValue[0])) {
                                allTaxeDetailMap.put(keyValue[0],allTaxeDetailMap.get(keyValue[0]).add(new BigDecimal(keyValue[1])));
                            } else {
                                allTaxeDetailMap.put(keyValue[0],new BigDecimal(keyValue[1]));
                            }

                        }
                    }
                }

                for (String allTaxDetailKey : allTaxeDetailMap.keySet()) {
                    allTaxeDetail += allTaxDetailKey+":"+allTaxeDetailMap.get(allTaxDetailKey)+",";
                }


                if (!hasFoundCn) {
                    ticketAndTaxe.setCn("0");
                }
                if (!hasFoundYq) {
                    ticketAndTaxe.setYq("0");
                }
                if (!hasFoundYr) {
                    ticketAndTaxe.setYr("0");
                }
                ticketAndTaxe.setTicketNo(t.getTicketNo());

                //需要排除机建和燃油
                if (StringUtils.hasText(allTaxeDetail)) {
                    ticketAllTaxGJ.setAllTax(allTaxeDetail.substring(0,allTaxeDetail.length()-1));
                }

                ticketAndTaxe.setOther(otherAllFare);
                log.info("otherAllFare-2:"+otherAllFare.intValue());

                if (ticket == null) {
                    String passengerName = dtoPassengerList.get(i).getName();
                    Date issueDate = getIssueDateFromDETRHistory(dtoTicket.getIssCode() + "-" + dtoTicket.getTicketNo(), passengerName);
                    t.setIssCode(dtoTicket.getIssCode());
                    t.setTicketNo(dtoTicket.getTicketNo());
                    t.setBspType("1");
                    t.setBookTime(issueDate);
                    t.setIssuedDate(issueDate);
                    etTicketMapper.insert(t);
                    ticketAllTaxGJ.setTicketId(t.getId());
                    etTicketAlltaxGjMapper.insert(ticketAllTaxGJ);
                } else {
                    t = ticket;
                }
                ticketList.add(t);
                ticketNoToTaxes.put(t.getTicketNo(),ticketAndTaxe);
                log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefundGJ - 4:" + fltRefund.getRefundNo());

                try {
                    String passengerName = dtoPassengerList.get(i).getName();
                    String secondFactorCode = "NM";
                    String secondFactorValue = passengerName;
                    // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                    secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                    // 双因素处理无陪儿童
                    secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                    DETRTKTResult result = ShowTicket.getInstance("JD").getResult2F(t.getIssCode() + "-" + t.getTicketNo(), secondFactorCode, secondFactorValue);
                    ibeTicketMsg.put(dtoTicket.getTicketNo(), result);
                    log.info("DETRTKTResultDETRTKTResult:" + t.getIssCode() + t.getTicketNo() + ":" + result);
                } catch (Exception e) {
                    log.error("FlightRefundWithoutOrderServiceImpl getTicketInfoByTktNo error:" + e.getMessage());
                }
            }

            Set<EtFltRefundPaxSeg> fltRefundPaxSegs = new HashSet<EtFltRefundPaxSeg>();
            Set<EtPassengerSegment> passengerSegments = new HashSet<EtPassengerSegment>();

            Set<String> lineSet = new HashSet<String>();
            int i = 0;
            int segmentIndex = 1;
            int count = 0;
            //
            EtProduct productTSTP = etProductMapper.selectOne(new QueryWrapper<EtProduct>()
                    .eq("code", "TSTP")
                    .eq("airline_code", "JD")); //找个模拟产品
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefundGJ - 5:" + productTSTP.getCode() + productTSTP.getStatus() + fltRefund.getRefundNo());


                List<EtSegment> tmpSegmentList = new ArrayList<EtSegment>();
                boolean hasChangedPassenger = true;
                for (DTOSegment dtoSegment : dtoSegmentList) {
                    String line = dtoSegment.getDepCode() + dtoSegment.getArrCode();
                    if (lineSet.contains(line)) {
                        i++;
                        hasChangedPassenger = true;
                        segmentIndex = 1; //换人之后航段顺序重置
                        lineSet.clear();//防治第二个人的第二段也重复，清空一下第一组航段
                    }
                    if (lineSet.size() == 0) {
                        hasChangedPassenger = true;
                    } else {
                        hasChangedPassenger = false;
                    }
                    EtTicket t = ticketList.get(count);

                    EtSegment s = new EtSegment();

                    //保存航段信息
                    s = SegmentMapper.getInstance().mapToEntity(dtoSegment);
                    s.setInventory(Inventory.IBE.toString());

                    s.setDepCode(dtoSegment.getDepCode());
                    s.setArrCode(dtoSegment.getArrCode());

                    etSegmentMapper.insert(s);

                    //多个要退的航段 TODO
                    EtPassengerSegment pax = new EtPassengerSegment();
                    pax.setTicketId(t.getId());
                    pax.setSegmentId(s.getId());
                    pax.setPassengerId(passengerList.get(count).getId());


                    if (hasChangedPassenger) {
                        //国际票多段只有一个价格，价格全部放在第一段
                        pax.setMarketFare(new BigDecimal(netFareMap.get(t.getIssCode() + "-" + t.getTicketNo())));
                        pax.setNetFare(new BigDecimal(netFareMap.get(t.getIssCode() + "-" + t.getTicketNo())));
                        TicketAndTaxe ticketAndTaxe = ticketNoToTaxes.get(t.getTicketNo());
                        if (ticketAndTaxe != null && ticketAndTaxe.getCn() != null && ticketAndTaxe.getYq() != null) {
                            pax.setFuelTax(new BigDecimal(ticketAndTaxe.getYr()));
                            pax.setAirportTax(new BigDecimal(ticketAndTaxe.getCn()));
                            pax.setTax1(new BigDecimal(ticketAndTaxe.getYq()));//战险
                        } else {
                            pax.setFuelTax(BigDecimal.ZERO);
                            pax.setAirportTax(BigDecimal.ZERO);
                        }
                        Double allTax = 0.0;
                        try {
                            for (String s1 : taxMap.get(t.getIssCode() + "-" + t.getTicketNo()).split(",")) {
                                allTax += Double.parseDouble(s1.split(":")[1]);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        pax.setOtherTaxes(new BigDecimal(allTax)); //国际票的税临时存这里
                        pax.setTax2(ticketAndTaxe.getOther());
                        log.info("otherAllFare-3:" + pax.getTax2());
                    } else {
                        pax.setMarketFare(BigDecimal.ZERO);
                        pax.setNetFare(BigDecimal.ZERO);
                        pax.setOtherTaxes(BigDecimal.ZERO);
                        pax.setFuelTax(BigDecimal.ZERO);
                        pax.setAirportTax(BigDecimal.ZERO);
                    }

                    pax.setTicketStatus(TicketStatus.REFUNDED.toString());

                    EtSpecialRefundOriginTkt specialRefundOriginTkt = null;
                    if (ibeTicketMsg == null || ibeTicketMsg.size() == 0) {
                        pax.setStatus(SegmentStatus.NO.toString());
                    } else {
                        DETRTKTResult detrtktResult = ibeTicketMsg.get(t.getTicketNo());
                        if (detrtktResult == null) {
                            pax.setStatus(SegmentStatus.NO.toString());
                        } else {
                            int currSegIndex = -1;
                            for (Object airSeg : detrtktResult.getAirSeg()) {
                                DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                                if (detrtktSegment.getDepAirportCode().equals(dtoSegment.getDepCode())
                                        && detrtktSegment.getArrAirportCode().equals(dtoSegment.getArrCode())) {
                                    currSegIndex = detrtktSegment.getSegmentIndex();
                                    break;
                                }
                            }
                            String exchangedTicketNo = detrtktResult.getExchangeInfo();
                            // 获取换开的原客票
                            specialRefundOriginTkt = handleSpecialRefundOriginTktGJ(exchangedTicketNo, detrtktResult.getPassengerName(), currSegIndex, originTktNetFareMap, originTktTaxMap);
                            try {
                                for (Object airSeg : detrtktResult.getAirSeg()) {
                                    log.error("-----" + airSeg.getClass());
                                    DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                                    log.error("-----" + detrtktSegment.getDepAirportCode() + dtoSegment.getDepCode());
                                    log.error("-----" + detrtktSegment.getArrAirportCode() + dtoSegment.getArrCode());
                                    String ticketStatus = detrtktSegment.getTicketStatus();
                                    log.info("-----" + "FlightRefundWithoutOrderServiceImpl ticketStatus:" + ticketStatus + fltRefund.getRefundNo());

                                    //USED/FLOWN","REFUNDED","OPEN FOR USE","CHECKED IN","SUSPEND"

                                    if (ticketStatus != null && ticketStatus.indexOf("OPEN FOR USE") > -1) {
                                        pax.setTicketStatus(TicketStatus.OPEN_FOR_USE.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("REFUNDED") > -1) {
                                        pax.setTicketStatus(TicketStatus.REFUNDED.toString());
                                        pax.setStatus(SegmentStatus.XX.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("USED") > -1) {
                                        pax.setTicketStatus(TicketStatus.USED_OR_FLOWN.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("CHECKED") > -1) {
                                        pax.setTicketStatus(TicketStatus.CHECKED_IN.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else {
                                        pax.setTicketStatus(TicketStatus.SUSPENDED.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    }
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage());
                                pax.setStatus(SegmentStatus.NO.toString());
                            }
                        }
                    }
                    pax.setProductId(productTSTP.getId());
                    pax.setSequence(segmentIndex++);
                    pax.setCouponPrice(BigDecimal.ZERO);
                    pax.setOldNetFare(BigDecimal.ZERO);
                    pax.setCurrency(currencyMap.get(t.getIssCode() + "-" + t.getTicketNo()));

                    long paxId = etPassengerSegmentMapper.insert(pax);
                    passengerSegments.add(pax);

                    // 保存换开的原客票
                    if (specialRefundOriginTkt != null) {
                        pax.setCurrency(originCurrencyMap.get(t.getIssCode() + "-" + t.getTicketNo()));
                        specialRefundOriginTkt.setPaxSegId(pax.getId());
                        etSpecialRefundOriginTktMapper.insert(specialRefundOriginTkt);
                    }

                    lineSet.add(line);
                    tmpSegmentList.add(s);
                    count++;
                }


                for (EtSegment segment : tmpSegmentList) {
                    Set<EtPassengerSegment> tmpPsSet = new HashSet<EtPassengerSegment>();
                    for (EtPassengerSegment passengerSegment : passengerSegments) {
                        EtSegment s = etSegmentMapper.selectById(passengerSegment.getSegmentId());
                        if (segment.getDepCode().equals(s.getDepCode())
                                && segment.getArrCode().equals(s.getArrCode())
                        ) {
                            tmpPsSet.add(passengerSegment);
                        }
                    }
                    segment.setId(null);
                    etSegmentMapper.insert(segment);
                    log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 6:" + fltRefund.getRefundNo());
                }


            for (EtPassengerSegment passengerSegment : passengerSegments) {
                EtFltRefundPaxSeg fltRefundPaxSeg = new EtFltRefundPaxSeg(); //关联退票的航段
                fltRefundPaxSeg.setRefundAmount(BigDecimal.ZERO);
                fltRefundPaxSeg.setRefundId(fltRefund.getId());
                fltRefundPaxSegs.add(fltRefundPaxSeg);
                fltRefundPaxSeg.setPaxSegId(passengerSegment.getId());
                etFltRefundPaxSegMapper.insert(fltRefundPaxSeg);

                log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 7:" + fltRefund.getRefundNo());

            }

            fltRefund.setOrderSource(dtoFltRefund.getOrderSource());

            //更新一下其他字段
            etFltRefundMapper.updateById(fltRefund);
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 8:" + fltRefund.getRefundNo());

            // 保存银行卡信息
            dtoSpecialRefundBankInfo.setRefundNo(fltRefund.getRefundNo());
            EtSpecialRefundBankInfo specialRefundBankInfo = SpecialRefundBankInfoMapper.getInstance().mapToEntity(dtoSpecialRefundBankInfo);
            specialRefundBankInfo.setFltRefundId(fltRefund.getId());
            etSpecialRefundBankInfoMapper.insert(specialRefundBankInfo);
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 9:" + fltRefund.getRefundNo());

            // 保存用户上传图片信息
            for (DTOSpecialRefundImg dtoSpecialRefundImg : dtoSpecialRefundImgList) {
                dtoSpecialRefundImg.setRefundNo(fltRefund.getRefundNo());
            }
            List<EtSpecialRefundImg> specialRefundImgList = SpecialRefundImgMapper.getInstance().mapToEntities(dtoSpecialRefundImgList);
            for (EtSpecialRefundImg specialRefundImg : specialRefundImgList) {
                specialRefundImg.setRefundAuditId(audit.getId());
                specialRefundImg.setFltRefundId(fltRefund.getId());
            }
            for (EtSpecialRefundImg etSpecialRefundImg : specialRefundImgList) {
                etSpecialRefundImgMapper.insert(etSpecialRefundImg);
            }
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 10:" + fltRefund.getRefundNo());

            return fltRefund.getRefundNo();
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new SpecialRefundException();
        }
    }


    /**
     * 生成国际票、海外站退单
     *
     * @param dtoFltRefund
     * @param dtoPayment
     * @param dtoSegmentList
     * @param dtoTicketList
     * @param dtoPassengerList
     * @param ticketPrices
     * @param taxPrices
     * @param allTax
     * @return
     */
    @Transactional
    public String createFltRefundMsg(DTOFltRefund dtoFltRefund, DTOPayment dtoPayment, List<DTOSegment> dtoSegmentList, List<DTOTicket> dtoTicketList,
                                     List<DTOPassenger> dtoPassengerList, List<BigDecimal> ticketPrices, List<BigDecimal> taxPrices, String[] allTax) {

		/*
		insert into ET_FLT_REFUND values(733547,'20200628092045235811',1,'JDADMIN',sysdate,'NEW','测试国际票',null,null,'JD','UNCONSTRAINT','','','',''
,'GJ',sysdate,0,0,null,null,null,null,null,null);
insert into et.ET_FLT_REFUND_AUDIT values(1073017,733547,'TEST',sysdate,'JDADMIN',sysdate,'FIRSTAUDIT','测试国际票',null);
insert into ET_FLT_REFUND_PAX_SEG values(1043026,733547,15376949,0,0,0,0);
insert into ET_PASSENGER_SEGMENT values(15376954,13550975,8492198,12579374,1000,1000,100,0,0,null,null,null,null,0,0,0,0,0,null,null,null,null,'R','XX',221,1,0
,null,null,null,null,null,0,1000,0,0,null,0); --需要维护一个国际虚拟产品 221   旅客表13550975
insert into ET_SEGMENT values(8492198,0,'PEK','PEK','X','JD123',sysdate,sysdate,'320',null,'JD','IBE',20000); --需要检查国际三字码是否都在表里
insert into et.et_ticket values(12579374,'898','*********',null,null,0,0,null,0,null,null,null,sysdate,sysdate);
insert into et.et_payment values(9316767,'2020070622001494311428135534','ALIP','PAY',00.1,'PAID',sysdate,'RMB','','',null,null,null,null,null
,'2088421702658252',null,sysdate,null,null,'GJ',null);
insert into et.ET_PAYMENT_ALIP values(9316767,null,'C',null,'2020070622001494311428135534',null,null,0,null,null,'2088421702658252',null,null);
		 */

        try {
            EtFltRefund fltRefund = new EtFltRefund();
            fltRefund.setOrderId(1l);
            fltRefund.setRefundNo(FlightRefundNumberGenerator.generate());
            fltRefund.setRefundType(dtoFltRefund.getRefundType());//自愿、非自愿

            fltRefund.setConstraintReason(dtoFltRefund.getConstraintReason());//非自愿原因

            fltRefund.setCreateTime(dtoFltRefund.getRefundTime());

            fltRefund.setStatus("NEW");//退票单初始状态
            fltRefund.setUserName(dtoFltRefund.getUserName());
            fltRefund.setAirlineCode("JD");
            fltRefund.setOrderSource(dtoFltRefund.getOrderSource());//订单来源
            fltRefund.setReserveRemark(dtoFltRefund.getReserveRemark());
            fltRefund.setRemark(dtoFltRefund.getRemark());
            fltRefund.setOfficeNo(dtoFltRefund.getOfficeNo());
            fltRefund.setRetrieveTimeFlag(dtoFltRefund.getRetrieveTimeFlag());

            // 1
            etFltRefundMapper.insert(fltRefund);

            log.info("FlightRefundWithoutOrderServiceImpl - 1:" + fltRefund.getRefundNo());

            //审核记录
            EtFltRefundAudit audit = new EtFltRefundAudit();
            audit.setSubmitTime(new Date());
            audit.setAuditTime(new Date());
            audit.setRefundId(fltRefund.getId());
            audit.setSubmitUsername(dtoFltRefund.getRefundUser());
            audit.setNotion("国际票退票");
            if ("HWZ".equals(dtoFltRefund.getOrderSource())) {
                audit.setNotion("海外站退票");
            }
            audit.setAction("FIRSTAUDIT");

            // 2
            etFltRefundAuditMapper.insert(audit);

            //外部支付记录
            EtAlipayPayment outPayment = null;
            EtWXpayPayment wxPayment = null;
            EtPayment worldPayPayment = null;
            if (PayType.ALIPAY.equals(dtoPayment.getPayType())) {
                outPayment = new EtAlipayPayment();
                outPayment.setPaymentNo(FlightRefundNumberGenerator.generate());//退款流水号
                outPayment.setAmount(dtoPayment.getAmount());
                outPayment.setPayType(dtoPayment.getPayType().toString());

                outPayment.setPayStatus(PayStatus.PAID.toString());
                outPayment.setSource("GJ");
                if ("HWZ".equals(dtoFltRefund.getOrderSource())) {
                    outPayment.setSource("HWZ");
                }
                outPayment.setPayerContactType("C");
                outPayment.setPayTime(dtoPayment.getPayTime());
                outPayment.setAction(PayAction.PAY.toString());
                outPayment.setCurrency("RMB");
                outPayment.setDealId(dtoPayment.getRemark());//支付平台流水号，用于退款
                outPayment.setRemark(dtoPayment.getPaymentNo());//国际票的支付流水号
                etPaymentMapper.insert(outPayment);
                etAlipayPaymentMapper.insert(outPayment);
                log.info("FlightRefundWithoutOrderServiceImpl - 2:" + fltRefund.getRefundNo());
            } else if (PayType.WORLD_PAY.equals(dtoPayment.getPayType())) {
                worldPayPayment = new EtPayment();
                worldPayPayment.setPaymentNo(FlightRefundNumberGenerator.generate());//退款流水号
                worldPayPayment.setAmount(dtoPayment.getAmount());
                worldPayPayment.setPayType(dtoPayment.getPayType().toString());

                worldPayPayment.setPayStatus(PayStatus.PAID.toString());
                worldPayPayment.setSource("GJ");
                if ("HWZ".equals(dtoFltRefund.getOrderSource())) {
                    worldPayPayment.setSource("HWZ");
                }
                //worldPayPayment.setPayerContactType("C");
                worldPayPayment.setPayTime(dtoPayment.getPayTime());
                worldPayPayment.setAction(PayAction.PAY.toString());
                worldPayPayment.setCurrency("RMB");
                //worldPayPayment.setDealId(dtoPayment.getRemark());//支付平台流水号，用于退款
                worldPayPayment.setRemark(dtoPayment.getPaymentNo());//国际票的支付流水号
                etPaymentMapper.insert(worldPayPayment);
                log.info("FlightRefundWithoutOrderServiceImpl - 2:" + fltRefund.getRefundNo());
            } else {
                wxPayment = new EtWXpayPayment();
                wxPayment.setPaymentNo(FlightRefundNumberGenerator.generate());//退款流水号
                wxPayment.setAmount(dtoPayment.getAmount());
                wxPayment.setPayType(dtoPayment.getPayType().toString());
                wxPayment.setPayStatus(PayStatus.PAID.toString());
                wxPayment.setSource("GJ");
                if ("HWZ".equals(dtoFltRefund.getOrderSource())) {
                    wxPayment.setSource("HWZ");
                }
                wxPayment.setPayTime(dtoPayment.getPayTime());
                wxPayment.setAction(PayAction.PAY.toString());
                wxPayment.setCurrency("RMB");
                wxPayment.setDealId(dtoPayment.getRemark());//支付平台流水号，用于退款
                wxPayment.setRemark(dtoPayment.getPaymentNo());//国际票的支付流水号
                etPaymentMapper.insert(wxPayment);
                etWXpayPaymentMapper.insert(wxPayment);
                log.info("FlightRefundWithoutOrderServiceImpl - 2:" + fltRefund.getRefundNo());
            }


            List<EtPassenger> passengerList = new ArrayList<EtPassenger>();
            for (DTOPassenger dtoPassenger : dtoPassengerList) {

                Customer customer = null;
                String certificateNo = "";
                try {
                    certificateNo = EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Customer ce = customerMapper.findCustomerByNameAndId(
                        dtoPassenger.getName(),
                        certificateNo,
                        dtoPassenger.getCertificateType());
                if (ce == null) {
                    customer = new Customer();
                    customer.setName(dtoPassenger.getName());
                    customer.setCertificatetype(dtoPassenger.getCertificateType());
                    try {
                        customer.setCertificateno(EncoderUtil.encryptDefault(dtoPassenger.getCertificateNo()));
                        customer.setEncrypted("1");
                    } catch (Exception e) {
                        customer.setCertificateno(dtoPassenger.getCertificateNo());
                        log.error("EncoderUtil.encryptDefault error:" + e.getMessage());
                    }
                    customer.setGender(Gender.MALE.toString());
                    customerMapper.insert(customer);
                } else {
                    customer = ce;
                }

                EtPassenger passenger = new EtPassenger();
                passenger.setCustomerId(customer.getId());
                passenger.setPassengerType(dtoPassenger.getPassengerType().toString());
                passenger.setMobilePhone(dtoPassenger.getMobilePhone());
                etPassengerMapper.insert(passenger);
                passengerList.add(passenger);
                log.info("FlightRefundWithoutOrderServiceImpl - 3:" + fltRefund.getRefundNo());
            }

            //把外部支付记录关联到退单
            EtFltRefundOutsidePayment refundPayment = new EtFltRefundOutsidePayment();

            refundPayment.setAmount(BigDecimal.ZERO);
            refundPayment.setFltRefundId(fltRefund.getId());
            if (outPayment != null) {
                refundPayment.setPaymentId(outPayment.getId());
            } else if (worldPayPayment != null) {
                refundPayment.setPaymentId(worldPayPayment.getId());
            } else {
                refundPayment.setPaymentId(wxPayment.getId());
            }

            // fltRefund.setFltRefundOutSidePayments(fltRefundOutSidePayments);//退票单关联外部支付记录
            etFltRefundOutsidePaymentMapper.insert(refundPayment);

            List<EtTicket> ticketList = new ArrayList<EtTicket>();
            Map<String, TicketAndTaxe> ticketNoToTaxes = new HashMap<String, TicketAndTaxe>();
            //提取票状态，后续考虑去掉 @TODO
            Map<String, DETRTKTResult> ibeTicketMsg = new HashMap<String, DETRTKTResult>();

            for (int i = 0; i < dtoTicketList.size(); i++) {
                DTOTicket dtoTicket = dtoTicketList.get(i);
                EtTicket t = new EtTicket();
                t.setIssCode(dtoTicket.getIssCode());
                t.setTicketNo(dtoTicket.getTicketNo());
                t.setBspType("1");
                t.setBookTime(dtoTicket.getBookTime());
                t.setIssuedDate(dtoTicket.getBookTime());
                etTicketMapper.insert(t);
                EtTicketAlltaxGj ticketAllTaxGJ = new EtTicketAlltaxGj();
                ticketAllTaxGJ.setTicketId(t.getId());
                String allTaxsDesc = allTax[i];

                //所有税费明细
                String allTaxeDetail = "";
                Map<String, BigDecimal> allTaxeDetailMap = new HashMap<String, BigDecimal>();//相同的税费要累加
                //摘出机建和燃油
                TicketAndTaxe ticketAndTaxe = new TicketAndTaxe();
                boolean hasFoundCn = false;
                boolean hasFoundYr = false;
                boolean hasFoundYq = false;
                BigDecimal otherAllFare = BigDecimal.ZERO;

                if (allTaxsDesc != null) {
                    log.info("createFltRefundMsg allTaxsDesc:" + allTaxsDesc);
                    for (String taxKeyValue : allTaxsDesc.split(",")) {
                        if (StringUtils.hasText(taxKeyValue)) {
                            String[] keyValue = taxKeyValue.split(":");
                            if (keyValue != null && keyValue.length == 2) {
                                if ("CN".equals(keyValue[0])) {
                                    ticketAndTaxe.setCn(keyValue[1]);
                                    hasFoundCn = true;
                                } else if ("YR".equals(keyValue[0])) {
                                    ticketAndTaxe.setYr(keyValue[1]);
                                    hasFoundYr = true;
                                } else if ("YQ".equals(keyValue[0])) {
                                    ticketAndTaxe.setYq(keyValue[1]);
                                    hasFoundYq = true;
                                } else {
                                    //排除CN、YR、YQ，其他税加一起。例如GB:-703.0;UB:-428.0;YR:-967.0;YQ:-28.0
                                    otherAllFare = otherAllFare.add(new BigDecimal(keyValue[1]));//-1131(GB+UB)
                                    log.info("otherAllFare-1:" + otherAllFare.intValue());
                                }
                                //所有的税都要放入明细
                                if (allTaxeDetailMap.containsKey(keyValue[0])) {
                                    allTaxeDetailMap.put(keyValue[0], allTaxeDetailMap.get(keyValue[0]).add(new BigDecimal(keyValue[1])));
                                } else {
                                    allTaxeDetailMap.put(keyValue[0], new BigDecimal(keyValue[1]));
                                }
                            }
                        }
                    }
                }

                for (String allTaxDetailKey : allTaxeDetailMap.keySet()) {
                    if ("CN".equals(allTaxDetailKey)) {  // 重新赋值,防止多个同税种的情况的取值问题
                        ticketAndTaxe.setCn(allTaxeDetailMap.get(allTaxDetailKey).toString());
                    } else if ("YR".equals(allTaxDetailKey)) {
                        ticketAndTaxe.setYr(allTaxeDetailMap.get(allTaxDetailKey).toString());
                    } else if ("YQ".equals(allTaxDetailKey)) {
                        ticketAndTaxe.setYq(allTaxeDetailMap.get(allTaxDetailKey).toString());
                    }
                    allTaxeDetail += allTaxDetailKey + ":" + allTaxeDetailMap.get(allTaxDetailKey) + ",";
                }


                if (!hasFoundCn) {
                    ticketAndTaxe.setCn("0");
                }
                if (!hasFoundYq) {
                    ticketAndTaxe.setYq("0");
                }
                if (!hasFoundYr) {
                    ticketAndTaxe.setYr("0");
                }
                ticketAndTaxe.setTicketNo(t.getTicketNo());

                //需要排除机建和燃油
                if (StringUtils.hasText(allTaxeDetail)) {
                    ticketAllTaxGJ.setAllTax(allTaxeDetail.substring(0, allTaxeDetail.length() - 1));
                }

                ticketAndTaxe.setOther(otherAllFare);
                log.info("otherAllFare-2:" + otherAllFare.intValue());


                etTicketAlltaxGjMapper.insert(ticketAllTaxGJ);
                log.info("FlightRefundWithoutOrderServiceImpl - 4:" + fltRefund.getRefundNo());
                ticketList.add(t);

                ticketNoToTaxes.put(t.getTicketNo(), ticketAndTaxe);
                try {
                    DTOPassenger dtoPassenger = dtoPassengerList.get(i);
                    String secondFactorCode = "NM";
                    String secondFactorValue = dtoPassenger.getName();
                    // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                    secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                    // 双因素处理无陪儿童
                    secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                    DETRTKTResult result = ShowTicket.getInstance("JD").getResult2F(t.getIssCode() + t.getTicketNo(), secondFactorCode, secondFactorValue);
                    ibeTicketMsg.put(dtoTicket.getTicketNo(), result);
                    log.info("DETRTKTResult:" + t.getIssCode() + t.getTicketNo() + ":" + result);
                } catch (Exception e) {
                    log.error("FlightRefundWithoutOrderServiceImpl getTicketInfoByTktNo error:" + e.getMessage());
                }
            }

            Set<EtFltRefundPaxSeg> fltRefundPaxSegs = new HashSet<EtFltRefundPaxSeg>();
            Set<String> lineSet = new HashSet<String>();
            int i = 0;
            int segmentIndex = 1;
            //
            EtProduct productGjp = etProductMapper.selectOne(new QueryWrapper<EtProduct>()
                    .eq("code", "GJP")
                    .eq("airline_code", "JD")); //找个模拟产品

            log.info("FlightRefundWithoutOrderServiceImpl - 5:" + productGjp.getCode() + productGjp.getStatus() + fltRefund.getRefundNo());
            boolean hasChangedPassenger = true;
            for (DTOSegment dtoSegment : dtoSegmentList) {
                String line = dtoSegment.getDepCode() + dtoSegment.getArrCode();
                if (lineSet.contains(line)) {
                    i++;
                    hasChangedPassenger = true;
                    segmentIndex = 1; //换人之后航段顺序重置
                    lineSet.clear();//防治第二个人的第二段也重复，清空一下第一组航段
                }
                if (lineSet.size() == 0) {
                    hasChangedPassenger = true;
                } else {
                    hasChangedPassenger = false;
                }
                EtTicket t = ticketList.get(i);
                //保存航段信息
                EtSegment segment = SegmentMapper.getInstance().mapToEntity(dtoSegment);
                segment.setOrderId(1l);
                segment.setInventory(Inventory.IBE.toString());
                segment.setDepCode(dtoSegment.getDepCode());
                segment.setArrCode(dtoSegment.getArrCode());

                etSegmentMapper.insert(segment);

                //多个要退的航段 TODO
                EtPassengerSegment pax = new EtPassengerSegment();
                pax.setTicketId(t.getId());
                pax.setSegmentId(segment.getId());
                pax.setPassengerId(passengerList.get(i).getId());

                if (hasChangedPassenger) {
                    //国际票多段只有一个价格，价格全部放在第一段
                    pax.setMarketFare(ticketPrices.get(i));
                    pax.setNetFare(ticketPrices.get(i));
                    TicketAndTaxe ticketAndTaxe = ticketNoToTaxes.get(t.getTicketNo());
                    if (ticketAndTaxe != null && ticketAndTaxe.getCn() != null && ticketAndTaxe.getYq() != null) {
                        pax.setFuelTax(new BigDecimal(ticketAndTaxe.getYr()));
                        pax.setAirportTax(new BigDecimal(ticketAndTaxe.getCn()));
                        pax.setTax1(new BigDecimal(ticketAndTaxe.getYq()));//战险
                    } else {
                        pax.setFuelTax(BigDecimal.ZERO);
                        pax.setAirportTax(BigDecimal.ZERO);
                    }
                    pax.setOtherTaxes(taxPrices.get(i));//国际票的税临时存这里
                    pax.setTax2(ticketAndTaxe.getOther());
                    log.info("otherAllFare-3:" + pax.getTax2());
                } else {
                    pax.setMarketFare(BigDecimal.ZERO);
                    pax.setNetFare(BigDecimal.ZERO);
                    pax.setOtherTaxes(BigDecimal.ZERO);
                    pax.setFuelTax(BigDecimal.ZERO);
                    pax.setAirportTax(BigDecimal.ZERO);
                }


//                pax.setTicketStatus(TicketStatus.REFUNDED);

                if (ibeTicketMsg == null || ibeTicketMsg.size() == 0) {
                    pax.setStatus(SegmentStatus.NO.toString());
                } else {
                    DETRTKTResult detrtktResult = ibeTicketMsg.get(t.getTicketNo());
                    if (detrtktResult == null) {
                        pax.setStatus(SegmentStatus.NO.toString());
                    } else {
                        try {
                            for (Object airSeg : detrtktResult.getAirSeg()) {
                                log.error("-----" + airSeg.getClass());
                                DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                                log.error("-----" + detrtktSegment.getDepAirportCode() + dtoSegment.getDepCode());
                                log.error("-----" + detrtktSegment.getArrAirportCode() + dtoSegment.getArrCode());
                                if (detrtktSegment.getDepAirportCode().equals(dtoSegment.getDepCode())
                                        && detrtktSegment.getArrAirportCode().equals(dtoSegment.getArrCode())) {
                                    String ticketStatus = detrtktSegment.getTicketStatus();
                                    log.info("-----" + "FlightRefundWithoutOrderServiceImpl ticketStatus:" + ticketStatus + fltRefund.getRefundNo());

                                    //USED/FLOWN","REFUNDED","OPEN FOR USE","CHECKED IN","SUSPEND"

                                    if (ticketStatus != null && ticketStatus.indexOf("OPEN FOR USE") > -1) {
                                        pax.setTicketStatus(TicketStatus.OPEN_FOR_USE.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("REFUNDED") > -1) {
                                        pax.setTicketStatus(TicketStatus.REFUNDED.toString());
                                        pax.setStatus(SegmentStatus.XX.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("USED") > -1) {
                                        pax.setTicketStatus(TicketStatus.USED_OR_FLOWN.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else if (ticketStatus != null && ticketStatus.indexOf("CHECKED") > -1) {
                                        pax.setTicketStatus(TicketStatus.CHECKED_IN.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    } else {
                                        pax.setTicketStatus(TicketStatus.SUSPENDED.toString());
                                        pax.setStatus(SegmentStatus.RR.toString());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            pax.setStatus(SegmentStatus.NO.toString());
                        }
                    }
                }
                pax.setProductId(productGjp.getId());
                pax.setSequence(segmentIndex++);
                pax.setPoint(0l);
                pax.setCouponPrice(BigDecimal.ZERO);
                pax.setOldNetFare(BigDecimal.ZERO);

                if ("HWZ".equals(dtoFltRefund.getOrderSource())) {
                    pax.setCurrency(dtoPayment.getCurrency());
                }


                Set<EtPassengerSegment> passengerSegments = new HashSet<EtPassengerSegment>();
                passengerSegments.add(pax);
                etPassengerSegmentMapper.insert(pax);

                log.info("FlightRefundWithoutOrderServiceImpl - 6:" + fltRefund.getRefundNo());

                EtFltRefundPaxSeg fltRefundPaxSeg = new EtFltRefundPaxSeg(); //关联退票的航段
                fltRefundPaxSeg.setRefundAmount(BigDecimal.ZERO);
                fltRefundPaxSeg.setRefundId(fltRefund.getId());
                fltRefundPaxSeg.setPaxSegId(pax.getId());
                etFltRefundPaxSegMapper.insert(fltRefundPaxSeg);

                log.info("FlightRefundWithoutOrderServiceImpl - 7:" + fltRefund.getRefundNo());

                lineSet.add(line);
            }

            log.info("FlightRefundWithoutOrderServiceImpl - 8:" + fltRefund.getRefundNo());
            return fltRefund.getRefundNo();
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            throw new SpecialRefundException();
        }
    }


    /**
     * 保存国际票传过来的图片路径
     * @param refundNo
     * @param imgUrlList
     * @return
     */
    public void saveUploadImg(String refundNo, String[] imgUrlList) {

        List<EtRefundGjImg> refundGJImgList = new ArrayList<EtRefundGjImg>();
        EtFltRefundAudit newEtFltRefundAudit = new EtFltRefundAudit();

        List<EtFltRefund> fltRefundList = etFltRefundMapper.selectList(new QueryWrapper<EtFltRefund>().eq("refund_no", refundNo));
        if (fltRefundList == null || fltRefundList.size() == 0) {
            throw new SpecialRefundException("没有找到对应的退票单");
        }
        try {
            EtFltRefund etFltRefund = fltRefundList.get(0);
            List<EtFltRefundAudit> etFltRefundAuditList = etFltRefundAuditMapper.selectList(new QueryWrapper<EtFltRefundAudit>().eq("refund_id", etFltRefund.getId()));

            Collections.sort(etFltRefundAuditList, new Comparator<EtFltRefundAudit>() {
                public int compare(EtFltRefundAudit o1, EtFltRefundAudit o2) {
                    long diff = o1.getId() - o2.getId();
                    if (diff > 0) {
                        return 1;
                    }else if (diff < 0) {
                        return -1;
                    }
                    return 0;
                }
            });

            newEtFltRefundAudit = etFltRefundAuditList.get(0);

            // 保存用户上传图片信息
            for (String imgUrl : imgUrlList) {
                EtRefundGjImg refundGJImg = new EtRefundGjImg();
                refundGJImg.setRefundNo(etFltRefund.getRefundNo());
                refundGJImg.setFltRefundId(etFltRefund.getId());
                refundGJImg.setImgUrl(imgUrl);
                refundGJImg.setCreateTime(new Date());
                refundGJImg.setRefundAuditId(newEtFltRefundAudit.getId());
                refundGJImgList.add(refundGJImg);
            }
            etRefundGjImgService.saveBatch(refundGJImgList);
        } catch (Exception e) {
            e.printStackTrace();
            throw new SpecialRefundException("系统异常，请稍后再试");
        }
    }

    /**
     * 国际票、海外站退票审核状态查询
     * @param refundNo
     * @return
     */
    public Map<String, String> queryRefundOrderStatus(String refundNo) {
        Map<String, String> map = new HashMap<String, String>();
        EtFltRefund etFltRefund = etFltRefundMapper.selectOne(new QueryWrapper<EtFltRefund>().eq("refund_no", refundNo));
        BigDecimal refundAmount = new BigDecimal("0");
        BigDecimal allFare = new BigDecimal("0");

        if (etFltRefund != null) {
            String status = etFltRefund.getStatus();
            List<EtFltRefundPaxSeg> fltRefundPaxSegs = etFltRefundPaxSegMapper.getByRefundId(etFltRefund.getId());
            for (EtFltRefundPaxSeg fltRefundPaxSeg : fltRefundPaxSegs) {
                if (fltRefundPaxSeg.getActualRefundAmount() != null) {
                    refundAmount = refundAmount.add(fltRefundPaxSeg.getActualRefundAmount());
                }
                EtPassengerSegment ps = etPassengerSegmentMapper.selectById(fltRefundPaxSeg.getPaxSegId());

                if (ps.getNetFare() != null) {
                    allFare = allFare.add(ps.getNetFare());
                    if (ps.getOtherTaxes() != null) {
                        allFare = allFare.add(ps.getOtherTaxes());
                    }
                }
            }
            map.put("refundNo", refundNo);
            map.put("status", status);
            map.put("refundAmount", refundAmount.toString());
            BigDecimal fee = allFare.subtract(refundAmount);
            map.put("fee", fee.toString());
        }
        return map;
    }



    /**
     * 存在换开的情况，获取换开的原客票
     *
     * @param exchangedTicketNo
     * @return
     */
    private EtSpecialRefundOriginTkt handleSpecialRefundOriginTkt(String exchangedTicketNo, String passengerName, int segmentIndex) {
        if (StringUtils.hasText(exchangedTicketNo)) {
            try {
                String secondFactorCode = "NM";
                String secondFactorValue = passengerName;
                // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                // 双因素处理无陪儿童
                secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                DETRTKTResult result = ShowTicket.getInstance("JD").getResult2F(exchangedTicketNo, secondFactorCode, secondFactorValue);
                DETRHistoryResult detrHistoryResult = ShowTicket.getInstance("JD").getHistoryResult2F(exchangedTicketNo, secondFactorCode, secondFactorValue);

                log.debug("DETRTKTResult:" + exchangedTicketNo + ":" + result);

                EtSpecialRefundOriginTkt specialRefundOriginTkt = new EtSpecialRefundOriginTkt();

                for (Object airSeg : result.getAirSeg()) {
                    DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                    if (detrtktSegment.getSegmentIndex() == segmentIndex) {
                        // 对于 航班日期、航班号， 如果还没清编码，从票面取；如果已经清编码了，从票面历史中提取
                        if (StringUtils.hasText(detrtktSegment.getPnrNo())) {
                            specialRefundOriginTkt.setFlightNo(detrtktSegment.getFlightNo());
                            specialRefundOriginTkt.setDepTime(detrtktSegment.getDepTime());
                        } else {
                            for (int i1 = 0; i1 < detrHistoryResult.getInfoItem().size(); i1++) {
                                DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(i1);
                                if (detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getCabin() + "/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                    String[] item = detrHistoryInfoItem.getOperDesc().split("/")[0].split(" ");
                                    String flightNo = item[item.length - 1];
                                    String ibeFlightDate = new DateTime(QDateTime.stringToDate(detrHistoryInfoItem.getOperDesc().split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                                    specialRefundOriginTkt.setDepTime(new DateTime(ibeFlightDate).toDate());
                                    specialRefundOriginTkt.setFlightNo(flightNo.length() > 6 ? "" : flightNo);
                                    break;
                                }
                            }
                        }

                        double fare = getFareFromFC(detrtktSegment.getArrAirportCode(), result.getFareCompute());
                        specialRefundOriginTkt.setMarketFare(new BigDecimal(fare));
                        specialRefundOriginTkt.setNetFare(new BigDecimal(fare));
                        specialRefundOriginTkt.setFuelTax(new BigDecimal(getFuelTax(result) / result.getSegmentCount()));
                        specialRefundOriginTkt.setAirportTax(new BigDecimal(getAirportTax(result) / result.getSegmentCount()));
                        specialRefundOriginTkt.setSequence(detrtktSegment.getSegmentIndex());
                        specialRefundOriginTkt.setPassengerName(result.getPassengerName());
                        specialRefundOriginTkt.setDepCode(detrtktSegment.getDepAirportCode());
                        specialRefundOriginTkt.setArrCode(detrtktSegment.getArrAirportCode());
                        specialRefundOriginTkt.setOtherTaxes(specialRefundOriginTkt.getAirportTax().add(specialRefundOriginTkt.getFuelTax()));
                        specialRefundOriginTkt.setTax1(new BigDecimal(0));
                        specialRefundOriginTkt.setTax2(new BigDecimal(0));
                        specialRefundOriginTkt.setTax3(new BigDecimal(0));
                        specialRefundOriginTkt.setTax4(new BigDecimal(0));

                        if (result.getPassengerType() == 1 || result.getPassengerType() == 2) {
                            specialRefundOriginTkt.setPassengerType(PassengerType.CHILD.toString());
                        } else if (result.getPassengerType() == 3) {
                            specialRefundOriginTkt.setPassengerType(PassengerType.INFANT.toString());
                        } else {
                            specialRefundOriginTkt.setPassengerType(PassengerType.ADULT.toString());
                        }
                        specialRefundOriginTkt.setTicketNo(exchangedTicketNo);
                        specialRefundOriginTkt.setTicketStatus(TicketStatus.EXCHANGE.name());
                        return specialRefundOriginTkt;
                    }
                }
            } catch (Exception e) {
                log.error("FlightRefundWithoutOrderServiceHelper handleSpecialRefundOriginTkt error:" + e.getMessage());
            }
            return null;
        } else {
            return null;
        }
    }


    /**
     * 存在换开的情况，获取换开的原客票（国际票）
     * @param exchangedTicketNo
     * @return
     */
    private EtSpecialRefundOriginTkt handleSpecialRefundOriginTktGJ(String exchangedTicketNo, String passengerName, int segmentIndex, Map<String, String> originNetFareMap, Map<String, String> originTaxMap) {
        if (StringUtils.hasText(exchangedTicketNo)) {
            try {
                String secondFactorCode = "NM";
                String secondFactorValue = passengerName;
                // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
                secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
                // 双因素处理无陪儿童
                secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
                DETRTKTResult result = ShowTicket.getInstance("JD").getResult2F(exchangedTicketNo, secondFactorCode, secondFactorValue);
                DETRHistoryResult detrHistoryResult = ShowTicket.getInstance("JD").getHistoryResult2F(exchangedTicketNo, secondFactorCode, secondFactorValue);

                log.debug("DETRTKTResult:" + exchangedTicketNo + ":" + result);

                EtSpecialRefundOriginTkt specialRefundOriginTkt = new EtSpecialRefundOriginTkt();

                for (Object airSeg : result.getAirSeg()) {
                    DETRTKTSegment detrtktSegment = (DETRTKTSegment) airSeg;
                    if (detrtktSegment.getSegmentIndex() == segmentIndex) {
                        // 对于 航班日期、航班号， 如果还没清编码，从票面取；如果已经清编码了，从票面历史中提取
                        if (StringUtils.hasText(detrtktSegment.getPnrNo())) {
                            specialRefundOriginTkt.setFlightNo(detrtktSegment.getFlightNo());
                            specialRefundOriginTkt.setDepTime(detrtktSegment.getDepTime());
                        } else {
                            for (int i1 = 0; i1 < detrHistoryResult.getInfoItem().size(); i1++) {
                                DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(i1);
                                if (detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getCabin() + "/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                    String[] item = detrHistoryInfoItem.getOperDesc().split("/")[0].split(" ");
                                    String flightNo = item[item.length - 1];
                                    String ibeFlightDate = new DateTime(QDateTime.stringToDate(detrHistoryInfoItem.getOperDesc().split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                                    specialRefundOriginTkt.setDepTime(new DateTime(ibeFlightDate).toDate());
                                    specialRefundOriginTkt.setFlightNo(flightNo.length() > 6 ? "" : flightNo);
                                    break;
                                }
                            }
                        }

                        double fare = Double.parseDouble(originNetFareMap.get(result.getTicketNo()));
                        double airportTax = 0;
                        double fuelTax = 0;
                        double allTax = 0;

                        String originTaxMapStr = originTaxMap.get(result.getTicketNo());
                        if (StringUtils.hasText(originTaxMapStr)) {
                            for (String s : originTaxMapStr.split(",")) {
                                String key = s.split(":")[0];
                                String value = s.split(":")[1];
                                if ("CN".equals(key)) {
                                    airportTax = Double.parseDouble(value);
                                } else if ("YR".equals(key)) {
                                    fuelTax = Double.parseDouble(value);
                                }
                                allTax += Double.parseDouble(value);
                            }
                        }
                        specialRefundOriginTkt.setMarketFare(new BigDecimal(fare));
                        specialRefundOriginTkt.setNetFare(new BigDecimal(fare));
                        specialRefundOriginTkt.setFuelTax(new BigDecimal(fuelTax));
                        specialRefundOriginTkt.setAirportTax(new BigDecimal(airportTax));
                        specialRefundOriginTkt.setSequence(detrtktSegment.getSegmentIndex());
                        specialRefundOriginTkt.setPassengerName(result.getPassengerName());
                        specialRefundOriginTkt.setDepCode(detrtktSegment.getDepAirportCode());
                        specialRefundOriginTkt.setArrCode(detrtktSegment.getArrAirportCode());
                        specialRefundOriginTkt.setOtherTaxes(new BigDecimal(allTax));
                        specialRefundOriginTkt.setTax1(new BigDecimal(0));
                        specialRefundOriginTkt.setTax2(new BigDecimal(0));
                        specialRefundOriginTkt.setTax3(new BigDecimal(0));
                        specialRefundOriginTkt.setTax4(new BigDecimal(0));

                        if (result.getPassengerType() == 1 || result.getPassengerType() == 2) {
                            specialRefundOriginTkt.setPassengerType(PassengerType.CHILD.toString());
                        } else if (result.getPassengerType() == 3) {
                            specialRefundOriginTkt.setPassengerType(PassengerType.INFANT.toString());
                        } else {
                            specialRefundOriginTkt.setPassengerType(PassengerType.ADULT.toString());
                        }
                        specialRefundOriginTkt.setTicketNo(exchangedTicketNo);
                        specialRefundOriginTkt.setTicketStatus(TicketStatus.EXCHANGE.toString());
                        return specialRefundOriginTkt;
                    }
                }
            } catch (Exception e) {
                log.error("FlightRefundWithoutOrderServiceHelper handleSpecialRefundOriginTkt error:" + e.getMessage());
            }
            return null;
        } else {
            return null;
        }
    }

    /**
     * 是否存在票号
     *
     * @param ticketNo
     * @return
     */
    public boolean hasTicketRefund(String ticketNo) {
        QueryWrapper<EtTicket> ticketQueryWrapper = new QueryWrapper<>();
        ticketQueryWrapper.eq(StringUtils.hasText(ticketNo), "ticket_no", ticketNo);
        List<EtTicket> ticketList = this.etTicketMapper.selectList(ticketQueryWrapper);
        return ticketList != null && ticketList.size() > 0;
    }

    /**
     * 从票面 FC 项解析获取运价
     *
     * @param arrCode
     * @param fcText
     * @return
     */
    private double getFareFromFC(String arrCode, String fcText) {
        double fare = 0;
        try {
            String[] textList = fcText.trim().split(" ");

            String text = textList[textList.length - 1];
            if (text.startsWith(arrCode)) {
                // 回程
                fare = Double.valueOf(text.substring(3).split("CNY")[0]);
            } else {
                // 去程
                fare = Double.valueOf(text.split("CNY")[1].split("END")[0]) - Double.valueOf(text.substring(3).split("CNY")[0]);
            }
        } catch (Exception e) {
            log.info("fcText: " + fcText);
            e.printStackTrace();
        }

        return fare;
    }


    /**
     * 从票面解析获取机建费
     *
     * @param detrtktResult
     * @return
     */
    private double getAirportTax(DETRTKTResult detrtktResult) {
        for (int i = 0; i < detrtktResult.getTaxLength(); i++) {
            String taxCode = detrtktResult.getTaxCode(i);
            if ("CN".equals(taxCode)) {
                return detrtktResult.getTaxAmount(i);
            }
        }

        return 0;
    }


    /**
     * 从票面解析获取燃油费
     *
     * @param detrtktResult
     * @return
     */
    private double getFuelTax(DETRTKTResult detrtktResult) {
        for (int i = 0; i < detrtktResult.getTaxLength(); i++) {
            String taxCode = detrtktResult.getTaxCode(i);
            if ("YQ".equals(taxCode)) {
                return detrtktResult.getTaxAmount(i);
            }
        }

        return 0;
    }


    /**
     * 从 DETRHistory item 中获取出票时间
     *
     * @param ticketNo
     * @return
     */
    private Date getIssueDateFromDETRHistory(String ticketNo, String passengerName) {
        String secondFactorCode = "NM";
        String secondFactorValue = passengerName;
        // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
        secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
        // 双因素处理无陪儿童
        secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        DETRHistoryResult detrHistoryResult = ShowTicket.getInstance("JD").getHistoryResult2F(ticketNo, secondFactorCode, secondFactorValue);
        for (int i = 0; i < detrHistoryResult.getInfoItemNum(); i++) {
            DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(i);
            if ("TRMK".equals(detrHistoryInfoItem.getOperType())) {
                return detrHistoryInfoItem.getOperTime();
            }
        }
        return null;
    }

}

