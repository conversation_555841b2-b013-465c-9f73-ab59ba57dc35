package net.jdair.specialrefund.bizz.helper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.service.EtRefundGjImgService;
import net.jdair.specialrefund.bizz.utils.EncoderUtil;
import net.jdair.specialrefund.bizz.vo.DTOOrder;
import net.jdair.specialrefund.bizz.vo.SpecialRefundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Slf4j
public class FlightRefundSpecialHisDataSyncHelper {

    @Autowired
    private EtTsPGSMapper etTsPGSMapper;

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;
    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;
    @Autowired
    private EtPaymentMapper etPaymentMapper;
    @Autowired
    private EtFltRefundOutsidePaymentMapper etFltRefundOutsidePaymentMapper;
    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private EtPnrMapper etPnrMapper;

    @Autowired
    private EtPassengerMapper etPassengerMapper;

    @Autowired
    private EtTicketMapper etTicketMapper;

    @Autowired
    private EtSegmentMapper etSegmentMapper;

    @Autowired
    private EtPassengerSegmentMapper etPassengerSegmentMapper;

    @Autowired
    private EtSpecialRefundOriginTktMapper etSpecialRefundOriginTktMapper;

    @Autowired
    private EtFltRefundPaxSegMapper etFltRefundPaxSegMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtProductMapper etProductMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;



    /**
     * 根据退票单号，从PGS查询组织数据，入库
     *
     * @param
     * @return
     */
    @Transactional
    public void insertRefund(EtFltRefund etFltRefund,
                             List<EtFltRefundAudit> etFltRefundAuditList,
                             List<EtPayment> etPayPayList,
                             List<Customer> customerList,
                             List<EtPassenger> etPassengerList,
                             List<EtPnr> etPnrList,
                             List<EtFltRefundOutsidePayment> etFltRefundOutsidePaymentList,
                             List<EtTicket> etTicketList,
                             List<EtSegment> etSegmentList,
                             List<EtPassengerSegment> etPassengerSegmentList,
                             EtSpecialRefundOriginTkt etSpecialRefundOriginTkt,
                             List<EtFltRefundPaxSeg> etFltRefundPaxSegList,
                             EtSpecialRefundBankInfo etSpecialRefundBankInfo,
                             List<EtSpecialRefundImg> etSpecialRefundImgList) {
        String refundNo = etFltRefund.getRefundNo();
            log.info("ts history data sync: " + refundNo);

            Map<Long, Long> auditIdMaps = new HashMap<>();
            // 1.保存 et_flt_refund 表
            etFltRefund.setId(null);
            etFltRefundMapper.insert(etFltRefund);

            // 2.保存 et_flt_refund_audit 表
            for (EtFltRefundAudit etFltRefundAudit : etFltRefundAuditList) {
                Long pgsAuditId = etFltRefundAudit.getId();
                etFltRefundAudit.setId(null);
                etFltRefundAudit.setRefundId(etFltRefund.getId());
                etFltRefundAuditMapper.insert(etFltRefundAudit);
                auditIdMaps.put(pgsAuditId, etFltRefundAudit.getId());
            }

            // 3.保存 et_payment
            for (EtPayment etPayPay : etPayPayList) {
                etPayPay.setId(null);
                etPaymentMapper.insert(etPayPay);
            }

            // 4.保存 customer
            for (Customer customer : customerList) {
                Customer ce = customerMapper.selectOne(new QueryWrapper<Customer>()
                        .eq("name", customer.getName())
                        .eq("certificateno",customer.getCertificateno())
                        .eq("certificatetype", customer.getCertificatetype()));
                if (ce == null) {
                    customer.setId(null);
                    customerMapper.insert(customer);
                } else {
                    customer.setId(ce.getId());
                }
            }

            // 6. 保存 et_passenger
            for (int i = 0; i < etPassengerList.size(); i++) {
                EtPassenger etPassenger = etPassengerList.get(i);
                for (EtPnr etPnr : etPnrList) {
                    if (etPassenger.getPnrId() != null && etPassenger.getPnrId().equals(etPnr.getId())) {
                        // 5. 保存 et_pnr
                        etPnr.setId(null);
                        etPnrMapper.insert(etPnr);
                        etPassenger.setPnrId(etPnr.getId());
                    }
                }
                etPassenger.setId(null);
                etPassenger.setCustomerId(customerList.get(i).getId());
                etPassengerMapper.insert(etPassenger);
            }

            // 6.保存 et_flt_refund_outside_payment
            for (int i = 0; i < etFltRefundOutsidePaymentList.size(); i++) {
                EtFltRefundOutsidePayment outsidePayment = etFltRefundOutsidePaymentList.get(i);
                outsidePayment.setId(null);
                outsidePayment.setPaymentId(etPayPayList.get(i).getId());
                outsidePayment.setFltRefundId(etFltRefund.getId());
                etFltRefundOutsidePaymentMapper.insert(outsidePayment);
            }

            // 7.保存 et_ticket
            for (EtTicket etTicket : etTicketList) {
                EtTicket ticket = etTicketMapper.selectOne(new QueryWrapper<EtTicket>()
                        .eq("iss_code", etTicket.getIssCode())
                        .eq("ticket_no", etTicket.getTicketNo()));
                if (ticket == null) {
                    etTicket.setId(null);
                    etTicketMapper.insert(etTicket);
                } else {
                    etTicket.setId(ticket.getId());
                }
            }

            // 8.保存 et_segment
            for (EtSegment etSegment : etSegmentList) {
                etSegment.setId(null);
                etSegmentMapper.insert(etSegment);
            }

            EtProduct productTSTP = etProductMapper.selectOne(new QueryWrapper<EtProduct>()
                    .eq("code", "TSTP")
                    .eq("airline_code", "JD")); //找个模拟产品

            // 9.保存 et_passenger_segment
            for (int i = 0; i < etPassengerSegmentList.size(); i++) {
                EtPassengerSegment etPassengerSegment = etPassengerSegmentList.get(i);
                boolean hasOrigin = false;
                if (etSpecialRefundOriginTkt != null && etPassengerSegment.getId().equals(etSpecialRefundOriginTkt.getPaxSegId())) {
                    hasOrigin = true;
                }
                etPassengerSegment.setId(null);
                etPassengerSegment.setProductId(productTSTP.getId());
                etPassengerSegment.setSegmentId(etSegmentList.get(i).getId());
                etPassengerSegment.setPassengerId(etPassengerList.get(i).getId());
                etPassengerSegment.setTicketId(etTicketList.get(i).getId());
                etPassengerSegmentMapper.insert(etPassengerSegment);

                if (hasOrigin) {
                    // 10.保存 et_special_refund_origin_tkt
                    etSpecialRefundOriginTkt.setId(null);
                    etSpecialRefundOriginTkt.setPaxSegId(etPassengerSegment.getId());
                    etSpecialRefundOriginTktMapper.insert(etSpecialRefundOriginTkt);
                }
            }

            // 10.保存 et_flt_refund_pax_seg
            for (int i = 0; i < etFltRefundPaxSegList.size(); i++) {
                EtFltRefundPaxSeg etFltRefundPaxSeg = etFltRefundPaxSegList.get(i);
                etFltRefundPaxSeg.setId(null);
                etFltRefundPaxSeg.setRefundId(etFltRefund.getId());
                etFltRefundPaxSeg.setPaxSegId(etPassengerSegmentList.get(i).getId());
                etFltRefundPaxSegMapper.insert(etFltRefundPaxSeg);
            }

            // 11.保存 et_special_refund_bank_info
            etSpecialRefundBankInfo.setId(null);
            etSpecialRefundBankInfo.setFltRefundId(etFltRefund.getId());
            etSpecialRefundBankInfoMapper.insert(etSpecialRefundBankInfo);

            // 12.保存 et_special_refund_img
            for (EtSpecialRefundImg etSpecialRefundImg : etSpecialRefundImgList) {
                if (!etSpecialRefundImg.getFltRefundId().equals(0L)) {
                    etSpecialRefundImg.setFltRefundId(etFltRefund.getId());
                }
                etSpecialRefundImgMapper.insert(etSpecialRefundImg);
                for (Long auditId : auditIdMaps.keySet()) {
                    if (etSpecialRefundImg.getRefundAuditId().equals(auditId)) {
                        etSpecialRefundImg.setRefundAuditId(etSpecialRefundImg.getId());
                        etSpecialRefundImgMapper.updateById(etSpecialRefundImg);
                    }
                }
            }

    }


    /**
     * 根据退票单号，从PGS查询组织数据，入库
     *
     * @param
     * @return
     */
    @Transactional
    public void insertRefundPayment(Long newRefundId, List<EtFltRefundPayment> etFltRefundPaymentList, List<EtPayment> etRefundPayList) {

        for (EtFltRefundPayment etFltRefundPayment : etFltRefundPaymentList) {
            // 3.保存 et_payment
            for (EtPayment etRefundPay : etRefundPayList) {
                if (etFltRefundPayment.getPaymentId().equals(etRefundPay.getId())) {
                    etRefundPay.setId(null);
                    etPaymentMapper.insert(etRefundPay);
                    etFltRefundPayment.setPaymentId(etRefundPay.getId());
                    etFltRefundPayment.setFltRefundId(newRefundId);
                    etFltRefundPaymentMapper.insert(etFltRefundPayment);
                }
            }
        }

    }

}

