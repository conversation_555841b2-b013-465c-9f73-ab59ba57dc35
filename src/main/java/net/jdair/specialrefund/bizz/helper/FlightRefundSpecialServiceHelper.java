package net.jdair.specialrefund.bizz.helper;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.util.QDateTime;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.controller.backend.domain.RefundAmountReq;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.feign.JdIbeFeignClient;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.DateUtil;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import net.jdair.specialrefund.bizz.utils.PNRManage;
import net.jdair.specialrefund.bizz.utils.SpecialRefundImgMapper;
import net.jdair.specialrefund.bizz.utils.pay.PayService;
import net.jdair.specialrefund.bizz.utils.sms.SmsServiceUtil;
import net.jdair.specialrefund.bizz.vo.*;
import net.jdair.specialrefund.common.exception.GlobalException;
import net.jdair.specialrefund.common.response.CodeRefund;
import net.jdair.specialrefund.common.response.RestResponse;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Executable;
import java.math.BigDecimal;
import java.util.*;


@Component
@Slf4j
public class FlightRefundSpecialServiceHelper {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;

    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;

    @Autowired
    private EtSpecialRefundImgMapper etSpecialRefundImgMapper;

    @Autowired
    private EtSpecialRefundBankInfoMapper etSpecialRefundBankInfoMapper;

    @Autowired
    private JdIbeFeignClient jdIbeFeignClient;

    @Autowired
    private SmsServiceUtil smsServiceUtil;

    @Autowired
    private FlightRefundServiceHelper flightRefundServiceHelper;

    @Autowired
    private PayService payService;

    @Autowired
    private SpecialRefundReportMapper specialRefundReportMapper;

    @Autowired
    private EtFltRefundPaymentMapper etFltRefundPaymentMapper;

    @Autowired
    private EtPaymentMapper etPaymentMapper;

    @Autowired
    private EtPaymentNoGeneratorMapper etPaymentNoGeneratorMapper;

    /**
     * 根据票号从 IBE detr 票面，放到 List<DTOOrder> (都弄成单人单段的)返回前台
     *
     * @param ticketNo
     * @param passengerName
     * @param flightDate
     * @return
     */
    public List<DTOOrder> generateDTOOrderByTicketNo(String ticketNo, String passengerName, String flightDate) {
        List<DTOOrder> dtoOrderList = new ArrayList<DTOOrder>();
        try {
            log.info("ibe detrTicket by ticketNo: " + ticketNo);
            //addOpRecord(AuthUtils.getAuthUser().getUsername(), "detrTicket:" + ticketNo);
            DETR detr = new DETR();
            DETR2F detr2f = new DETR2F();
            PNRManage.configIBEClient(detr, "JD");

            /**
             * 组织双因素。
             * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
             * CN,PNR记录编码，票面任一CPN的任一记录编码均可
             * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
             * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
             * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
             * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
             * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
             * etArea - N: current DB only，现行数据库中的客票 H: history DB only 历史数据库的客票，对应指令带,P选项 “”: distill the et in history DB automatically while not found in current DB. 首先尝试提取现行数据库的此票号数据，如在现行数据库找到此客票，无论可以提取数据或无权限提取， 则不再查询历史库，如未找到则再尝试历史库提取此票号数据，均未找到则抛出相应信息的异常
             */
            String secondFactorCode = "NM";
            String secondFactorValue = passengerName;
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
            DETRTKTResult detrtktResult = null;
            DETRHistoryResult detrHistoryResult = null;
            try {
                detrtktResult = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
                detrHistoryResult = detr2f.getTicketHistoryByTktNo2F(ticketNo, "N", secondFactorCode, secondFactorValue);
            } catch (Exception e) {
                // 当前库中没有，即客票已进历史
                if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                    detrtktResult = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "H", secondFactorCode, secondFactorValue);
                    detrHistoryResult = detr2f.getTicketHistoryByTktNo2F(ticketNo, "H", secondFactorCode, secondFactorValue);
                }
            }

            if (null != detrtktResult && detrtktResult.getSegmentCount() > 0 && null != detrtktResult.getSegment(0)) {
                if (StringUtils.hasText(passengerName) && !passengerName.equals(detrtktResult.getPassengerName())) {
                    return null;
                }

                DTOOrder dtoOrder = new DTOOrder();
                DTOPassenger dtoPassenger = new DTOPassenger();
                if (detrtktResult.getPassengerType() == 1 || detrtktResult.getPassengerType() == 2) {
                    dtoPassenger.setPassengerType(PassengerType.CHILD);
                } else if (detrtktResult.getPassengerType() == 3) {
                    dtoPassenger.setPassengerType(PassengerType.INFANT);
                } else {
                    dtoPassenger.setPassengerType(PassengerType.ADULT);
                }
                dtoPassenger.setName(detrtktResult.getPassengerName());
                // dtoPassenger.setCertificateNo();
                // dtoPassenger.setCertificateType();
                List<DTOPaxSegment> dtoPaxSegmentList = new ArrayList<DTOPaxSegment>();
                List<DTOSegment> dtoSegmentList = new ArrayList<DTOSegment>();

                // 获取原客票信息
                DETRTKTResult originDetrtktResult = null;
                DETRHistoryResult originDetrHistoryResult = null;
                if (StringUtils.hasText(detrtktResult.getExchangeInfo())) {
                    try {
                        originDetrtktResult = detr2f.getTicketInfoByTktNo2F(detrtktResult.getExchangeInfo(), false, "", "N", secondFactorCode, secondFactorValue);
                        originDetrHistoryResult = detr2f.getTicketHistoryByTktNo2F(detrtktResult.getExchangeInfo(), "N", secondFactorCode, secondFactorValue);
                    } catch (Exception e) {
                        // 当前库中没有，即客票已进历史
                        if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                            originDetrtktResult = detr2f.getTicketInfoByTktNo2F(detrtktResult.getExchangeInfo(), false, "", "H", secondFactorCode, secondFactorValue);
                            originDetrHistoryResult = detr2f.getTicketHistoryByTktNo2F(detrtktResult.getExchangeInfo(), "H", secondFactorCode, secondFactorValue);
                        }
                    }
                }

                for (int i = 0; i < detrtktResult.getSegmentCount(); i++) {
                    DTOSegment dtoSegment = new DTOSegment();
                    DTOPaxSegment dtoPaxSegment = new DTOPaxSegment();
                    DTOTicket dtoTicket = new DTOTicket();
                    DTOPnr dtoPnr = new DTOPnr();

                    if (detrtktResult.getETicketType() == 10) {
                        // "AIRLINE DOMESTIC";
                        dtoOrder.setInternational(0);
                        dtoTicket.setBspType(1);
                    } else if (detrtktResult.getETicketType() == 11) {
                        // "AIRLINE INTERNATIONAL";
                        dtoOrder.setInternational(1);
                        dtoTicket.setBspType(1);
                    } else if (detrtktResult.getETicketType() == 12) {
                        // "BSP DOMESTIC";
                        dtoOrder.setInternational(0);
                        dtoTicket.setBspType(2);  // 用 2 表示 BSP 票
                    } else if (detrtktResult.getETicketType() == 13) {
                        // "BSP INTERNATIONAL";
                        dtoOrder.setInternational(1);
                        dtoTicket.setBspType(2);  // 用 2 表示 BSP 票
                    }
                    dtoTicket.setTicketNo(detrtktResult.getTicketNo());
                    dtoTicket.setIssuedDate(detrHistoryResult.getIssueDate());
                    try {
                        String officeNo = "";
                        for (int k = detrHistoryResult.getInfoItem().size() - 1; k >= 0; k--) {
                            DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(k);
                            if ("TRMK".equals(detrHistoryInfoItem.getOperType())) {
                                String[] item = detrHistoryInfoItem.getOperDesc().split("\\+");
                                if (item.length > 1) {
                                    officeNo = item[1];
                                }
                            }
                        }
                        dtoTicket.setIssuedBy(officeNo);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    DETRTKTSegment detrtktSegment = detrtktResult.getSegment(i);
                    // String tmpFlightDate = detrtktResult.getFareCompute().split(" ")[1];

                    // 对于 航班日期、航班号， 如果还没清编码，从票面取；如果已经清编码了，从票面历史中提取
                    if (StringUtils.hasText(detrtktSegment.getPnrNo()) || (detrtktSegment.getDepTime() != null && StringUtils.hasText(detrtktSegment.getFlightNo()))) {
                        dtoPaxSegment.setDepTime((Date) detr.fixDepartureDate(detrtktResult, detrHistoryResult).get(i));
                        dtoPaxSegment.setFlightNo(detrtktSegment.getFlightNo());
                        dtoSegment.setDepTime(detrtktSegment.getDepTime());
                        dtoSegment.setFlightNo(detrtktSegment.getFlightNo());
                    } else {
                        for (int i1 = detrHistoryResult.getInfoItem().size() - 1; i1 >= 0; i1--) {
                            boolean findEOTU = false;
                            boolean findCKIN = false;
                            boolean findNFMT = false;
                            DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(i1);
                            // 有换开的场景 EOTU CHG FLT FROM JD5562/28SEP20/U/HAKXIY TO JDOPEN/OPEN/U/HAKXIY
                            if (originDetrtktResult != null) {
                                if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                    findEOTU = true;
                                }
                                if (!findEOTU) {
                                    if ("CKIN".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode())) {
                                        findCKIN = true;
                                    }
                                    if ("NFMT".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                        findNFMT = true;
                                    }
                                }
                            } else if ("USED/FLOWN".equals(detrtktSegment.getTicketStatus()) || "OPEN FOR USE".equals(detrtktSegment.getTicketStatus())) {  // 客票已使用 CKIN O/C JD5277/26JUL20/U/HAKTNA
                                if ("CKIN".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode())) {
                                    findCKIN = true;
                                }
                                if (!findCKIN) {
                                    if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                        findEOTU = true;
                                    }
                                }
                            } else {
                                if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                    findEOTU = true;
                                } else if (dtoOrder.getInternational() == 1) {
                                    if ("NFMT".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                        findNFMT = true;
                                    }
                                } else if ("CKIN".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode())) {
                                    findCKIN = true;
                                }
                            }
                            if (findEOTU || findNFMT) {
                                String[] item = detrHistoryInfoItem.getOperDesc().split("/")[0].split(" ");
                                String flightNo = item[item.length - 1];
                                String ibeFlightDate = new DateTime(QDateTime.stringToDate(detrHistoryInfoItem.getOperDesc().split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                                dtoPaxSegment.setDepTime(new DateTime(ibeFlightDate).toDate());
                                dtoPaxSegment.setFlightNo(flightNo);
                                dtoSegment.setDepTime(new DateTime(ibeFlightDate).toDate());
                                dtoSegment.setFlightNo(flightNo);
//                            if (StringUtils.hasText(flightDate) && !ibeFlightDate.equals(flightDate) && dtoOrder.getInternational() == 0) {
//                                continue;
//                            }
                                break;
                            } else if (findCKIN) {
                                String[] item = detrHistoryInfoItem.getOperDesc().split(" ");
                                String itemStr = item[item.length - 1];
                                String flightNo = itemStr.split("/")[0];
                                String ibeFlightDate = new DateTime(QDateTime.stringToDate(itemStr.split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                                dtoPaxSegment.setDepTime(new DateTime(ibeFlightDate).toDate());
                                dtoPaxSegment.setFlightNo(flightNo);
                                dtoSegment.setDepTime(new DateTime(ibeFlightDate).toDate());
                                dtoSegment.setFlightNo(flightNo);
//                            if (StringUtils.hasText(flightDate) && !ibeFlightDate.equals(flightDate) && dtoOrder.getInternational() == 0) {
//                                continue;
//                            }
                                break;
                            }
                        }
                    }

                    // String ibeFlightDate = new DateTime(QDateTime.stringToDate(tmpFlightDate.substring(0, tmpFlightDate.length() - 3), "DDMMMYY")).toString("yyyy-MM-dd");

                    dtoSegment.setDepCode(detrtktSegment.getDepAirportCode());
                    dtoSegment.setArrCode(detrtktSegment.getArrAirportCode());
                    dtoSegment.setArrTime(null);
                    dtoSegment.setCabinClass(String.valueOf(detrtktSegment.getCabin()));
                    dtoSegment.setAirlineCode(detrtktSegment.getAirline());
                    dtoSegment.setCabinPrice(detrtktResult.getFare());
                    dtoSegment.setStop(detrtktSegment.getStopType()); // TODO ?

                    if ("OPEN FOR USE".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.OPEN_FOR_USE);
                    } else if ("VOID".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.VOID);
                    } else if ("REFUNDED".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.REFUNDED);
                    } else if ("CHECKED IN".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.CHECKED_IN);
                    } else if ("FIM EXCH".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.FIM_OR_EXCHANGE);
                    } else if ("EXCHANGED".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.EXCHANGE);
                    } else if ("USED/FLOWN".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.USED_OR_FLOWN);
                    } else if ("SUSPENDED".equals(detrtktSegment.getTicketStatus())) {
                        dtoPaxSegment.setTicketStatus(TicketStatus.SUSPENDED);
                    }

                    dtoPaxSegment.setTicketIssued(dtoTicket);

                    dtoPaxSegment.setDepCode(detrtktSegment.getDepAirportCode());
                    dtoPaxSegment.setArrCode(detrtktSegment.getArrAirportCode());
                    dtoPaxSegment.setCabinClass(String.valueOf(detrtktSegment.getCabin()));
                    dtoPaxSegment.setAirlineCode(detrtktSegment.getAirline());

                    // 国际票不从 FC 项取票面价、税费
                    if (dtoOrder.getInternational() != 1) {
                        dtoPaxSegment.setAirportTax(getAirportTax(detrtktResult) / detrtktResult.getSegmentCount());
                        dtoPaxSegment.setFuelTax(getFuelTax(detrtktResult) / detrtktResult.getSegmentCount());
                        dtoPaxSegment.setOtherTaxes(dtoPaxSegment.getAirportTax() + dtoPaxSegment.getFuelTax());

                        double fare = getFareFromFC(detrtktSegment.getArrAirportCode(), detrtktSegment.getAirline(), detrtktResult.getFareCompute());
                        dtoPaxSegment.setNetFare(fare);
                        dtoPaxSegment.setMarketFare(fare);
                    }

                    // 设置原票信息
                    if (originDetrtktResult != null) {
                        for (int j = 0; j < originDetrtktResult.getSegmentCount(); j++) {
                            DETRTKTSegment originDetrtktSegment = originDetrtktResult.getSegment(j);
//                            if (originDetrtktSegment.getDepAirportCode().equals(detrtktSegment.getDepAirportCode())
//                                    && originDetrtktSegment.getArrAirportCode().equals(detrtktSegment.getArrAirportCode())) {
                            DTOSpecialRefundOriginTkt specialRefundOriginTkt = new DTOSpecialRefundOriginTkt();
                            specialRefundOriginTkt.setAirlineCode(originDetrtktSegment.getAirline());
                            specialRefundOriginTkt.setDepCode(originDetrtktSegment.getDepAirportCode());
                            specialRefundOriginTkt.setArrCode(originDetrtktSegment.getArrAirportCode());
                            specialRefundOriginTkt.setCabinClass(String.valueOf(originDetrtktSegment.getCabin()));
                            specialRefundOriginTkt.setFlightNo(originDetrtktSegment.getFlightNo());
                            specialRefundOriginTkt.setDepTime((Date) detr.fixDepartureDate(originDetrtktResult, originDetrHistoryResult).get(j));
                            specialRefundOriginTkt.setArrTime(originDetrtktSegment.getArrTime());
                            specialRefundOriginTkt.setSequence(originDetrtktSegment.getSegmentIndex());
                            specialRefundOriginTkt.setPassengerName(originDetrtktResult.getPassengerName());
                            specialRefundOriginTkt.setTax1(new BigDecimal(0));
                            specialRefundOriginTkt.setTax2(new BigDecimal(0));
                            specialRefundOriginTkt.setTax3(new BigDecimal(0));
                            specialRefundOriginTkt.setTax4(new BigDecimal(0));

                            // 国际票不从 FC 项取票面价、税费
                            if (dtoOrder.getInternational() != 1) {
                                specialRefundOriginTkt.setFuelTax(new BigDecimal(getFuelTax(originDetrtktResult) / originDetrtktResult.getSegmentCount()));
                                specialRefundOriginTkt.setAirportTax(new BigDecimal(getAirportTax(originDetrtktResult) / originDetrtktResult.getSegmentCount()));
                                specialRefundOriginTkt.setOtherTaxes(specialRefundOriginTkt.getAirportTax().add(specialRefundOriginTkt.getFuelTax()));

                                double originFare = getFareFromFC(originDetrtktSegment.getArrAirportCode(), originDetrtktSegment.getAirline(), originDetrtktResult.getFareCompute());
                                specialRefundOriginTkt.setMarketFare(new BigDecimal(originFare));
                                specialRefundOriginTkt.setNetFare(new BigDecimal(originFare));
                            }

                            if (originDetrtktResult.getPassengerType() == 1 || originDetrtktResult.getPassengerType() == 2) {
                                specialRefundOriginTkt.setPassengerType(PassengerType.CHILD.toString());
                            } else if (originDetrtktResult.getPassengerType() == 3) {
                                specialRefundOriginTkt.setPassengerType(PassengerType.INFANT.toString());
                            } else {
                                specialRefundOriginTkt.setPassengerType(PassengerType.ADULT.toString());
                            }
                            specialRefundOriginTkt.setTicketNo(originDetrtktResult.getTicketNo());
                            specialRefundOriginTkt.setTicketStatus(TicketStatus.EXCHANGE);

                            try {
                                String officeNo = "";
                                for (int k = originDetrHistoryResult.getInfoItem().size() - 1; k >= 0; k--) {
                                    DETRHistoryInfoItem detrHistoryInfoItem = originDetrHistoryResult.getInfoItem(k);
                                    if ("TRMK".equals(detrHistoryInfoItem.getOperType())) {
                                        String[] item = detrHistoryInfoItem.getOperDesc().split("\\+");
                                        if (item.length > 1) {
                                            officeNo = item[1];
                                        }
                                    }
                                }
                                specialRefundOriginTkt.setOfficeNo(officeNo);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            dtoPaxSegment.setDtoSpecialRefundOriginTkt(specialRefundOriginTkt);
                            break;
//                            }
                        }
                    }

                    if (StringUtils.hasText(detrtktSegment.getPnrNo())) {
                        dtoPnr.setPnrNo(detrtktSegment.getPnrNo());
                        dtoPassenger.setPnrValue(dtoPnr);
                    }

                    dtoSegmentList.add(dtoSegment);
                    dtoPaxSegmentList.add(dtoPaxSegment);

                    dtoPassenger.setPassengerSegmentList(dtoPaxSegmentList);
                }

                List<DTOPassenger> dtoPassengerList = new ArrayList<DTOPassenger>();
                dtoPassengerList.add(dtoPassenger);
                dtoOrder.setPassengerList(dtoPassengerList);
                dtoOrder.setSegmentList(dtoSegmentList);
                dtoOrderList.add(dtoOrder);
            }
            return dtoOrderList;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                throw new SpecialRefundException("票号不存在", e);
            }
            throw new SpecialRefundException("系统异常");
        }
    }


    /**
     * 根据票号从 IBE detr 指定旅客姓名的票面，放到 DTOPaxSegment 返回前台
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @return
     */
    public List<DTOOrder> generateDTOOrderByTicketNo(String ticketNo, String passengerName) {
        return generateDTOOrderByTicketNo(ticketNo, passengerName, null);
    }

    /**
     * 根据票号从 IBE detr 票面，放到 DTOPaxSegment 返回前台
     *
     * @param ticketNo 票号
     * @return
     */
    public List<DTOOrder> generateDTOOrderByTicketNo(String ticketNo) {
        return generateDTOOrderByTicketNo(ticketNo, null, null);
    }


    /**
     * 根据身份证号/护照号从 IBE detr 指定旅客姓名、航班日期的票面，放到 List<DTOOrder> （组织为单人单段的）返回前台
     *
     * @param idCardNo
     * @param passengerName
     * @param flightDate
     * @return
     */
    public List<DTOOrder> generateDTOOrderByIdCardNo(String idCardNo, String passengerName, String flightDate, String certType) {
        List<DTOOrder> dtoOrderList = new ArrayList<DTOOrder>();
        try {
            log.info("ibe detrTicket by idCardNo: " + idCardNo);

            if (!StringUtils.hasText(certType)) {
                certType = "NI";
            }

            DETR detr = new DETR();
            PNRManage.configIBEClient(detr, "JD");
            // IBE 按证件号提取票号列表
            Vector<String> ticketNoList = detr.getTicketListByCert(certType, idCardNo, false, "JD", null, 1);

            if (ticketNoList != null && ticketNoList.size() > 0) {
                for (String ticketNo : ticketNoList) {
                    try {
                        List<DTOOrder> tempList = this.generateDTOOrderByTicketNo(ticketNo, passengerName, flightDate);
                        if (tempList != null && tempList.size() > 0) {
                            dtoOrderList.addAll(tempList);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            }
            return dtoOrderList;
        } catch (Exception e) {
            e.printStackTrace();
            return dtoOrderList;
        }
    }

    /**
     * 根据身份证号从 IBE detr 指定旅客姓名的票面，放到 List<DTOOrder> （组织为单人单段的）返回前台
     *
     * @param idCardNo
     * @param passengerName
     * @return
     */
    public List<DTOOrder> generateDTOOrderByIdCardNo(String idCardNo, String passengerName, String flightDate) {
        return generateDTOOrderByIdCardNo(idCardNo, passengerName, flightDate, null);
    }

    /**
     * 根据身份证号从 IBE detr 指定旅客姓名的票面，放到 List<DTOOrder> （组织为单人单段的）返回前台
     *
     * @param idCardNo
     * @param passengerName
     * @return
     */
    public List<DTOOrder> generateDTOOrderByIdCardNo(String idCardNo, String passengerName) {
        return generateDTOOrderByIdCardNo(idCardNo, passengerName, null, null);
    }

    /**
     * 根据身份证号从 IBE detr 票面，放到 List<DTOOrder>（组织为单人单段的）返回前台
     *
     * @param idCardNo
     * @return
     */
    public List<DTOOrder> generateDTOOrderByIdCardNo(String idCardNo) {
        return generateDTOOrderByIdCardNo(idCardNo, null, null, null);
    }


    /**
     * 从票面 FC 项解析获取运价
     *
     * @param arrCode
     * @param fcText
     * @return
     */
    private double getFareFromFC(String arrCode, String airlineCode, String fcText) {
        double fare = 0;
        try {
            String[] textList = fcText.trim().split(" ");

            String text = textList[textList.length - 1];

            if ("Y8".equals(airlineCode) && text.startsWith("Y8")) {
                text = text.substring(2);
            }

            if (text.startsWith(arrCode)) {
                // 回程
                fare = Double.valueOf(text.substring(3).split("CNY")[0]);
            } else {
                // 去程
                fare = Double.valueOf(text.split("CNY")[1].split("END")[0]) - Double.valueOf(text.substring(3).split("CNY")[0]);
            }
        } catch (Exception e) {
            log.info("fcText: " + fcText);
            e.printStackTrace();
        }

        return fare;
    }


    /**
     * 从票面解析获取机建费
     *
     * @param detrtktResult
     * @return
     */
    private double getAirportTax(DETRTKTResult detrtktResult) {
        for (int i = 0; i < detrtktResult.getTaxLength(); i++) {
            String taxCode = detrtktResult.getTaxCode(i);
            if ("CN".equals(taxCode)) {
                return detrtktResult.getTaxAmount(i);
            }
        }

        return 0;
    }


    /**
     * 从票面解析获取燃油费
     *
     * @param detrtktResult
     * @return
     */
    private double getFuelTax(DETRTKTResult detrtktResult) {
        for (int i = 0; i < detrtktResult.getTaxLength(); i++) {
            String taxCode = detrtktResult.getTaxCode(i);
            if ("YQ".equals(taxCode)) {
                return detrtktResult.getTaxAmount(i);
            }
        }

        return 0;
    }

    /**
     * 根据换开了的票号查询换开到的票号
     *
     * @param exchangedTktNo
     * @return
     */
    public String getNewTktNoByExchangedTktNo(String exchangedTktNo, String passengerName) {
        return getNewTktNoByExchangedTktNo(exchangedTktNo, passengerName, 1);
    }

    private String getNewTktNoByExchangedTktNo(String exchangedTktNo, String passengerName, int limitCount) {
        try {
            if (limitCount > 2) {
                return "";
            }
            DETR2F detr2f = new DETR2F();
            PNRManage.configIBEClient(detr2f, "JD");
            String secondFactorCode = "NM";
            String secondFactorValue = passengerName;
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
            DETRHistoryResult detrHistoryResult = null;
            try {
                detrHistoryResult = detr2f.getTicketHistoryByTktNo2F(exchangedTktNo, "N", secondFactorCode, secondFactorValue);
            } catch (Exception e) {
                // 当前库中没有，即客票已进历史
                if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                    detrHistoryResult = detr2f.getTicketHistoryByTktNo2F(exchangedTktNo, "H", secondFactorCode, secondFactorValue);
                }
            }
            for (int i1 = detrHistoryResult.getInfoItem().size() - 1; i1 >= 0; i1--) {
                DETRHistoryInfoItem detrHistoryInfoItem = detrHistoryResult.getInfoItem(i1);
                // 有换开的场景 EOTU CHG FLT FROM JD5562/28SEP20/U/HAKXIY TO JDOPEN/OPEN/U/HAKXIY
                if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().startsWith("O/E")) {
                    String item[] = detrHistoryInfoItem.getOperDesc().split(" ");
                    String newTicketNo = item[item.length - 1];
                    DETRTKTResult detrtktResult = null;
                    try {
                        detrtktResult = detr2f.getTicketInfoByTktNo2F(newTicketNo, false, "", "N", secondFactorCode, secondFactorValue);
                    } catch (Exception e) {
                        // 当前库中没有，即客票已进历史
                        if (e.getMessage().contains("ET PASSENGER DATA NOT FOUND")) {
                            detrtktResult = detr2f.getTicketInfoByTktNo2F(newTicketNo, false, "", "H", secondFactorCode, secondFactorValue);
                        }
                    }
                    for (int j = 0; j < detrtktResult.getSegmentCount(); j++) {
                        DETRTKTSegment detrtktSegment = detrtktResult.getSegment(j);
                        if (detrtktSegment.getTicketStatus().equals("EXCHANGED")) {
                            limitCount++;
                            return getNewTktNoByExchangedTktNo(newTicketNo, passengerName, limitCount);
                        } else if (detrtktSegment.getTicketStatus().equals("OPEN FOR USE") && newTicketNo.startsWith("898")) {
                            return newTicketNo;
                        }
                    }
                }

            }
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    /**
     * 一审拒绝后，用户可以发起再次审核申请，退票单状态变为（审核中（NEW））
     *
     * @param dtoFltRefund
     * @param dtoSpecialRefundImgList
     * @param remark
     */
    public void submitAuditAgain(EtFltRefund dtoFltRefund, List<DTOSpecialRefundImg> dtoSpecialRefundImgList, String remark) {
        try {

            List<EtFltRefund> fltRefundList = etFltRefundMapper.selectList(new QueryWrapper<EtFltRefund>().eq("refund_no", dtoFltRefund.getRefundNo()));
            if (fltRefundList == null || fltRefundList.size() == 0) {
                throw new SpecialRefundException("没有该退单号");
            }

            EtFltRefund fltRefund = fltRefundList.get(0);
            //只有 一审拒绝 的状态，可以重新提交
            if (!fltRefund.getStatus().equals(DoAuditing.FREJECT.getValue())) {
                throw new SpecialRefundException("非拒绝状态，无法提交");
            }

            fltRefund.setStatus("NEW"); // 更新退票单状态为【审核中】

            //审核记录
            EtFltRefundAudit audit = new EtFltRefundAudit();
            audit.setSubmitTime(new Date());
            audit.setAuditTime(new Date());
            audit.setRefundId(fltRefund.getId());
            audit.setSubmitUsername(dtoFltRefund.getUserName());
            audit.setNotion(remark);
            audit.setAction("FIRSTAUDIT");
            etFltRefundAuditMapper.insert(audit);

            etFltRefundMapper.updateById(fltRefund);
            log.info("FlightRefundWithoutOrderServiceImpl.submitAuditAgain - 1:" + fltRefund.getRefundNo());

            // 保存用户上传图片信息
            for (DTOSpecialRefundImg dtoSpecialRefundImg : dtoSpecialRefundImgList) {
                dtoSpecialRefundImg.setRefundNo(fltRefund.getRefundNo());
                dtoSpecialRefundImg.setRefundAuditId(audit.getId());
                dtoSpecialRefundImg.setFltRefundId(fltRefund.getId());
            }
            List<EtSpecialRefundImg> specialRefundImgList = SpecialRefundImgMapper.getInstance().mapToEntities(dtoSpecialRefundImgList);
            for (EtSpecialRefundImg etSpecialRefundImg : specialRefundImgList) {
                etSpecialRefundImgMapper.insert(etSpecialRefundImg);
            }
            log.info("FlightRefundWithoutOrderServiceImpl.createSpecialFltRefund - 10:" + fltRefund.getRefundNo());

        } catch (Exception e) {
            e.printStackTrace();
            throw new SpecialRefundException(e.getMessage());
        }
    }

    /**
     * 退票清位处理
     *
     * @param refundNo
     */
    public void noPnrAndRefundTicket(String refundNo) {
        log.info("start noPnrAndRefundTicket, refundNo: {}", refundNo);
        SearchRefundOrderListReq req = new SearchRefundOrderListReq();
        req.setRefundNo(refundNo);
        List<EtFltRefund> etFltRefundList = etFltRefundMapper.findRefundByParams(req);
        if (etFltRefundList != null) {
            log.info("start handle ticketInfo...");
            TicketAndPnrClearRequest tmpRequest = new TicketAndPnrClearRequest();
            for (EtFltRefund etFltRefund : etFltRefundList) {
                List<TicketAndPnrClearRequest.TicketInfo> ticketInfoList = new ArrayList<>();
                for (EtPassengerSegment etPassengerSegment : etFltRefund.getEtPassengerSegmentList()) {
                    log.info("start handle passengerSegment...");
                    TicketAndPnrClearRequest.TicketInfo ticketInfo = new TicketAndPnrClearRequest.TicketInfo();
                    ticketInfo.setIssCode(etPassengerSegment.getEtTicket().getIssCode());
                    ticketInfo.setTicketNo(etPassengerSegment.getEtTicket().getTicketNo());
                    TicketAndPnrClearRequest.Seg seg = new TicketAndPnrClearRequest.Seg();
                    seg.setDepCode(etPassengerSegment.getEtSegment().getDepCode());
                    seg.setArrCode(etPassengerSegment.getEtSegment().getArrCode());
                    ticketInfo.setPassengerName(etPassengerSegment.getEtPassenger().getCustomer().getName());
                    ticketInfo.setSegList(Lists.newArrayList(seg));
                    ticketInfoList.add(ticketInfo);
                }
                tmpRequest.setTicketInfoList(ticketInfoList);
            }

            Map<String, TicketAndPnrClearRequest.TicketInfo> ticketInfoMap = new HashMap<>();

            for (TicketAndPnrClearRequest.TicketInfo ticketInfo : tmpRequest.getTicketInfoList()) {
                String key = ticketInfo.getIssCode() + "-" + ticketInfo.getTicketNo();
                if (ticketInfoMap.containsKey(key)) {
                    TicketAndPnrClearRequest.TicketInfo existingTicketInfo = ticketInfoMap.get(key);
                    existingTicketInfo.getSegList().addAll(ticketInfo.getSegList());
                } else {
                    ticketInfoMap.put(key, ticketInfo);
                }
            }

            TicketAndPnrClearRequest processedRequest = new TicketAndPnrClearRequest();
            processedRequest.setTicketInfoList(new ArrayList<>(ticketInfoMap.values()));

            try {
                log.info("start invoke ibe feign, req: {}", JSON.toJSONString(processedRequest));
                jdIbeFeignClient.ticketAndPnrClear(processedRequest);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 修改银行卡号
     *
     * @param refundNo    退票单号
     * @param toRealName  是否改成微信实名
     * @param newCardNo   新银行卡号
     * @param newBankName 新开户行名称
     * @param newBankName 新持卡人姓名
     */
    public void changeBankCardNo(String refundNo, boolean toRealName, String newCardNo, String newBankName, String newCardHolder) {
        try {
            SearchRefundOrderListReq req = new SearchRefundOrderListReq();
            req.setRefundNo(refundNo);
            List<EtFltRefund> fltRefundList = etFltRefundMapper.findRefundByParams(req);
            if (fltRefundList == null || fltRefundList.size() == 0) {
                throw new SpecialRefundException("没有该退单号");
            }

            EtFltRefund fltRefund = fltRefundList.get(0);
            // 前台旅客订单为“审核中”和“审核拒绝”状态时，可修改
            if (!fltRefund.getStatus().equals(DoAuditing.NEW.getValue())
                    && !fltRefund.getStatus().equals(DoAuditing.FPASS.getValue())
                    && !fltRefund.getStatus().equals(DoAuditing.REJECT.getValue())
                    && !fltRefund.getStatus().equals(DoAuditing.FREJECT.getValue())) {
                throw new SpecialRefundException("已进入退款环节，无法进行修改");
            }

            boolean hasChild = false;
            for (EtPassengerSegment etPassengerSegment : fltRefund.getEtPassengerSegmentList()) {
                if (PassengerType.CHILD.name().equals(etPassengerSegment.getEtPassenger().getPassengerType())) {
                    hasChild = true;
                    break;
                }
            }

            EtSpecialRefundBankInfo specialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(new QueryWrapper<EtSpecialRefundBankInfo>().eq("refund_no", refundNo));

            // 成人不支持修改持卡人姓名(国内)
            if ("TS".equals(fltRefund.getOrderSource()) && !hasChild) {
                if (!specialRefundBankInfo.getCardHolder().equals(newCardHolder)) {
                    throw new SpecialRefundException("暂不支持修改持卡人姓名");
                }
            }

            // 如果不是改成微信实名，校验是否为票面姓名（国内）
            if ("TS".equals(fltRefund.getOrderSource()) && !toRealName) {
                for (EtPassengerSegment etPassengerSegment : fltRefund.getEtPassengerSegmentList()) {
                    if (!newCardHolder.equals(etPassengerSegment.getEtPassenger().getCustomer().getName())) {
                        throw new SpecialRefundException("无法修改持卡人姓名!");
                    }
                }
            }

            specialRefundBankInfo.setCardNo(newCardNo);
            specialRefundBankInfo.setBankName(newBankName);
            specialRefundBankInfo.setCardHolder(newCardHolder);
            etSpecialRefundBankInfoMapper.updateById(specialRefundBankInfo);
        } catch (SpecialRefundException se) {
            throw se;
        } catch (Exception e) {
            throw new SpecialRefundException("系统异常");
        }
    }


    /**
     * 特殊退票,【审核中】、【一审拒绝】、【二审拒绝】状态的退单，增加 “关闭退票单” 的操作按钮
     *
     * @return
     * @throws Exception
     */
    public void closeOrderForSpecialRefund(CloseSpecialOrderReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getRefundId());
            log.info("closeOrderForSpecialRefund: " + req.getRefundId() + " ," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (!StringUtils.hasText(req.getRemark())) {
                throw new SpecialRefundException("备注信息不能为空");
            }

            if (DoAuditing.NEW.toString().equals(etFltRefund.getStatus())
                    || DoAuditing.FREJECT.toString().equals(etFltRefund.getStatus())
                    || DoAuditing.REJECT.toString().equals(etFltRefund.getStatus())
            ) {
                EtFltRefundAudit auditing = new EtFltRefundAudit();
                auditing.setAction(DoAuditing.SECONDAUDIT.getValue());
                auditing.setId(null);
                auditing.setRefundId(etFltRefund.getId());
                auditing.setAuditUsername(req.getLoginUserName());
                auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
                auditing.setNotion(req.getRemark());
                auditing.setAuditResult(DoAuditing.CLOSED.getValue());
                // 增加审核记录
                etFltRefundAuditMapper.insert(auditing);
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.CLOSED.toString());
                etFltRefundMapper.updateById(etFltRefund);
            } else {
                throw new SpecialRefundException("退票单状态非【审核中】、【一审拒绝】、【二审拒绝】");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票,【已关闭】状态的退单，增加 “重新开启退票单” 的操作按钮
     *
     * @return
     * @throws Exception
     */
    public void reOpenOrderForSpecialRefund(ReopenSpecialOrderReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getRefundId());
            log.info("reOpenOrderForSpecialRefund: " + req.getLoginUserName() + "," + req.getRefundId() + "," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (DoAuditing.CLOSED.toString().equals(etFltRefund.getStatus())) {
                EtFltRefundAudit auditing = new EtFltRefundAudit();

                auditing.setAction(DoAuditing.SECONDAUDIT.getValue());
                auditing.setId(null);
                auditing.setRefundId(etFltRefund.getId());
                auditing.setAuditUsername(req.getLoginUserName());
                auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
                auditing.setNotion("重新开启退票单");
                auditing.setAuditResult(DoAuditing.NEW.getValue());
                // 增加审核记录
                etFltRefundAuditMapper.insert(auditing);
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.NEW.toString());
                etFltRefundMapper.updateById(etFltRefund);
            } else {
                throw new SpecialRefundException("退票单状态非【已关闭】");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票,【退款接口处理中】、【退款处理中】、【退款成功】状态的退单，增加 “修改退票单为二审通过” 的操作按钮
     *
     * @return
     * @throws Exception
     */
    public void changeToPassForSpecialRefund(ChangeToPassReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getRefundId());
            log.info("changeToPassForSpecialRefund: " + req.getLoginUserName() + "," + req.getRefundId() + "," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (PayStatus.PENDICING.toString().equals(etFltRefund.getStatus())
                    || DoAuditing.PEND.toString().equals(etFltRefund.getStatus())
                    || DoAuditing.PAID.toString().equals(etFltRefund.getStatus())
            ) {
                EtFltRefundAudit auditing = new EtFltRefundAudit();

                auditing.setAction(DoAuditing.SECONDAUDIT.getValue());
                auditing.setId(null);
                auditing.setRefundId(req.getRefundId());
                auditing.setAuditUsername(req.getLoginUserName());
                auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
                auditing.setNotion("修改退票单状态为二审通过");
                auditing.setAuditResult(DoAuditing.PASS.getValue());
                // 增加审核记录
                etFltRefundAuditMapper.insert(auditing);
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.PASS.toString());
                etFltRefundMapper.updateById(etFltRefund);
            } else {
                throw new SpecialRefundException("退票单状态非【退款接口处理中】、【退款处理中】、【退款成功】");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票,撤回为一审通过
     *
     * @return
     * @throws Exception
     */
    public void changeToFPassForSpecialRefund(ChangeToFPassReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getRefundId());
            log.info("changeToFPassForSpecialRefund: " + req.getLoginUserName() + "," + req.getRefundId() + "," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (DoAuditing.PASS.toString().equals(etFltRefund.getStatus())) {
                EtFltRefundAudit auditing = new EtFltRefundAudit();

                auditing.setAction(DoAuditing.SECONDAUDIT.getValue());
                auditing.setId(null);
                auditing.setRefundId(req.getRefundId());
                auditing.setAuditUsername(req.getLoginUserName());
                auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
                auditing.setNotion("二审撤回");
                auditing.setAuditResult(DoAuditing.FPASS.getValue());
                // 增加审核记录
                etFltRefundAuditMapper.insert(auditing);
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.FPASS.toString());
                etFltRefundMapper.updateById(etFltRefund);
            } else {
                throw new SpecialRefundException("退票单状态非二审通过");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票,驳回
     * 特殊退票退款失败驳回为一审拒绝，同时给旅客发短信：
     * 尊敬的旅客，您好，您提交的898-XXXXXXXXXX特殊退票由于“（采集驳回备注栏信息） ”原因导致退款失败，建议您根据要求重新提交，感谢您对我司工作的支持。
     *
     * @return
     * @throws Exception
     */
    public void refundFailRejectForSpecialRefund(RefundFailRejectReq req) {
        try {
            EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(req.getRefundId());
            log.info("refundFailRejectForSpecialRefund: " + req.getLoginUserName() + "," + req.getRefundId() + "," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (DoAuditing.FAIL.toString().equals(etFltRefund.getStatus())) {
                EtFltRefundAudit auditing = new EtFltRefundAudit();

                auditing.setAction(DoAuditing.SECONDAUDIT.getValue());
                auditing.setId(null);
                auditing.setRefundId(req.getRefundId());
                auditing.setAuditUsername(req.getLoginUserName());
                auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
                auditing.setNotion(req.getSpecialRefundRejectReason());
                auditing.setAuditResult(DoAuditing.FREJECT.getValue());
                // 增加审核记录
                etFltRefundAuditMapper.insert(auditing);
                String currentRefundStatus = etFltRefund.getStatus();
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.FREJECT.toString());
                etFltRefundMapper.updateById(etFltRefund);

                etFltRefund.setStatus(currentRefundStatus);
                etFltRefund.setRemark(req.getSpecialRefundRejectReason());
                // 特殊退票 - "退款成功"发短信
                smsServiceUtil.sendMessage4SpecialRefund(etFltRefund, DoAuditing.FREJECT);
            } else {
                throw new SpecialRefundException("退票单状态非二审通过");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票,转线下银行付款
     *
     * @return
     * @throws Exception
     */
    public void makeOfflineForSpecialRefund(MakeOfflineReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getRefundId());
            log.info("makeOfflineForSpecialRefund: " + req.getLoginUserName() + "," + req.getRefundId() + "," + etFltRefund.getStatus());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            if (DoAuditing.PASS.toString().equals(etFltRefund.getStatus()) || DoAuditing.FAIL.toString().equals(etFltRefund.getStatus())) {
                // 更新退票单状态为【已关闭】
                etFltRefund.setStatus(DoAuditing.WAIT_OFFLINE.toString());
                etFltRefundMapper.updateById(etFltRefund);
            } else {
                throw new SpecialRefundException("退票单状态非二审通过或退款失败");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 特殊退票, 修改银行卡信息
     *
     * @return
     * @throws Exception
     */
    public void changeBankInfo(ChangeBankInfoReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getFltRefundId());
            if (etFltRefund == null) {
                throw new SpecialRefundException("没有该退单号");
            }
            log.info("changeBankInfo: " + req.getLoginUserName() + "," + req.getFltRefundId());

            // 除“二审通过”、“退款成功”两种状态以外的其他退单,管理员可修改银行卡信息
            if (etFltRefund.getStatus().equals(DoAuditing.PASS.getValue())
                    || etFltRefund.getStatus().equals(DoAuditing.PAID.getValue())) {
                throw new SpecialRefundException("该状态无法进行修改");
            }

            EtSpecialRefundBankInfo specialRefundBankInfo = etSpecialRefundBankInfoMapper.selectOne(new QueryWrapper<EtSpecialRefundBankInfo>().eq("flt_refund_id", req.getFltRefundId()));

            EtFltRefundAudit audit = new EtFltRefundAudit();

            audit.setSubmitTime(new Date());
            audit.setAuditTime(new Date());
            audit.setRefundId(etFltRefund.getId());
            audit.setSubmitUsername(etFltRefund.getUserName());
            audit.setAuditUsername(req.getLoginUserName());
            audit.setNotion(getChangeBankInfoRemark(specialRefundBankInfo, req));
            audit.setAction("FIRSTAUDIT");
            audit.setAuditResult(null);
            // 增加审核记录
            etFltRefundAuditMapper.insert(audit);

            specialRefundBankInfo.setCardNo(req.getCardNo());
            specialRefundBankInfo.setBankName(req.getBankName());
            specialRefundBankInfo.setCardHolder(req.getCardHolder());
            specialRefundBankInfo.setMobileNo(req.getMobileNo());
            etSpecialRefundBankInfoMapper.update(specialRefundBankInfo, new QueryWrapper<EtSpecialRefundBankInfo>().eq("flt_refund_id", req.getFltRefundId()));

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }


    /**
     * 特殊退票, 批量在线退款
     *
     * @return
     * @throws Exception
     */
    public void batchRefundOnlineTS(BatchRefundOnlineTSReq req) {
        try {
            boolean allPASSFlag = true;
            List<EtFltRefund> dtoFltRefundList = new ArrayList<EtFltRefund>();

            for (Long refundId : req.getRefundIds()) {
                EtFltRefund etFltRefund = flightRefundServiceHelper.findRefundDetailById(refundId);
                dtoFltRefundList.add(etFltRefund);
                if (!etFltRefund.getStatus().equals("PASS")) {
                    log.info(etFltRefund.getRefundNo() + " is not PASS");
                    allPASSFlag = false;
                }
            }

            if (allPASSFlag) {
                for (EtFltRefund refund : dtoFltRefundList) {
                    String paymentNo = "";
                    String tradeNoString = "";
                    List<EtPayment> payments = refund.getEtRefundPaymentList();
                    if (payments != null && payments.size() > 0) {
                        if (refund.getOrderSource().equals("TS") || refund.getOrderSource().equals("TSGJ")) {
                            // 允许多次退款，判断与上次退款发起时间的间隔不能小于 72h
                            Collections.sort(payments, new Comparator<EtPayment>() {
                                public int compare(EtPayment arg0, EtPayment arg1) {
                                    return arg1.getPayTime().compareTo(arg0.getPayTime());
                                }
                            });

                            EtPayment payment = payments.get(0);

                            if (DateUtil.timeTotalMinutes(new Date(), payment.getPayTime()) < 72 * 60) {
                                throw new SpecialRefundException("此次退款据上次操作在线退款时间不满足72小时间隔限制，请稍后重试");
                            } else {
                                // 查询易生退款结果
                                try {
                                    JSONObject jsonObject = flightRefundServiceHelper.queryEasycardDFRefundResult(payment.getPaymentNo());
                                    JSONObject response = jsonObject.getJSONObject("trade_acc_dsfpay_query_response");
                                    String resResult = JSONObject.toJSONString(response);
                                    String tradeStatus = JSON.parseObject(resResult).getString("trade_status");
                                    String code = JSON.parseObject(resResult).getString("code");
                                    if ("00".equals(code) && "SUCCESS".equals(tradeStatus)) {
                                        throw new SpecialRefundException("此订单已成功进行过一次退款，退款流水：" + payment.getPaymentNo());
                                    }
                                } catch (Exception e) {
                                    throw new SpecialRefundException("查询易生退款结果异常，请稍后重试");
                                }
                            }

                        } else {
                            throw new GlobalException(CodeRefund.REFUNDED_EXCEPTION, null);
                        }
                    }

                    //支付来源
                    String source = "";

                    List<EtPayment> dtoPaymentList = refund.getEtOutsidePaymentList();
                    if (dtoPaymentList == null || dtoPaymentList.size() != 1) {
                        throw new GlobalException(CodeRefund.NO_PAID_RECORD_EXCEPTION, null);
                    }

                    //支付类型
                    PayType payType = null;
                    Date payTime = new Date();
                    for (EtPayment payment : refund.getEtOutsidePaymentList()) {
                        if (PayStatus.PAID.toString().equals(payment.getPayStatus())) {
                            paymentNo = payment.getPaymentNo();
                            tradeNoString = payment.getPaymentNo();
                            //获取支付类型
                            payType = PayType.valueOfAlias(payment.getPayType());
                            source = payment.getSource();
                            payTime = payment.getPayTime();
                            break;
                        }
                    }

                    if ("".equals(paymentNo)) {
                        throw new GlobalException(CodeRefund.NO_PAID_RECORD_EXCEPTION, null);
                    }

                    /**
                     * 组装参数
                     */
                    EtPayment dtoPayment = new EtPayment();
                    dtoPayment.setPaymentNo(paymentNo);
                    dtoPayment.setRefundId(refund.getId());
                    dtoPayment.setPayer(req.getLoginUserName());
                    dtoPayment.setPayType(payType.toString());
                    dtoPayment.setAction(PayAction.REFUND.name());
                    dtoPayment.setPayTime(payTime);
                    //支付来源,主要是渠道来源
                    dtoPayment.setSource(source);

                    log.info("refund money online.paymentNo:" + paymentNo + ",refundId:" + refund.getId() + ",payType:" + payType.toString() + ",paymentSource:" + source + ",oper:" + req.getLoginUserName());
                    log.info("Begin to refund | paymentNo = " + paymentNo);
                    Map payResult = payService.onlineRefund(refund, dtoPayment, payType);
                    log.info("mars refund end | returnResult = " + payResult.get("returnResult"));
                    PaymentLogStatus returnResult = PaymentLogStatus.FAILED;
                    if (payResult.get("returnResult") != null) {
                        returnResult = (PaymentLogStatus) payResult.get("returnResult");
                    }

                    if (PaymentLogStatus.SUCCESS != returnResult) {
                        String errorMessage = "退票单号为 " + refund.getRefundNo() + " 的订单在线退款失败，支付类型为 " + (payType == null ? "" : payType.toString());
                        log.error("TS 退票单失败，refundId" + refund.getId() + "的订单在线退款失败");
                        throw new SpecialRefundException(errorMessage);
                    } else {
                        if (refund.getOrderSource().equals("TS") || refund.getOrderSource().equals("TSGJ")) {
                            // 特殊退票 - "退款成功"发短信
                            smsServiceUtil.sendMessage4SpecialRefund(refund, DoAuditing.PAID);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }


    /**
     * 特殊退票, 修改退单类型
     *
     * @return
     * @throws Exception
     */
    public void changeRefundTypeForSpecialRefund(ChangeRefundTypeReq req) {
        try {
            EtFltRefund etFltRefund = etFltRefundMapper.selectById(req.getId());
            log.info("changeRefundTypeForSpecialRefund: " + req.getLoginUserName() + ", " + req.getId());

            if (!"TS".equals(etFltRefund.getOrderSource()) && !"TSGJ".equals(etFltRefund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单");
            }

            ResourceBundle refundBundle = ResourceBundle.getBundle("pgs-refund");

            EtFltRefundAudit auditing = new EtFltRefundAudit();

            auditing.setAction(DoAuditing.CHANGE_REFUND_TYPE.getValue());
            auditing.setId(null);
            auditing.setRefundId(req.getId());
            auditing.setAuditUsername(req.getLoginUserName());
            auditing.setAuditTime(DateUtils.convertOracleDate(DateUtils.convertOracleDate(new Date())));
            auditing.setAuditResult(null);

            String oldRefundTypeStr = "", oldRefundReasonStr = "", newRefundTypeStr = "", newRefundReasonStr = "";
            try {
                oldRefundTypeStr = refundBundle.getString(etFltRefund.getRefundType());
            } catch (Exception e) {

            }
            try {
                oldRefundReasonStr = refundBundle.getString(etFltRefund.getConstraintReason());
            } catch (Exception e) {

            }
            try {
                newRefundTypeStr = refundBundle.getString(req.getRefundType());
            } catch (Exception e) {

            }
            try {
                newRefundReasonStr = refundBundle.getString(req.getConstraintReason());
            } catch (Exception e) {

            }
            auditing.setNotion("修改退票类型: 【" + oldRefundTypeStr + "】->【" + newRefundTypeStr + "】, 修改退票原因: 【" + oldRefundReasonStr + "】->【" + newRefundReasonStr + "】");

            // 增加审核记录
            etFltRefundAuditMapper.insert(auditing);
            // 更新退票单 退票类型和退票原因
            etFltRefund.setRefundType(req.getRefundType());
            etFltRefund.setConstraintReason(req.getConstraintReason());
            etFltRefundMapper.updateById(etFltRefund);


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SpecialRefundException(StatusCode.SYSTEM_ERROR.getMsg());
        }
    }


    /**
     * 线下退款
     *
     * @param req
     * @return
     */
    @Transactional
    public void refundAmountOffline(RefundAmountReq req) {
        try {
            log.info("RefundAmountOffline  userId:" + req.getLoginId() + "|refundId:" + req.getRefundId());
            EtFltRefund refund = flightRefundServiceHelper.findRefundDetailById(req.getRefundId());

            if (!"TS".equals(refund.getOrderSource()) && !"TSGJ".equals(refund.getOrderSource())) {
                throw new SpecialRefundException("非特殊退票订单！");
            }

            if (!"PASS".equals(refund.getStatus()) && !"WAIT_OFFLINE".equals(refund.getStatus()) && !"FAIL".equals(refund.getStatus())) {
                throw new SpecialRefundException("二审通过/待线下付款/退款失败的订单才能退款！");
            }

            EtFltRefundAudit dtoRefundAudit = new EtFltRefundAudit();
            dtoRefundAudit.setRefundId(refund.getId());
            dtoRefundAudit.setAuditTime(new Date()); // 退款操作时间
            dtoRefundAudit.setNotion(req.getRemark());  // 备注信息
            dtoRefundAudit.setAction(DoAuditing.OFFLINE.getValue());  // 线下退款action
            dtoRefundAudit.setAuditResult(DoAuditing.PAID.getValue());  // 审核结果：退款成功
            dtoRefundAudit.setAuditUsername(req.getLoginId());  // 操作人
            dtoRefundAudit.setSubmitTime(new Date());
            dtoRefundAudit.setSubmitUsername(req.getLoginId());

            // 先记录退款的操作审核信息
            etFltRefundAuditMapper.insert(dtoRefundAudit);

            // 线下退款
            refund.setPayMode(DoAuditing.OFFLINE.getValue());
            refund.setPayTime(DateUtils.convertOracleDate(new Date()));
            refund.setStatus(DoAuditing.PAID.getValue());
            etFltRefundMapper.updateById(refund);

            // long seqNo = etPaymentMapper.generatePaymentNoSeq();
            EtPaymentNoGenerator etPaymentNoGenerator = etPaymentNoGeneratorMapper.selectOne(new QueryWrapper<EtPaymentNoGenerator>()
                    .eq("refund_no", refund.getRefundNo())
                    .eq("payment_no_status", "WAIT_USE")
            );
            etPaymentNoGenerator.setPaymentNoStatus("USED");
            etPaymentNoGenerator.setUsedTime(new Date());
            etPaymentNoGeneratorMapper.updateById(etPaymentNoGenerator);

            // 特殊退票 - "退款成功"发短信
            smsServiceUtil.sendMessage4SpecialRefund(refund, DoAuditing.PAID);
        } catch (Exception e) {
            throw new SpecialRefundException(e.getMessage());
        }
    }


    public String queryOnlineRefundStatus(QueryOnlineRefundStatusReq req) {
        String refundPaymentNo = "";
        String result = "";

        try {
            EtFltRefund refund = etFltRefundMapper.selectById(req.getRefundId());
            List<EtFltRefundPayment> refundPaymentMapList = etFltRefundPaymentMapper.selectList(new QueryWrapper<EtFltRefundPayment>().eq("flt_refund_id", refund.getId()));
            List<EtPayment> refundPaymentList = new ArrayList<>();
            for (EtFltRefundPayment map : refundPaymentMapList) {
                EtPayment etPayment = etPaymentMapper.selectById(map.getPaymentId());
                refundPaymentList.add(etPayment);
            }
            EtPayment payment = new EtPayment();
            if (refundPaymentList != null && refundPaymentList.size() > 0) {
                // 存在多笔退款记录，倒序排列，取最近一条退款
                Collections.sort(refundPaymentList, new Comparator<EtPayment>() {
                    public int compare(EtPayment arg0, EtPayment arg1) {
                        return arg1.getPayTime().compareTo(arg0.getPayTime());
                    }
                });
                payment = refundPaymentList.get(0);
                if (PayAction.REFUND.name().equals(payment.getAction()) && ("TS".equals(payment.getSource()) || "TSGJ".equals(payment.getSource()))) {
                    refundPaymentNo = payment.getPaymentNo();
                }
            }
            if (StringUtils.hasText(refundPaymentNo)) {
                if (DoAuditing.OFFLINE.getValue().equals(refund.getPayMode())) {
                    return "退款方式为线下退款，非易生代付退款";
                }

                boolean callbackResult = flightRefundServiceHelper.refundCallbackEasycardDF("", true, refundPaymentNo);
                log.info("query online refund refundPaymentNo: " + refundPaymentNo + ", result: SUCCESS");
                JSONObject jsonResult = flightRefundServiceHelper.queryEasycardDFRefundResult(refundPaymentNo);
                JSONObject response = jsonResult.getJSONObject("trade_acc_dsfpay_query_response");
                String msg = JSONObject.toJSONString(response);
                return "易生代付退款接口返回结果：" + msg;

            } else {
                log.error("query online refund refundPaymentNo: " + refundPaymentNo + ", 系统异常，退款流水号不存在");
                return "系统异常，退款流水号不存在";
            }
        } catch (Exception e) {
            log.error("query online refund error refundPaymentNo: " + refundPaymentNo + ", refundId:" + req.getRefundId() + ", err:" + e.getMessage(), e);
            return "在线退款结果异常，请稍后再试！";
        }
    }

    /**
     * 特殊退票报表查询
     * @param req
     * @return
     */
    public List<DTOSpecialRefundReport> queryReport(SearchRefundReportQueryReq req) {
        List<DTOSpecialRefundReport> list = specialRefundReportMapper.querySpecialRefundReport(req);
        return list;
    }


    /**
     * 生成修改银行卡信息时的备注内容
     *
     * @param oldSpecialRefundBankInfo
     * @param req
     * @return
     */
    private String getChangeBankInfoRemark(EtSpecialRefundBankInfo oldSpecialRefundBankInfo, ChangeBankInfoReq req) {
        StringBuffer remarkMessage = new StringBuffer("修改银行卡信息-");
        if (!oldSpecialRefundBankInfo.getCardHolder().equals(req.getCardHolder())) {
            remarkMessage.append("修改账户姓名、");
        }
        if (!oldSpecialRefundBankInfo.getCardNo().equals(req.getCardNo())) {
            remarkMessage.append("修改银行卡号、");
        }
        if (!oldSpecialRefundBankInfo.getBankName().equals(req.getBankName())) {
            remarkMessage.append("修改开户行名称、");
        }
        if (!oldSpecialRefundBankInfo.getMobileNo().equals(req.getMobileNo())) {
            remarkMessage.append("修改联系电话、");
        }
        return remarkMessage.toString().substring(0, remarkMessage.length() - 1);
    }


    /**
     * @throws
     * @Title: getErrorJsonResult
     * @Description: 获取请求失败的JSONResult对象
     * @param: @param msg
     * @param: @return
     * @return: JSONResult
     */
    public JSONResult getErrorJsonResult(StatusCode statusCode) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(statusCode.getCode(), statusCode.getMsg()));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

    public JSONResult getErrorJsonResult(String code, String message) {
        JSONResult r = new JSONResult();
        r.setError(new ErrorMessage(code, message));
        r.setStatus(StatusCode.FAIL.getMsg());
        return r;
    }

}

