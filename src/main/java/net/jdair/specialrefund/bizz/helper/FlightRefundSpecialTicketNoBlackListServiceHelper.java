package net.jdair.specialrefund.bizz.helper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.jdair.specialrefund.bizz.domain.EtTsTicketnoBlacklist;
import net.jdair.specialrefund.bizz.mapper.EtTsTicketnoBlacklistMapper;
import net.jdair.specialrefund.bizz.utils.SpecialRefundTicketNoBlackListMapper;
import net.jdair.specialrefund.bizz.vo.DTOSpecialRefundTicketNoBlackList;
import net.jdair.specialrefund.bizz.vo.SpecialRefundTicketNoBlackListStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 特殊退票票号黑名单管理服务
 */

@Component
@Slf4j
public class FlightRefundSpecialTicketNoBlackListServiceHelper {

    @Autowired
    private EtTsTicketnoBlacklistMapper etTsTicketnoBlacklistMapper;


    /**
     * @param ticketNo
     * @description 按票号查询黑名单
     */
    public List<DTOSpecialRefundTicketNoBlackList> findByTicketNoAndStatus(String ticketNo, SpecialRefundTicketNoBlackListStatus status) {
        List<EtTsTicketnoBlacklist> specialRefundTicketNoBlackLists = etTsTicketnoBlacklistMapper.selectList(new QueryWrapper<EtTsTicketnoBlacklist>()
                .eq("ticket_no", ticketNo)
                .eq("status", status)
        );
        if (specialRefundTicketNoBlackLists.size() > 0) {
            return SpecialRefundTicketNoBlackListMapper.getInstance().mapToValues(specialRefundTicketNoBlackLists);
        } else {
            return null;
        }
    }

    /**
     * @param dtoSpecialRefundTicketNoBlackList
     * @description 新增或修改黑名单
     */
    public void addOrUpdate(DTOSpecialRefundTicketNoBlackList dtoSpecialRefundTicketNoBlackList) {
        if (dtoSpecialRefundTicketNoBlackList.getId() != null) {
            EtTsTicketnoBlacklist specialRefundTicketNoBlackList = etTsTicketnoBlacklistMapper.selectById(dtoSpecialRefundTicketNoBlackList.getId());
            specialRefundTicketNoBlackList.setTicketNo(dtoSpecialRefundTicketNoBlackList.getTicketNo());
            specialRefundTicketNoBlackList.setOperator(dtoSpecialRefundTicketNoBlackList.getOperator());
            specialRefundTicketNoBlackList.setStatus(dtoSpecialRefundTicketNoBlackList.getStatus());
            etTsTicketnoBlacklistMapper.updateById(specialRefundTicketNoBlackList);
        } else {
            EtTsTicketnoBlacklist specialRefundTicketNoBlackList = SpecialRefundTicketNoBlackListMapper.getInstance().mapToEntity(dtoSpecialRefundTicketNoBlackList);
            specialRefundTicketNoBlackList.setId(null);
            specialRefundTicketNoBlackList.setStatus(SpecialRefundTicketNoBlackListStatus.ACTIVE.name());
            specialRefundTicketNoBlackList.setCreateDate(new Date());
            etTsTicketnoBlacklistMapper.insert(specialRefundTicketNoBlackList);
        }
    }

    /**
     * @param id
     * @description 删除黑名单
     */
    public void delete(Long id) {
        EtTsTicketnoBlacklist specialRefundTicketNoBlackList = etTsTicketnoBlacklistMapper.selectById(id);
        etTsTicketnoBlacklistMapper.deleteById(specialRefundTicketNoBlackList);
    }

}
