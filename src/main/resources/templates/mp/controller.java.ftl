package ${package.Controller};


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.Api;
import io.swagger.annotations.*;
import net.jdair.specialrefund.common.response.RestResponse;
import ${package.Service}.${table.serviceName};
import ${package.Entity}.${entity};
<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>
import javax.annotation.Resource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;



/**
 * <p>
 * ${table.comment!} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 * @version v1.0
 */
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@Api(value = "${table.name} 服务接口")
@RequestMapping("<#if controllerMappingHyphenStyle??>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
    <#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
    <#else>
public class ${table.controllerName} {
    </#if>

    @Resource
    private ${table.serviceName} ${table.serviceName?uncap_first};

    /**
    * 查询分页数据
    */
    @ApiOperation(value = "查询 ${table.name} 分页数据", notes="查询 ${table.name} 分页数据")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int"),
        @ApiImplicitParam(name = "factor", value = "", required = true, dataType = "int")
    })
    @RequestMapping(value = "list", method = RequestMethod.GET)
    public RestResponse<Page<${entity}>> listByPage(@RequestParam(name = "page", defaultValue = "1") int page,
                                    @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                    @RequestParam(name = "factor", defaultValue = "") String factor) {
        return RestResponse.ok(${table.serviceName?uncap_first}.list${entity}sByPage(page, pageSize,factor));
    }


    /**
    * 根据id查询
    */
    @ApiOperation(value = "根据id查询 ${table.name}", notes="根据id查询 ${table.name}")
    @ApiImplicitParam(name = "id", value = "${table.name} ID", required = true, dataType = "Long")
    @RequestMapping(value = "get", method = RequestMethod.GET)
    public RestResponse<${entity}> getById(@RequestParam(name = "id") Long id) {
        return RestResponse.ok(${table.serviceName?uncap_first}.get${entity}ById(id));
    }

    /**
    * 新增
    */
    @ApiOperation(value = "新增 ${table.name}",notes="新增一条 ${table.name}")
    @ApiImplicitParam(name = "${table.name}", value = "${table.name} 实体", required = true, dataType = "${entity}对象")
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    public RestResponse<Long> insert(@RequestBody ${entity} ${entity?uncap_first}) {
        return RestResponse.ok(${table.serviceName?uncap_first}.insert${entity}(${entity?uncap_first}));
    }

    /**
    * 删除
    */
    @ApiOperation(value = "删除 ${table.name}",notes="根据id删除 ${table.name}")
    @ApiImplicitParam(name = "id", value = "${table.name} id", required = true, dataType = "<#list table.fields as field><#if field.keyFlag == true>${field.columnType?lower_case?cap_first}</#if></#list>")
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    public RestResponse<Long> deleteById(@RequestParam(name = "id") Long id) {
        return RestResponse.ok(${table.serviceName?uncap_first}.delete${entity}ById(id));
    }

    /**
    * 修改
    */
    @ApiOperation(value = "修改 ${table.name}",notes="根据id修改 ${table.name}")
    @ApiImplicitParam(name = "${table.name}", value = "${table.name} 实体", required = true, dataType = "${entity}对象")
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse<Long> updateById(@RequestBody ${entity} ${entity?uncap_first}) {
        return RestResponse.ok(${table.serviceName?uncap_first}.update${entity}(${entity?uncap_first}));
    }
}
</#if>