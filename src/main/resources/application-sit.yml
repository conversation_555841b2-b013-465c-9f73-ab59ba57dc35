# 开发环境配置文件
server:
  port: 8081     # Springboot占用端口

spring:
  application:
    name: jd-ms-ticket-refund
  autoconfigure:
    #自动化配置 例外处理
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: mysql #默认数据源
      datasource:
        oracle:
          url: ***************************************  # 数据库链接
          username: <PERSON><PERSON>(uW8Aqb3HeMCWWNbjQD3+fA==)     # 数据库账号（需要替换成您的数据库账号） jdet
          password: ENC(KDvjSwUaIyDyD+IKbU2R0dlejQdwsKAv)
          driver-class-name: oracle.jdbc.OracleDriver
        mysql:
          url: jdbc:mysql://*********:3306/jd_special_refund?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useUnicode=true&useSSL=false&allowPublicKeyRetrieval=true     # 数据库链接
          username: <PERSON><PERSON>(yMgKlB6mjah1ajf+2eIz1noH4IovcAd168saPwensT4=)     # 数据库账号（需要替换成您的数据库账号）jd_special_refund
          password: ENC(N8avqx0vdTzJD6fiJ9CtlBp3qisv5pIx)     # 数据库密码（需要替换成您的数据库密码）Jdair@123
          driver-class-name: com.mysql.cj.jdbc.Driver
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20

  redis:
    password: ENC(BavCb+skeCZq9ID4BVlLiJxxFD8foz6An1tdi1whXZnaX42Q+3EO/QRcUd8mKAp6AHl+Xt10Qfx1vmuCjcrStA==)
    port: 6379
    #线上环境
    host: ************
    lettuce:
      pool:
        # 最大空闲连接数 默认8
        max-idle: 8
        # 最小空闲连接数 默认0
        min-idle: 0
        # 最大活跃链接数 默认8
        max-active: 8
    #请求时间
    timeout: 5000

  devtools:
    restart:
      enabled: true     # 开启热更新

# logback 配置
logging:
  config: classpath:log-back.xml
  file:
    path: /app/logs/jd-ms-ticket-refund/
  level:
    root: INFO

# IBE 配置
ibe:
  config:
    ip: *************
    backupIp: *************
    port: 6891
    app: jdcrm
    office: xiy258
    customno: 0
    validationno: 64
    printNos: 1
    printInterNos: 6

# 股份 esb 接口配置信息
odsapi:
  url: https://odsuat.hnair.net/api
  serialization: fastjson2
  timeout: 600000

#必填配置 #uat 地址为https://odsuat.hnair.net/api
#以下为生产环境各区的调用地址，消费系统根据各自调用情况，自行选择
#美兰公共：https://odsapi-ml.hnair.net/api
#南数公共：https://odsapi-ns.hnair.net/api
#北数公共：https://odsapi-bs.hnair.net/api
#序列化框架可选配置，默认hessian2，综合性能 fst  > fastjson2 > hessian2 > hessian > json
#推荐使用 hessian2 和 fastjson2
#default 600000 可选配置

# 邮件发送账号配置
email:
  HWZSendEmail:
    from: da
    pwd: ENC(QFOxs+7HrGeRQIgvVDQyL2aiX6pw+GtW)
  TsSendEmail:
    from: da
    pwd: ENC(QFOxs+7HrGeRQIgvVDQyL2aiX6pw+GtW)
    receiver: <EMAIL>,<EMAIL>

# 短信配置
sms:
  url: http://msg.jdair.net
  send: /ussinterface/uss/json/mobile/messSend.json
  sendTD: /ussinterface/uss/json/mobile/advertMessSend.json
  cc: 1
  cp: 202.100.200.62

# 模板配置
jd:
  template:
    sms:
      # 审核通过
      refundPass: 您的票号{ticketNo}已申请退票并审核通过，拟退款{amout}元。如有疑问请致电客服热线95375。
      # 审核未通过
      refundUnPass: 您的票号{ticketNo}非自愿退票审核未通过，按自愿退票处理，退款金额{amout}元。如有疑问请致电95375。
      # 审核拒绝
      refundRefuse: 您的票号{ticketNo}退票材料尚不符合条件，详情可登陆首都航空官网【我的订单】进行查询。如有疑问请致电95375。
      # 国际票，二审通过, 金额为0的短信模板
      gjRefundPass0: 您{date}提交的退票申请，经审核退票手续费高于票款总额，退款金额为零，如有疑问请致电95375。
      # 国际票，审核未通过，金额为0的短信模板
      gjRefundUnpass0: 您{date}提交的非自愿退票审核未通过，按自愿退票处理，经审核退票手续费高于票款总额，退款金额为零，如有疑问请致电95375。
      # 退款成功
      refundMoney: 旅客{name}，票号{ticket}的退票申请已完成退款，退款金额{amout}元，请您注意查收。
      # 特殊退票一审拒绝
      tsRefundFRejectMsg: 尊敬的旅客，您好，您提交的{ticketNo}特殊退票由于“{firstRejectRemark}”原因导致一审不通过，建议您根据要求重新提交相关材料，感谢您对我司工作的支持。
      # 特殊退票退款成功
      tsRefundMoneySuccessMsg: 尊敬的旅客，您提交的{ticketNo}特殊退票已退款，到账以银行处理时间为准，请注意查收，感谢您对我司工作的支持。
      # 特殊退票退款失败
      tsRefundFailFRejectMsg: 尊敬的旅客，您好，您提交的{ticketNo}特殊退票由于以下原因导致退款失败，“{refundFailRejectRemark}”。请核对修改以上相关信息后进入海航丨首都航空小程序-特殊退票-申请信息查询-找到订单后点击进入修改相关信息后在原申请单中再次提交！


# 支付商户配置
pay:
  alipay:
    source:
      GJ:
        accountId: <EMAIL>
        merchantId: ****************
        md5Key: v4tj4pwf159sha9h0vd17m2dlt7s81n9
        payUrl: https://mapi.alipay.com/gateway.do?
        payCallBackUrl:
        backendPayCallBackUrl:
        alipaymentNotifyUrl: https://api.jdair.net/proxy/payment/alipayRefundCallbackGJ
        refundNotifyUrl:
        refundUrl: https://mapi.alipay.com/gateway.do?
        alipayNotifyUrl: https://notify.alipay.com/trade/notify_query.do?
      GJNew:
        accountId: <EMAIL>
        merchantId: ****************
        md5Key: ee5c12fft2xzlqkt8zqhq0zj5ynf7hu9
        payUrl: https://mapi.alipay.com/gateway.do?
        payCallBackUrl:
        backendPayCallBackUrl:
        alipaymentNotifyUrl: https://api.jdair.net/proxy/payment/alipayRefundCallbackGJ
        refundNotifyUrl:
        refundUrl: https://mapi.alipay.com/gateway.do?
        alipayNotifyUrl: https://notify.alipay.com/trade/notify_query.do?
        effectiveDate: 2023-03-19
      HWZ:
        accountId: <EMAIL>
        merchantId: ****************
        md5Key: 232ys7oygwpivd0fxooyrgapu8k4lok4
        payUrl: https://mapi.alipay.com/gateway.do?
        payCallBackUrl:
        backendPayCallBackUrl:
        alipaymentNotifyUrl: https://api.jdair.net/proxy/payment/alipayRefundCallbackGJ
        refundNotifyUrl:
        refundUrl: https://mapi.alipay.com/gateway.do?
        alipayNotifyUrl: https://notify.alipay.com/trade/notify_query.do?
      # 国际票含随票保险订单，支付宝退款账户配置
      GJWithInsurance:
        accountId: <EMAIL>
        merchantId: ****************
        appId: ****************
        appPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCKyj4ZMH8Bd8Zoud8KddZCjHXxJDvTlurt4ImlQGSQzAqfN2E7aGbIpSuqThpcqmlyRYHbgvRTZTZolmhKUaR38YHPDnOJ02zpmZPPPp9yUAQCeaYz4hKmf5ows3OU4GDtTD2iibsG/Ng+a87uYW3piU9KKcrT3Si7h1ahWXpB+kox7AQnPDCBWbh+u9vNBeDnMA7RJn3Y/Y5UsU4QKU1+dmZpr5qH0ee4pv28DxJwOJfQzkRl6zX5M0zBearQkCmYbrdK0xF19cQjH46F+rUaqi7Hl3JbbZWn4HV/S5DlufGILl1BmroksqOHvGgy0Wnga/I9V7mJnXmTN7YwS/mLAgMBAAECggEBAIG4QY49m5d9oX5kq6kVNiuBKQM3wIslNXMcOMqxgs0rSIpDsqdaDydd6ZAF6/HMfT4G5TaMEVyY7t+WJOfBox1QJummMisR52v21QYRG+o2C2lc1hEMLi5ceXsnXtI5XZ6tcbYnUR1Y2ayOCx0XByoN4oQIx8B5pwaPmlK4VOKwbsuHUUPCxujBuRSKNpF4BiIlmuQLJgBKUBJGef/qXvRA3gs7Wou3uNtCxvM5iYqPUt/8FmleGZn/rNGhobHbsWmLQmssXhO32P1Zvs82DoQlhfha3nFXUD6siH9dd1kd8JzWkrz0l9Rj1yJGXTYrgB8XxqVW5d4zBKspDI7pJiECgYEA2o6LUkGrGmVj4Ju2g1I+p8dQ0VlTxLwvlSQd8NZ6H2wabtv8Q7v9rLt8SBgEao1kzr7tEIkREaWOTftaUTrQ0GWjQXkfw7cx70Q+Lf/Dfxj1zW2ssGbX1ITQQHuJ7ACDEi8fAMdbgW2cb8p0h78MrTOC6WUFwICGiglnPlcwxNkCgYEAopFKBeE8nplwA4r5VGKcZSg3DTcRkCp4Op5jnlRBPlfnY0mfOIIlgeZGKI4qP42ZWB01wcUC2k+CkAQ5qm6MSd8yfSxFdSW+Yxigi/oSNVqU84k06YHygO0L6gnCEUM+XwxDpXNNwQHMMDhMBDVPTiew1QiTl2brgkpKNFxQIwMCgYA+ffGiRHqbIpCLYZqvM+4MZTZbldIY98IXP+t7uKfUokFwn2AjMfaX1Ef2CUACA4nmf7sjNj32MMNe0tcBUPLyE425o9uMR650fqvU1UPd7JIj+g8dpVpjPtKZDXnjTEYuBeE0hMxKVsAbSuVFB0ClqYvuFl+tqlJN1+B4d8/PCQKBgQCJC5IrgsVnrlIPqoil6ruNS2mdal8T+bmBMSUpxD18IP6fZOEk8hJMf6OFbNOQ3/31mipHP544C+81GVrDoo4BeHdHrpqfN0JYp9owJpBTChmndHN2pmWY6jyw/6jqhJV/6wbb42gY3TsVK2Bp68b/S9Z2wNQrPkgrfhUwLuIfoQKBgC5r8jj0ppBRgeqZgwnzqWRNEuGtvw5DKOfqEPLW9xNtxgdYH9SiAhqdHU/JaH0uC7exD0ZhlJi0JJgZpLKZ6ME/3SBBbaDn9mU4PoOd1dP+dl9a83/Iue9Df/ZSaCeYazFyebwMwTjADkoegb16gagvrd38ISwmVwEn2XlJZSVR
        appPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiso+GTB/AXfGaLnfCnXWQox18SQ705bq7eCJpUBkkMwKnzdhO2hmyKUrqk4aXKppckWB24L0U2U2aJZoSlGkd/GBzw5zidNs6ZmTzz6fclAEAnmmM+ISpn+aMLNzlOBg7Uw9oom7BvzYPmvO7mFt6YlPSinK090ou4dWoVl6QfpKMewEJzwwgVm4frvbzQXg5zAO0SZ92P2OVLFOEClNfnZmaa+ah9HnuKb9vA8ScDiX0M5EZes1+TNMwXmq0JApmG63StMRdfXEIx+Ohfq1Gqoux5dyW22Vp+B1f0uQ5bnxiC5dQZq6JLKjh7xoMtFp4GvyPVe5iZ15kze2MEv5iwIDAQAB
        aliPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAht8LEZ5zkD8j98M+QcD7LCpDXljyTpQUjpSlQBmoBYdwHqeJs9D9sKqzQYTt6nxcmeC46GohI/vlAJB9i7Nxmn8N8l7kx/2WsvmMMDak9f5d6CWYlA6nEF/J9jdXAtM4RCysaCHqtfHCtaNcnN04nk8xp6em27ZIRixpLlKXGVOP5H7EUysjqpaY176si5lkCtv0OzXSrqWqmD26MCTMEaNirzc3giZA8XwNuXTfLBVC9POKtJwHxSb7INZrTYSgaikQ4mSdvZWAazhSjUJan0Idc0TZAxladYICuRBJ3zo7HcxulIb5aXY8hxocOuGB5ercSpgZdxfab/YeKYxwJQIDAQAB
  wxpay:
    source:
      GJ:
        appID: wx3c642b1089615a45
        mchID: 1217215701
        key: d6e4ba9535e1b80e58885864be695b18
        certPath: key/wxpay/inter/apiclient_cert.p12
        payCallBackUrl:
        backendPayCallBackUrl: https://4fdef6e3.r8.cpolar.top/proxy/payment/backendCallBackPayWXpay
        refundUrl: https://api.mch.weixin.qq.com/secapi/pay/refund

# eureka 注册中心地址
eureka:
  client:
    serviceUrl:
      defaultZone: http://1********:8761/eureka/,http://*********:8761/eureka/
  instance:
    prefer-ip-address: true

# minio 文件服务配置
minio:
  config:
    # 标记是否开启
    open: true
  # 账号
  accessKey: jd-ms-ticket-refund
  # 密码
  secretKey: Za4Q9cnJgEeLjHHdOxllrhr8lpjlRYXz
  # 服务地址
  endpoint: https://doc.jdair.net
  # 可选项。默认上传文件目标路径（使用这个参数，可以指定上传文件在 MinIO 存储桶中的位置和名称，可以是文件路径、目录路径或者是一个包含路径和文件名的完整路径）
  uploadFileDest: /opt/temp1/
  # 可选项。上传文件时的压缩大小阈值，单位是字节。当上传的文件大小超过指定的阈值时，MinIO 客户端会自动对文件进行压缩。默认情况下，MinIO 客户端不会对文件进行压缩。500*1024 500K
  compressSize: 512000