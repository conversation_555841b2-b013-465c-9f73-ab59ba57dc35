<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtProductMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtProduct">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="airlineCode" column="airline_code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="ei" column="ei" jdbcType="VARCHAR"/>
            <result property="productType" column="product_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,airline_code,
        name,description,status,
        create_id,create_date,ei,
        product_type
    </sql>
</mapper>
