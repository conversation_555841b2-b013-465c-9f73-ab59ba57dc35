<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtFltRefundAuditMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtFltRefundAudit">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="refundId" column="refund_id" jdbcType="DECIMAL"/>
            <result property="submitUsername" column="submit_username" jdbcType="VARCHAR"/>
            <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
            <result property="auditUsername" column="audit_username" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="notion" column="notion" jdbcType="VARCHAR"/>
            <result property="amountstr" column="amountstr" jdbcType="VARCHAR"/>
            <result property="auditResult" column="audit_result" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_id,submit_username,
        submit_time,audit_username,audit_time,
        action,notion,amountstr,
        audit_result
    </sql>
</mapper>
