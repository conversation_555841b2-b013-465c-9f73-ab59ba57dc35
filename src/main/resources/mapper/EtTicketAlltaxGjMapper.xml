<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTicketAlltaxGjMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtTicketAlltaxGj">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ticketId" column="ticket_id" jdbcType="BIGINT"/>
            <result property="allTax" column="all_tax" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ticket_id,all_tax
    </sql>
</mapper>
