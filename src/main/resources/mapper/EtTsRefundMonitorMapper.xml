<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTsRefundMonitorMapper">

    <select id="queryMaxPaymentId" resultType="java.lang.Long">
        select max(id) from et_payment
    </select>

    <!-- 同一个订单产生二次退款（二审通过或已退款） -->
    <select id="queryByMultiPayment" resultType="net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord">
        select r.refund_no
        from et_flt_refund r,
             (select p.id, p.amount, rp.flt_refund_id
              from et_payment p,
                   et_flt_refund_payment rp
              where p.id > #{preMaxPaymentId}
                and p.id = rp.payment_id
                and p.pay_status = 'PAID') pid_amounts
        where r.id = pid_amounts.flt_refund_id
          and (r.order_source = 'TS' or r.order_source = 'TSGJ')
        GROUP BY r.refund_no
        HAVING COUNT(*) > 1
    </select>

    <!-- 同一个金额、同一收款账号（二审通过或已退款） -->
    <select id="queryBySameCardAndAmount" resultType="net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord">
        select r.refund_no, rids.card_no, rids.card_holder, rids.amount
        from et_flt_refund r,
             (select bank.flt_refund_id, bank.card_holder, tmp.*
              from et_special_refund_bank_info bank,
                   et_payment pay,
                   et_flt_refund_payment rp,
                   (select b.card_no, pid_amounts.amount
                    from et_special_refund_bank_info b,
                         et_flt_refund r,
                         (select p.id, p.amount, rp.flt_refund_id
                          from et_payment p,
                               et_flt_refund_payment rp
                          where p.id >= #{preMaxPaymentId}
                            and p.id = rp.payment_id
                            and p.pay_status = 'PAID') pid_amounts
                    where b.flt_refund_id = pid_amounts.flt_refund_id
                      and b.flt_refund_id = r.id
                      and (r.order_source = 'TS' or r.order_source = 'TSGJ')
                    group by b.card_no, pid_amounts.amount
                    having count(*) > 1
                   ) tmp
              where bank.card_no = tmp.card_no
                and pay.amount = tmp.amount
                and pay.id = rp.payment_id
                and rp.flt_refund_id = bank.flt_refund_id
             ) rids
        where r.id = rids.flt_refund_id
        order by rids.card_no
    </select>

    <!-- 票号、退单号、收款人、收款账号、金额均相同 -->
    <select id="queryByAllSame" resultType="net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord">
        select
            refund_no
        from (
                 select distinct
                     r.refund_no,
                     rids.card_no,
                     rids.card_holder,
                     rids.amount,
                     t.iss_code,
                     t.ticket_no,
                     rids.payment_no,
                     rids.pay_status
                 from
                     et_flt_refund r
                         join
                     et_flt_refund_pax_seg rps on r.id = rps.refund_id
                         join
                     et_passenger_segment ps on ps.id = rps.pax_seg_id
                         join
                     et_ticket t on t.id = ps.ticket_id
                         join (
                         select
                             bank.flt_refund_id,
                             bank.card_holder,
                             pay.payment_no,
                             pay.pay_status,
                             tmp.*
                         from
                             et_special_refund_bank_info bank
                                 join
                             et_flt_refund_payment rp on bank.flt_refund_id = rp.flt_refund_id
                                 join
                             et_payment pay on pay.id = rp.payment_id
                                 join (
                                 select
                                     b.card_no,
                                     pid_amounts.amount,
                                     r.refund_no
                                 from
                                     et_special_refund_bank_info b
                                         join
                                     et_flt_refund r on b.flt_refund_id = r.id
                                         join (
                                         select
                                             p.id,
                                             p.amount,
                                             rp.flt_refund_id
                                         from
                                             et_payment p
                                                 join
                                             et_flt_refund_payment rp on p.id = rp.payment_id
                                         where
                                             p.id >= #{preMaxPaymentId}
                                           and p.pay_status = 'PAID'
                                     ) pid_amounts on b.flt_refund_id = pid_amounts.flt_refund_id
                                 where
                                     (r.order_source = 'TS' or r.order_source = 'TSGJ')
                                 group by
                                     b.card_no, pid_amounts.amount
                                 having
                                     count(*) > 1
                             ) tmp on bank.card_no = tmp.card_no and pay.amount = tmp.amount
                         where
                             pay.pay_status = 'PAID'
                     ) rids on r.id = rids.flt_refund_id
             ) as refund_ticket_info
        group by
            refund_no, iss_code, ticket_no
        having
            count(*) > 1
    </select>

</mapper>
