<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.jdair.specialrefund.bizz.mapper.SpecialRefundReportMapper">

    <!-- 特殊退票 - 退款报表查询 -->
    <select id="querySpecialRefundReport" resultType="net.jdair.specialrefund.bizz.vo.DTOSpecialRefundReport">
        SELECT
        MAX(CASE WHEN tmp.S_ACTION = 'SECONDAUDIT' THEN tmp.AUDIT_TIME ELSE '' END) AS secondAuditTime,
        MAX(tmp.CABIN) AS cabin,
        MAX(tmp.FLIGHT_NO) AS flightNo,
        tmp.REFUND_NO AS refundNo,
        MAX(tmp.PAX_SEG_ID) AS paxSegId,
        tmp.DEP_CODE AS depCode,
        tmp.ARR_CODE AS arrCode,
        MAX(tmp.CREATE_TIME) AS refundDate,
        MAX(tmp.PAY_TIME) AS payTime,
        MAX(tmp.NAME) AS passengerName,
        tmp.TICKET_NO AS ticketNo,
        MAX(tmp.REMARK) AS remark,
        MAX(tmp.STATUS) AS auditResult,
        MAX(CASE WHEN a.ACTION = 'FIRSTAUDIT' AND tmp.STATUS != 'NEW' AND AUDIT_USERNAME IS NOT NULL THEN a.NOTION ELSE '' END) AS firstAuditRemark,
        MAX(CASE WHEN tmp.S_ACTION = 'SECONDAUDIT' THEN tmp.NOTION ELSE '' END) AS secondAuditRemark,
        MAX(tmp.NET_FARE) AS salePrice,
        MAX(tmp.AIRPORT_TAX) AS airportTax,
        MAX(tmp.FUEL_TAX) AS fuelTax,
        MAX(tmp.REFUND_AMOUNT) AS applyAmount,
        MAX(tmp.ACTUAL_REFUND_AMOUNT) AS refundAmount,
        MAX(tmp.REFUND_TYPE) AS refundType,
        MAX(tmp.CONSTRAINT_REASON) AS refundReason
        FROM (
        SELECT
        r.id AS REFUND_ID,
        r.REFUND_NO AS REFUND_NO,
        s.DEP_CODE AS DEP_CODE,
        s.ARR_CODE AS ARR_CODE,
        s.CABIN_CLASS AS CABIN,
        s.FLIGHT_NO AS FLIGHT_NO,
        DATE_FORMAT(r.CREATE_TIME, '%Y-%m-%d %H:%i:%s') AS CREATE_TIME,
        DATE_FORMAT(r.PAY_TIME, '%Y-%m-%d %H:%i:%s') AS PAY_TIME,
        c.NAME AS NAME,
        CONCAT(t.ISS_CODE, '-', t.TICKET_NO) AS TICKET_NO,
        r.REMARK AS REMARK,
        r.STATUS AS AUDIT_RESULT,
        au.NOTION AS NOTION,
        au.ACTION AS S_ACTION,
        DATE_FORMAT(au.AUDIT_TIME, '%Y-%m-%d %H:%i:%s') AS AUDIT_TIME,
        ps.ID AS PAX_SEG_ID,
        ps.NET_FARE AS NET_FARE,
        ps.AIRPORT_TAX AS AIRPORT_TAX,
        ps.FUEL_TAX AS FUEL_TAX,
        rfps.REFUND_AMOUNT AS REFUND_AMOUNT,
        rfps.ACTUAL_REFUND_AMOUNT AS ACTUAL_REFUND_AMOUNT,
        r.REFUND_TYPE AS REFUND_TYPE,
        r.CONSTRAINT_REASON AS CONSTRAINT_REASON,
        r.STATUS AS STATUS
        FROM et_flt_refund r
        LEFT JOIN et_flt_refund_pax_seg rfps ON r.id = rfps.REFUND_ID
        LEFT JOIN et_passenger_segment ps ON ps.id = rfps.PAX_SEG_ID
        LEFT JOIN et_segment s ON s.id = ps.SEGMENT_ID
        LEFT JOIN et_passenger p ON p.id = ps.PASSENGER_ID
        LEFT JOIN customer c ON c.ID = p.CUSTOMER_ID
        LEFT JOIN et_ticket t ON t.id = ps.TICKET_ID
        LEFT JOIN et_flt_refund_audit au ON au.REFUND_ID = r.id
        WHERE 1 = 1
        <if test="criteria.orderSource != null and criteria.orderSource != ''">
            AND r.order_source = #{criteria.orderSource}
        </if>
        <if test="criteria.refundDateStart != null and criteria.refundDateStart != ''">
            AND r.CREATE_TIME &gt;= #{criteria.refundDateStart}
        </if>
        <if test="criteria.refundDateEnd != null and criteria.refundDateEnd != ''">
            AND r.CREATE_TIME &lt;= #{criteria.refundDateEnd}
        </if>
        <if test="criteria.flightDateStart != null and criteria.flightDateStart != ''">
            AND s.DEP_TIME &gt;= #{criteria.flightDateStart}
        </if>
        <if test="criteria.flightDateEnd != null and criteria.flightDateEnd != ''">
            AND s.DEP_TIME &lt;= #{criteria.flightDateEnd}
        </if>
        <if test="criteria.auditDateStart != null and criteria.auditDateStart != ''">
            AND au.AUDIT_TIME &gt;= #{criteria.auditDateStart}
        </if>
        <if test="criteria.auditDateEnd != null and criteria.auditDateEnd != ''">
            AND au.AUDIT_TIME &lt;= #{criteria.auditDateEnd}
        </if>
        <if test="criteria.payTimeStart != null and criteria.payTimeStart != ''">
            AND r.PAY_TIME &gt;= #{criteria.payTimeStart}
        </if>
        <if test="criteria.payTimeEnd != null and criteria.payTimeEnd != ''">
            AND r.PAY_TIME &lt;= #{criteria.payTimeEnd}
        </if>
        <if test="criteria.depCode != null and criteria.depCode != ''">
            AND s.DEP_CODE = #{criteria.depCode}
        </if>
        <if test="criteria.arrCode != null and criteria.arrCode != ''">
            AND s.ARR_CODE = #{criteria.arrCode}
        </if>
        <if test="criteria.auditStatus != null and criteria.auditStatus != ''">
            AND r.STATUS = #{criteria.auditStatus}
            <choose>
                <when test="criteria.auditStatus == 'NEW' or criteria.auditStatus == 'FPASS' or criteria.auditStatus == 'FREJECT'">
                    AND au.ACTION = 'FIRSTAUDIT'
                </when>
                <otherwise>
                    AND au.ACTION = 'SECONDAUDIT'
                </otherwise>
            </choose>
        </if>
        <if test="criteria.refundType != null and criteria.refundType != ''">
            AND r.REFUND_TYPE = #{criteria.refundType}
        </if>
        ) tmp
        LEFT JOIN et_flt_refund_audit a ON a.REFUND_ID = tmp.REFUND_ID
        GROUP BY tmp.REFUND_NO, tmp.TICKET_NO, tmp.DEP_CODE, tmp.ARR_CODE
    </select>

</mapper>
