<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtPaymentNoGeneratorMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtPaymentNoGenerator">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
            <result property="paymentNo" column="payment_no" jdbcType="VARCHAR"/>
            <result property="paymentNoStatus" column="payment_no_status" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="usedTime" column="used_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_no,payment_no,
        payment_no_status,create_time,update_time,
        used_time
    </sql>
</mapper>
