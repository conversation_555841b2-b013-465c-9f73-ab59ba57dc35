<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtPassengerSegmentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtPassengerSegment">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="passengerId" column="passenger_id" jdbcType="DECIMAL"/>
            <result property="segmentId" column="segment_id" jdbcType="DECIMAL"/>
            <result property="ticketId" column="ticket_id" jdbcType="DECIMAL"/>
            <result property="marketFare" column="market_fare" jdbcType="DECIMAL"/>
            <result property="netFare" column="net_fare" jdbcType="DECIMAL"/>
            <result property="airportTax" column="airport_tax" jdbcType="DECIMAL"/>
            <result property="fuelTax" column="fuel_tax" jdbcType="DECIMAL"/>
            <result property="otherTaxes" column="other_taxes" jdbcType="DECIMAL"/>
            <result property="tax1" column="tax1" jdbcType="DECIMAL"/>
            <result property="tax2" column="tax2" jdbcType="DECIMAL"/>
            <result property="tax3" column="tax3" jdbcType="DECIMAL"/>
            <result property="tax4" column="tax4" jdbcType="DECIMAL"/>
            <result property="insurance" column="insurance" jdbcType="DECIMAL"/>
            <result property="agentFeeRate" column="agent_fee_rate" jdbcType="DECIMAL"/>
            <result property="spFeeRate" column="sp_fee_rate" jdbcType="DECIMAL"/>
            <result property="agentFee" column="agent_fee" jdbcType="DECIMAL"/>
            <result property="spFee" column="sp_fee" jdbcType="DECIMAL"/>
            <result property="fareBasis" column="fare_basis" jdbcType="VARCHAR"/>
            <result property="notValidBefore" column="not_valid_before" jdbcType="TIMESTAMP"/>
            <result property="notValidAfter" column="not_valid_after" jdbcType="TIMESTAMP"/>
            <result property="allow" column="allow" jdbcType="VARCHAR"/>
            <result property="ticketStatus" column="ticket_status" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="DECIMAL"/>
            <result property="sequence" column="sequence" jdbcType="SMALLINT"/>
            <result property="point" column="point" jdbcType="BIGINT"/>
            <result property="hasFreeTicket" column="has_free_ticket" jdbcType="VARCHAR"/>
            <result property="usedFreeTicket" column="used_free_ticket" jdbcType="VARCHAR"/>
            <result property="used" column="used" jdbcType="VARCHAR"/>
            <result property="payFreeTicket" column="pay_free_ticket" jdbcType="VARCHAR"/>
            <result property="currency" column="currency" jdbcType="CHAR"/>
            <result property="couponPrice" column="coupon_price" jdbcType="DECIMAL"/>
            <result property="oldNetFare" column="old_net_fare" jdbcType="DECIMAL"/>
            <result property="orderId" column="order_id" jdbcType="DECIMAL"/>
            <result property="pointPrice" column="point_price" jdbcType="DECIMAL"/>
            <result property="refCabin" column="ref_cabin" jdbcType="VARCHAR"/>
            <result property="refFare" column="ref_fare" jdbcType="DECIMAL"/>
            <result property="oriProductCode" column="ori_product_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,passenger_id,segment_id,
        ticket_id,market_fare,net_fare,
        airport_tax,fuel_tax,other_taxes,
        tax1,tax2,tax3,
        tax4,insurance,agent_fee_rate,
        sp_fee_rate,agent_fee,sp_fee,
        fare_basis,not_valid_before,not_valid_after,
        allow,ticket_status,status,
        product_id,sequence,point,
        has_free_ticket,used_free_ticket,used,
        pay_free_ticket,currency,coupon_price,
        old_net_fare,order_id,point_price,
        ref_cabin,ref_fare,ori_product_code
    </sql>

    <select id="findByRefundId" resultMap="net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper.searchRefundOrderListPsResMap">
        select *
        from et_flt_refund_pax_seg rps,
             et_passenger_segment ps,
             et_segment s
        where rps.pax_seg_id = ps.id
          and ps.segment_id = s.id
          and rps.refund_id = #{refundId}
    </select>


</mapper>
