<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtOrderPaymentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtOrderPayment">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="orderId" column="order_id" jdbcType="DECIMAL"/>
            <result property="paymentId" column="payment_id" jdbcType="DECIMAL"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,payment_id,
        amount
    </sql>
</mapper>
