<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtFltRefundPaxSegMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg">
        <id property="id" column="id" jdbcType="DECIMAL"/>
        <result property="refundId" column="refund_id" jdbcType="DECIMAL"/>
        <result property="paxSegId" column="pax_seg_id" jdbcType="DECIMAL"/>
        <result property="refundAmount" column="refund_amount" jdbcType="DECIMAL"/>
        <result property="actualRefundAmount" column="actual_refund_amount" jdbcType="DECIMAL"/>
        <result property="refundPoint" column="refund_point" jdbcType="SMALLINT"/>
        <result property="amsRefundPoint" column="ams_refund_point" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_id,pax_seg_id,
        refund_amount,actual_refund_amount,refund_point,
        ams_refund_point
    </sql>

    <!-- 根据 refund_id 查询退票航段信息 -->
    <select id="getByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg">
        SELECT
        <include refid="Base_Column_List" />
        FROM et_flt_refund_pax_seg where refund_id = #{id}
    </select>
</mapper>
