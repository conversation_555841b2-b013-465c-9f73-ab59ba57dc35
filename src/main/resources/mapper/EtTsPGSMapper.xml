<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTsPGSMapper">

    <select id="findRefundByTime" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefund">
        select * from et.ET_FLT_REFUND r where
                r.CREATE_TIME &gt;= TO_DATE(#{createTimeStart}, 'YYYY-MM-DD HH24:MI:SS') and
                r.CREATE_TIME &lt;= TO_DATE(#{createTimeEnd}, 'YYYY-MM-DD HH24:MI:SS') and
                (r.ORDER_SOURCE = 'TS' or r.ORDER_SOURCE = 'TSGJ')
    </select>

    <select id="findPaxSegByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg">
        select * from et.ET_FLT_REFUND_PAX_SEG rfps where rfps.REFUND_ID = #{refundId}
    </select>

    <select id="findRefundByRefundNo" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefund">
        select * from et.ET_FLT_REFUND r where r.REFUND_NO = #{refundNo}
    </select>

    <select id="findPsByPaxSegId" resultType="net.jdair.specialrefund.bizz.domain.EtPassengerSegment">
        select * from et.ET_PASSENGER_SEGMENT ps where ps.id = #{paxSegId}
    </select>

    <select id="findPByPId" resultType="net.jdair.specialrefund.bizz.domain.EtPassenger">
        select * from et.ET_PASSENGER p where p.id = #{pId}
    </select>

    <select id="findCByCId" resultType="net.jdair.specialrefund.bizz.domain.Customer">
        select * from et.CUSTOMER c where c.ID = #{cId}
    </select>

    <select id="findPnrByPnrId" resultType="net.jdair.specialrefund.bizz.domain.EtPnr">
        select * from et.ET_PNR pnr where pnr.ID = #{pnrId}
    </select>

    <select id="findSBySId" resultType="net.jdair.specialrefund.bizz.domain.EtSegment">
        select * from et.ET_SEGMENT s where s.id = #{sId}
    </select>

    <select id="findTByTId" resultType="net.jdair.specialrefund.bizz.domain.EtTicket">
        select * from et.ET_TICKET t where t.id = #{tId}
    </select>

    <select id="findAuditByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefundAudit">
        select * from et.ET_FLT_REFUND_AUDIT au where au.REFUND_ID = #{refundId}
    </select>

    <select id="findOutPaymentByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefundOutsidePayment">
        select * from et.ET_FLT_REFUND_OUTSIDE_PAYMENT op where op.FLT_REFUND_ID = #{refundId}
    </select>

    <select id="findPayPaymentById" resultType="net.jdair.specialrefund.bizz.domain.EtPayment">
        select * from et.ET_PAYMENT pay where pay.ID = #{paymentId}
    </select>

    <select id="findRefundPaymentByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtFltRefundPayment">
        select * from et.ET_FLT_REFUND_PAYMENT rp where rp.FLT_REFUND_ID = #{refundId}
    </select>

    <select id="findBankByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo">
        select * from et.ET_SPECIAL_REFUND_BANK_INFO bank where bank.FLT_REFUND_ID = #{refundId}
    </select>

    <select id="findImgByRefundId" resultType="net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg">
        select * from et.ET_SPECIAL_REFUND_IMG img where img.FLT_REFUND_ID = #{refundId}
    </select>

    <select id="findRefundPaymentById" resultType="net.jdair.specialrefund.bizz.domain.EtPayment">
        select * from et.ET_PAYMENT pay where pay.ID = #{paymentId}
    </select>

    <select id="findOriginByPaxSegId" resultType="net.jdair.specialrefund.bizz.domain.EtSpecialRefundOriginTkt">
        select * from et.ET_SPECIAL_REFUND_ORIGIN_TKT origin where origin.PAX_SEG_ID = #{paxSegId}
    </select>

</mapper>
