<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtSpecialRefundImgMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="fltRefundId" column="flt_refund_id" jdbcType="DECIMAL"/>
            <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
            <result property="imgOriginName" column="img_origin_name" jdbcType="VARCHAR"/>
            <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="refundAuditId" column="refund_audit_id" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,flt_refund_id,img_url,img_origin_name,
        refund_no,source,status,
        operator,create_time,refund_audit_id
    </sql>
</mapper>
