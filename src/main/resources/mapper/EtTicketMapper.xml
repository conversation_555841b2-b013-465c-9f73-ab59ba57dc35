<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTicketMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtTicket">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="issCode" column="iss_code" jdbcType="CHAR"/>
            <result property="ticketNo" column="ticket_no" jdbcType="CHAR"/>
            <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
            <result property="serialNoCk" column="serial_no_ck" jdbcType="CHAR"/>
            <result property="printStatus" column="print_status" jdbcType="CHAR"/>
            <result property="bspType" column="bsp_type" jdbcType="CHAR"/>
            <result property="endorsement" column="endorsement" jdbcType="VARCHAR"/>
            <result property="ck" column="ck" jdbcType="VARCHAR"/>
            <result property="conjunctionTkt" column="conjunction_tkt" jdbcType="VARCHAR"/>
            <result property="agentCode" column="agent_code" jdbcType="VARCHAR"/>
            <result property="issuedBy" column="issued_by" jdbcType="VARCHAR"/>
            <result property="issuedDate" column="issued_date" jdbcType="TIMESTAMP"/>
            <result property="bookTime" column="book_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,iss_code,ticket_no,
        serial_no,serial_no_ck,print_status,
        bsp_type,endorsement,ck,
        conjunction_tkt,agent_code,issued_by,
        issued_date,book_time
    </sql>
</mapper>
