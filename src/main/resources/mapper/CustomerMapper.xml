<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.CustomerMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.Customer">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="certificateno" column="certificateno" jdbcType="VARCHAR"/>
            <result property="certificatetype" column="certificatetype" jdbcType="VARCHAR"/>
            <result property="frequenceno" column="frequenceno" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="CHAR"/>
            <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
            <result property="othername" column="othername" jdbcType="VARCHAR"/>
            <result property="frequentflyerno" column="frequentflyerno" jdbcType="VARCHAR"/>
            <result property="firstname" column="firstname" jdbcType="VARCHAR"/>
            <result property="lastname" column="lastname" jdbcType="VARCHAR"/>
            <result property="nationality" column="nationality" jdbcType="VARCHAR"/>
            <result property="passportno" column="passportno" jdbcType="VARCHAR"/>
            <result property="expiredDate" column="expired_date" jdbcType="VARCHAR"/>
            <result property="bornDate" column="born_date" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="issueCountry" column="issue_country" jdbcType="VARCHAR"/>
            <result property="depcity" column="depcity" jdbcType="VARCHAR"/>
            <result property="depprovince" column="depprovince" jdbcType="VARCHAR"/>
            <result property="depcountry" column="depcountry" jdbcType="VARCHAR"/>
            <result property="depstreet" column="depstreet" jdbcType="VARCHAR"/>
            <result property="deppost" column="deppost" jdbcType="VARCHAR"/>
            <result property="arrcity" column="arrcity" jdbcType="VARCHAR"/>
            <result property="arrprovince" column="arrprovince" jdbcType="VARCHAR"/>
            <result property="arrcountry" column="arrcountry" jdbcType="VARCHAR"/>
            <result property="arrpost" column="arrpost" jdbcType="VARCHAR"/>
            <result property="arrstreet" column="arrstreet" jdbcType="VARCHAR"/>
            <result property="jjctype" column="jjctype" jdbcType="VARCHAR"/>
            <result property="jjcno" column="jjcno" jdbcType="VARCHAR"/>
            <result property="encrypted" column="encrypted" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,certificateno,
        certificatetype,frequenceno,gender,
        birthday,othername,frequentflyerno,
        firstname,lastname,nationality,
        passportno,expired_date,born_date,
        remark,issue_country,depcity,
        depprovince,depcountry,depstreet,
        deppost,arrcity,arrprovince,
        arrcountry,arrpost,arrstreet,
        jjctype,jjcno,encrypted,
        update_date
    </sql>

    <!--  -->
    <select id="findCustomerByNameAndId" resultMap="BaseResultMap">
        select m.* from customer m
        where 1 = 1
        <if test="name != null and name != ''">
            and m.name = #{name}
        </if>
        <if test="certificateNo != null and certificateNo != ''">
            and m.certificateno = #{certificateNo}
        </if>
        <if test="certificateType != null and certificateType != ''">
            and m.certificatetype = #{certificateType}
        </if>
    </select>
</mapper>
