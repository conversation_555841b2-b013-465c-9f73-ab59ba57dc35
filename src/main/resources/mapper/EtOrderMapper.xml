<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtOrderMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtOrder">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="ticketType" column="ticket_type" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="userId" column="user_id" jdbcType="DECIMAL"/>
            <result property="officeNo" column="office_no" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
            <result property="contactTel" column="contact_tel" jdbcType="VARCHAR"/>
            <result property="contactMobile" column="contact_mobile" jdbcType="VARCHAR"/>
            <result property="contactEmail" column="contact_email" jdbcType="VARCHAR"/>
            <result property="contactAddress" column="contact_address" jdbcType="VARCHAR"/>
            <result property="currency" column="currency" jdbcType="CHAR"/>
            <result property="timeLimit" column="time_limit" jdbcType="TIMESTAMP"/>
            <result property="airlineCode" column="airline_code" jdbcType="VARCHAR"/>
            <result property="pnrImport" column="pnr_import" jdbcType="CHAR"/>
            <result property="mailFee" column="mail_fee" jdbcType="DECIMAL"/>
            <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
            <result property="international" column="international" jdbcType="TINYINT"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="onSaleAmount" column="on_sale_amount" jdbcType="DECIMAL"/>
            <result property="quotaValue" column="quota_value" jdbcType="DECIMAL"/>
            <result property="couponNo" column="coupon_no" jdbcType="VARCHAR"/>
            <result property="fullPoint" column="full_point" jdbcType="VARCHAR"/>
            <result property="pointAmount" column="point_amount" jdbcType="DECIMAL"/>
            <result property="couponType" column="coupon_type" jdbcType="VARCHAR"/>
            <result property="groupmark" column="groupmark" jdbcType="TINYINT"/>
            <result property="issurSmsSended" column="issur_sms_sended" jdbcType="TINYINT"/>
            <result property="cardRemark" column="card_remark" jdbcType="VARCHAR"/>
            <result property="itinerary" column="itinerary" jdbcType="VARCHAR"/>
            <result property="webSource" column="web_source" jdbcType="VARCHAR"/>
            <result property="encrypted" column="encrypted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,ticket_type,
        create_time,user_id,office_no,
        status,contact_name,contact_tel,
        contact_mobile,contact_email,contact_address,
        currency,time_limit,airline_code,
        pnr_import,mail_fee,ip_address,
        international,source,on_sale_amount,
        quota_value,coupon_no,full_point,
        point_amount,coupon_type,groupmark,
        issur_sms_sended,card_remark,itinerary,
        web_source,encrypted
    </sql>
</mapper>
