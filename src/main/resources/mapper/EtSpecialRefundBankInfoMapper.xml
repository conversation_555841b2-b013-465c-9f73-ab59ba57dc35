<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtSpecialRefundBankInfoMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="cardNo" column="card_no" jdbcType="VARCHAR"/>
            <result property="cardHolder" column="card_holder" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="mobileNo" column="mobile_no" jdbcType="VARCHAR"/>
            <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
            <result property="fltRefundId" column="flt_refund_id" jdbcType="DECIMAL"/>
            <result property="nbkno" column="nbkno" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,card_no,card_holder,
        bank_name,mobile_no,refund_no,
        flt_refund_id,nbkno,remark
    </sql>
</mapper>
