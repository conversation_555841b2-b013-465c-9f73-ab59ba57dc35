<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtPaymentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtPayment">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="paymentNo" column="payment_no" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="VARCHAR"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="payStatus" column="pay_status" jdbcType="VARCHAR"/>
            <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
            <result property="currency" column="currency" jdbcType="CHAR"/>
            <result property="payer" column="payer" jdbcType="VARCHAR"/>
            <result property="batch" column="batch" jdbcType="CHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="settDate" column="sett_date" jdbcType="SMALLINT"/>
            <result property="field1" column="field1" jdbcType="VARCHAR"/>
            <result property="field2" column="field2" jdbcType="VARCHAR"/>
            <result property="field3" column="field3" jdbcType="VARCHAR"/>
            <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
            <result property="payMode" column="pay_mode" jdbcType="VARCHAR"/>
            <result property="returnTime" column="return_time" jdbcType="TIMESTAMP"/>
            <result property="systemSsn" column="system_ssn" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="cardType" column="card_type" jdbcType="VARCHAR"/>
            <result property="bankId" column="bank_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,payment_no,pay_type,
        action,amount,pay_status,
        pay_time,currency,payer,
        batch,remark,sett_date,
        field1,field2,field3,
        merchant_id,pay_mode,return_time,
        system_ssn,source,card_type,
        bank_id
    </sql>
    
    <select id="generatePaymentNoSeq" resultType="Long">
        SELECT AUTO_INCREMENT
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'et_payment'
    </select>

</mapper>
