<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTsTicketnoBlacklistMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtTsTicketnoBlacklist">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="ticketNo" column="ticket_no" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ticket_no,operator,
        status,create_date
    </sql>
</mapper>
