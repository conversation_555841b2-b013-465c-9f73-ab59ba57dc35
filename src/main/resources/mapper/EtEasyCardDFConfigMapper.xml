<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtEasyCardDFConfigMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtEasyCardDFConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="partnerId" column="partner_id" jdbcType="VARCHAR"/>
            <result property="merchantId" column="merchant_id" jdbcType="VARCHAR"/>
            <result property="merchantPrivateKey" column="merchant_private_key" jdbcType="VARCHAR"/>
            <result property="easypayPublicKey" column="easypay_public_key" jdbcType="VARCHAR"/>
            <result property="desEncodeKey" column="des_encode_key" jdbcType="VARCHAR"/>
            <result property="payUrl" column="pay_url" jdbcType="VARCHAR"/>
            <result property="notifyUrl" column="notify_url" jdbcType="VARCHAR"/>
            <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
