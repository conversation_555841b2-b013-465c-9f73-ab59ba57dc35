<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtSegmentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtSegment">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="orderId" column="order_id" jdbcType="DECIMAL"/>
            <result property="depCode" column="dep_code" jdbcType="CHAR"/>
            <result property="arrCode" column="arr_code" jdbcType="CHAR"/>
            <result property="cabinClass" column="cabin_class" jdbcType="VARCHAR"/>
            <result property="flightNo" column="flight_no" jdbcType="VARCHAR"/>
            <result property="depTime" column="dep_time" jdbcType="TIMESTAMP"/>
            <result property="arrTime" column="arr_time" jdbcType="TIMESTAMP"/>
            <result property="planeType" column="plane_type" jdbcType="VARCHAR"/>
            <result property="groupNo" column="group_no" jdbcType="VARCHAR"/>
            <result property="airlineCode" column="airline_code" jdbcType="VARCHAR"/>
            <result property="inventory" column="inventory" jdbcType="VARCHAR"/>
            <result property="baseFare" column="base_fare" jdbcType="DECIMAL"/>
            <result property="arrTerminal" column="arr_terminal" jdbcType="VARCHAR"/>
            <result property="depTerminal" column="dep_terminal" jdbcType="VARCHAR"/>
            <result property="stop" column="stop" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,dep_code,
        arr_code,cabin_class,flight_no,
        dep_time,arr_time,plane_type,
        group_no,airline_code,inventory,
        base_fare,arr_terminal,dep_terminal,
        stop
    </sql>
</mapper>
