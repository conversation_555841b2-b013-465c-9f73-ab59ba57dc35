<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtFltRefundMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtFltRefund">
        <id property="id" column="id" jdbcType="DECIMAL"/>
        <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="DECIMAL"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="payMode" column="pay_mode" jdbcType="VARCHAR"/>
        <result property="airlineCode" column="airline_code" jdbcType="VARCHAR"/>
        <result property="refundType" column="refund_type" jdbcType="VARCHAR"/>
        <result property="officeNo" column="office_no" jdbcType="VARCHAR"/>
        <result property="ftStatus" column="ft_status" jdbcType="VARCHAR"/>
        <result property="pointStatus" column="point_status" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="orderSource" column="order_source" jdbcType="VARCHAR"/>
        <result property="retrieveTime" column="retrieve_time" jdbcType="TIMESTAMP"/>
        <result property="changeId" column="change_id" jdbcType="DECIMAL"/>
        <result property="retrieveTimeFlag" column="retrieve_time_flag" jdbcType="TINYINT"/>
        <result property="couponNo" column="coupon_no" jdbcType="VARCHAR"/>
        <result property="couponType" column="coupon_type" jdbcType="VARCHAR"/>
        <result property="constraintReason" column="constraint_reason" jdbcType="VARCHAR"/>
        <result property="fictitious" column="fictitious" jdbcType="VARCHAR"/>
        <result property="reserveRemark" column="reserve_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_no,order_id,
        user_name,create_time,status,
        remark,pay_time,pay_mode,
        airline_code,refund_type,office_no,
        ft_status,point_status,user_type,
        order_source,retrieve_time,change_id,
        retrieve_time_flag,coupon_no,coupon_type,
        constraint_reason,fictitious,reserve_remark
    </sql>

    <resultMap id="searchRefundOrderListResMap"
               type="net.jdair.specialrefund.bizz.domain.EtFltRefund">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="refund_no" property="refundNo"/>
        <result column="reserve_remark" property="reserveRemark"/>
        <result column="user_name" property="userName"/>
        <result column="create_time" property="createTime"/>
        <result column="retrieve_time" property="retrieveTime"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="constraint_reason" property="constraintReason"/>
        <result column="pay_mode" property="payMode"/>
        <result column="pay_time" property="payTime"/>
        <result column="refund_type" property="refundType"/>
        <result column="office_no" property="officeNo"/>
        <result column="user_type" property="userType"/>
        <result column="order_source" property="orderSource"/>
        <result column="fictitious" property="fictitious"/>
        <!--  通过退单id作为条件，查询退票航段列表 -->
        <collection property="etPassengerSegmentList" column="ps_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtPassengerSegment"
                    resultMap="searchRefundOrderListPsResMap"/>

        <!--  通过退单id作为条件，查询旅客航段中间表 -->
        <collection property="etFltRefundPaxSegList"
                    column="rps_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg"
                    resultMap="searchRefundOrderListRpsResMap"/>

        <!--  通过退单id作为条件，查询银行表 -->
        <collection property="etSpecialRefundBankInfo"
                    column="bank_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo"
                    resultMap="bankInfoResMap"/>
        <!--  通过退单id作为条件，查询审核记录 -->
        <collection property="fltRefundAudit"
                    column="audit_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtFltRefundAudit"
                    resultMap="auditResMap"/>
        <!--  通过退单id作为条件，查询图片表 -->
        <collection property="etSpecialRefundImgList"
                    column="img_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg"
                    resultMap="imgResMap"/>
    </resultMap>

    <resultMap id="searchRefundOrderListRpsResMap"
               type="net.jdair.specialrefund.bizz.domain.EtFltRefundPaxSeg"
               extends="net.jdair.specialrefund.bizz.mapper.EtFltRefundPaxSegMapper.BaseResultMap">
        <id property="id" column="rps_id"/>
    </resultMap>

    <resultMap id="bankInfoResMap"
               type="net.jdair.specialrefund.bizz.domain.EtSpecialRefundBankInfo">
        <id property="id" column="bank_id"/>
        <result property="cardNo" column="bank_card_no" jdbcType="VARCHAR"/>
        <result property="cardHolder" column="bank_card_holder" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_bank_name" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="bank_mobile_no" jdbcType="VARCHAR"/>
        <result property="refundNo" column="bank_refund_no" jdbcType="VARCHAR"/>
        <result property="fltRefundId" column="bank_flt_refund_id" jdbcType="DECIMAL"/>
        <result property="nbkno" column="bank_nbkno" jdbcType="VARCHAR"/>
        <result property="remark" column="bank_remark" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="auditResMap"
               type="net.jdair.specialrefund.bizz.domain.EtFltRefundAudit">
        <id property="id" column="audit_id"/>
        <result property="refundId" column="audit_refund_id" jdbcType="DECIMAL"/>
        <result property="submitUsername" column="audit_submit_username" jdbcType="VARCHAR"/>
        <result property="submitTime" column="audit_submit_time" jdbcType="TIMESTAMP"/>
        <result property="auditUsername" column="audit_audit_username" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_audit_time" jdbcType="TIMESTAMP"/>
        <result property="action" column="audit_action" jdbcType="VARCHAR"/>
        <result property="notion" column="audit_notion" jdbcType="VARCHAR"/>
        <result property="amountstr" column="audit_amountstr" jdbcType="VARCHAR"/>
        <result property="auditResult" column="audit_audit_result" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="imgResMap"
               type="net.jdair.specialrefund.bizz.domain.EtSpecialRefundImg">
        <id property="id" column="img_id"/>
        <result property="fltRefundId" column="img_flt_refund_id" jdbcType="DECIMAL"/>
        <result property="imgUrl" column="img_img_url" jdbcType="VARCHAR"/>
        <result property="imgOriginName" column="img_img_origin_name" jdbcType="VARCHAR"/>
        <result property="refundNo" column="img_refund_no" jdbcType="VARCHAR"/>
        <result property="source" column="img_source" jdbcType="VARCHAR"/>
        <result property="status" column="img_status" jdbcType="VARCHAR"/>
        <result property="operator" column="img_operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="img_create_time" jdbcType="TIMESTAMP"/>
        <result property="refundAuditId" column="img_refund_audit_id" jdbcType="DOUBLE"/>
    </resultMap>


    <resultMap id="searchRefundOrderListPsResMap"
               type="net.jdair.specialrefund.bizz.domain.EtPassengerSegment"
               extends="net.jdair.specialrefund.bizz.mapper.EtPassengerSegmentMapper.BaseResultMap">
        <id property="id" column="ps_id"/>
        <collection property="etSegment"
                    column="s_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtSegment"
                    resultMap="searchRefundOrderListSegResMap"/>
        <collection property="etTicket"
                    column="t_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtTicket"
                    resultMap="searchRefundOrderListTicketResMap"/>
        <collection property="etPassenger"
                    column="p_id"
                    ofType="net.jdair.specialrefund.bizz.domain.EtPassenger"
                    resultMap="searchRefundOrderListPassengerResMap"/>
    </resultMap>

    <resultMap id="searchRefundOrderListSegResMap"
               type="net.jdair.specialrefund.bizz.domain.EtSegment"
               extends="net.jdair.specialrefund.bizz.mapper.EtSegmentMapper.BaseResultMap">
        <id property="id" column="s_id"/>
    </resultMap>

    <resultMap id="searchRefundOrderListTicketResMap"
               type="net.jdair.specialrefund.bizz.domain.EtTicket"
               extends="net.jdair.specialrefund.bizz.mapper.EtTicketMapper.BaseResultMap">
        <id property="id" column="t_id"/>
    </resultMap>

    <resultMap id="searchRefundOrderListPassengerResMap"
               type="net.jdair.specialrefund.bizz.domain.EtPassenger"
               extends="net.jdair.specialrefund.bizz.mapper.EtPassengerMapper.BaseResultMap">
        <id property="id" column="p_id"/>
        <collection property="customer"
                    column="c_id"
                    ofType="net.jdair.specialrefund.bizz.domain.Customer"
                    resultMap="searchRefundOrderListCustomerResMap"/>
    </resultMap>

    <resultMap id="searchRefundOrderListCustomerResMap"
               type="net.jdair.specialrefund.bizz.domain.Customer"
               extends="net.jdair.specialrefund.bizz.mapper.CustomerMapper.BaseResultMap">
        <id property="id" column="c_id"/>
        <id property="remark" column="c_remark"/>
    </resultMap>

    <resultMap id="searchRefundListResForPageMap"
               type="net.jdair.specialrefund.bizz.domain.EtFltRefund">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="refund_no" property="refundNo"/>
        <result column="reserve_remark" property="reserveRemark"/>
        <result column="user_name" property="userName"/>
        <result column="create_time" property="createTime"/>
        <result column="retrieve_time" property="retrieveTime"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="constraint_reason" property="constraintReason"/>
        <result column="pay_mode" property="payMode"/>
        <result column="pay_time" property="payTime"/>
        <result column="refund_type" property="refundType"/>
        <result column="office_no" property="officeNo"/>
        <result column="user_type" property="userType"/>
        <result column="order_source" property="orderSource"/>
        <result column="fictitious" property="fictitious"/>
    </resultMap>

    <!-- 条件查询退票单列表 -->
    <select id="findRefundByParams" resultMap="searchRefundOrderListResMap">
        SELECT r.id,
        r.order_id,
        r.refund_no,
        r.reserve_remark,
        r.user_name,
        r.create_time,
        r.retrieve_time,
        r.status,
        r.remark,
        r.constraint_reason,
        r.pay_mode,
        r.pay_time,
        r.refund_type,
        r.office_no,
        r.user_type,
        r.order_source,
        r.fictitious,
        rps.id as rps_id,
        rps.*,
        ps.id as ps_id,
        ps.*,
        s.id as s_id,
        s.*,
        t.id as t_id,
        t.*,
        p.id as p_id,
        p.*,
        c.id as c_id,
        c.remark as c_remark,
        c.*
        FROM et_flt_refund r
        JOIN et_flt_refund_pax_seg rps ON r.id = rps.refund_id
        JOIN et_passenger_segment ps ON rps.pax_seg_id = ps.id
        JOIN et_segment s ON ps.segment_id = s.id
        JOIN et_ticket t ON ps.ticket_id = t.id
        JOIN et_passenger p ON ps.passenger_id = p.id
        JOIN customer c ON p.customer_id = c.id
        WHERE 1 = 1
        and r.id = rps.refund_id
        and rps.pax_seg_id = ps.id
        and ps.segment_id = s.id
        and ps.ticket_id = t.id
        and ps.passenger_id = p.id
        and p.customer_id = c.id
        <if test="openid != null and openid != ''">
            and r.user_name = #{openid}
        </if>
        <if test="issCode != null and issCode != ''">
            and t.iss_code = #{issCode}
        </if>
        <if test="ticketNo != null and ticketNo != ''">
            and t.ticket_no = #{ticketNo}
        </if>
        <if test="passengerName != null and passengerName != ''">
            and c.name = #{passengerName}
        </if>
        <if test="refundNo != null and refundNo != ''">
            and r.refund_no = #{refundNo}
        </if>
        <if test="flightNo != null and flightNo != ''">
            and s.flight_no = #{flightNo}
        </if>
        <if test="depCode != null and depCode != ''">
            and s.dep_code = #{depCode}
        </if>
        <if test="arrCode != null and arrCode != ''">
            and s.arr_code = #{arrCode}
        </if>
        <if test="refundStatus != null and refundStatus != ''">
            and r.status = #{refundStatus}
        </if>
        <if test="refundType != null and refundType != ''">
            and r.refund_type = #{refundType}
        </if>
        <if test="constraintReason != null and constraintReason != ''">
            and r.constraint_reason = #{constraintReason}
        </if>
        <if test="refundUserType != null and refundUserType != ''">
            and r.fictitious = #{refundUserType}
        </if>
        <if test="officeNo != null and officeNo != ''">
            and r.office_no = #{officeNo}
        </if>
        <if test="orderSource != null and orderSource != ''">
            and r.order_source = #{orderSource}
        </if>
        <if test="orderSourceList != null and orderSourceList != ''">
            and r.order_source in
            <foreach collection="orderSourceList"  item="orderSource" open="(" close=")" separator=",">
                #{orderSource}
            </foreach>
        </if>
        <if test="refundDateStart != null and refundDateStart != '' ">
            and date_format(r.create_time, '%Y-%m-%d') &gt;= #{refundDateStart}
        </if>
        <if test="refundDateEnd != null and refundDateEnd != ''">
            and date_format(r.create_time, '%Y-%m-%d') &lt;= #{refundDateEnd}
        </if>
        <if test="flightDateStart != null and flightDateStart != '' ">
            and date_format(s.dep_time, '%Y-%m-%d') &gt;= #{flightDateStart}
        </if>
        <if test="flightDateEnd != null and flightDateEnd != ''">
            and date_format(s.dep_time, '%Y-%m-%d') &lt;= #{flightDateEnd}
        </if>
        order by r.id desc;
    </select>

    <!-- 分页条件查询退票单列表 -->
    <select id="findRefundListByParamsForPage" resultMap="searchRefundListResForPageMap">
        SELECT
        r.id,
        r.order_id,
        r.refund_no,
        r.reserve_remark,
        r.user_name,
        r.create_time,
        r.retrieve_time,
        r.status,
        r.remark,
        r.constraint_reason,
        r.pay_mode,
        r.pay_time,
        r.refund_type,
        r.office_no,
        r.user_type,
        r.order_source,
        r.fictitious
        FROM et_flt_refund r
        WHERE 1 = 1
        <if test="param.openid != null and param.openid != ''">
            AND r.user_name = #{param.openid}
        </if>
        <if test="param.issCode != null and param.issCode != ''">
            AND EXISTS (
            SELECT 1 FROM et_ticket t
            WHERE t.iss_code = #{param.issCode}
            AND t.id IN (SELECT ps.ticket_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.ticketNo != null and param.ticketNo != ''">
            AND EXISTS (
            SELECT 1 FROM et_ticket t
            WHERE t.ticket_no = #{param.ticketNo}
            AND t.id IN (SELECT ps.ticket_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.passengerName != null and param.passengerName != ''">
            AND EXISTS (
            SELECT 1 FROM customer c
            WHERE c.name = #{param.passengerName}
            AND c.id IN (SELECT p.customer_id FROM et_passenger p WHERE p.id IN (SELECT ps.passenger_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id)))
            )
        </if>
        <if test="param.refundNo != null and param.refundNo != ''">
            AND r.refund_no = #{param.refundNo}
        </if>
        <if test="param.flightNo != null and param.flightNo != ''">
            AND EXISTS (
            SELECT 1 FROM et_segment s
            WHERE s.flight_no = #{param.flightNo}
            AND s.id IN (SELECT ps.segment_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.depCode != null and param.depCode != ''">
            AND EXISTS (
            SELECT 1 FROM et_segment s
            WHERE s.dep_code = #{param.depCode}
            AND s.id IN (SELECT ps.segment_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.arrCode != null and param.arrCode != ''">
            AND EXISTS (
            SELECT 1 FROM et_segment s
            WHERE s.arr_code = #{param.arrCode}
            AND s.id IN (SELECT ps.segment_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.refundStatus != null and param.refundStatus != ''">
            AND r.status = #{param.refundStatus}
        </if>
        <if test="param.refundType != null and param.refundType != ''">
            AND r.refund_type = #{param.refundType}
        </if>
        <if test="param.constraintReason != null and param.constraintReason != ''">
            and r.constraint_reason = #{param.constraintReason}
        </if>
        <if test="param.refundUserType != null and param.refundUserType != ''">
            and r.fictitious = #{param.refundUserType}
        </if>
        <if test="param.officeNo != null and param.officeNo != ''">
            and r.office_no = #{param.officeNo}
        </if>
        <if test="param.orderSource != null and param.orderSource != ''">
            and r.order_source = #{param.orderSource}
        </if>
        <if test="param.orderSourceList != null and param.orderSourceList != ''">
            and r.order_source in
            <foreach collection="param.orderSourceList" item="orderSource" open="(" close=")" separator=",">
                #{orderSource}
            </foreach>
        </if>
        <if test="param.refundDateStart != null and param.refundDateStart != '' ">
            and date_format(r.create_time, '%Y-%m-%d') &gt;= #{param.refundDateStart}
        </if>
        <if test="param.refundDateEnd != null and param.refundDateEnd != ''">
            and date_format(r.create_time, '%Y-%m-%d') &lt;= #{param.refundDateEnd}
        </if>

        <if test="param.flightDateStart != null and param.flightDateStart != ''">
            AND EXISTS (
            SELECT 1 FROM et_segment s
            WHERE DATE_FORMAT(s.dep_time, '%Y-%m-%d') &gt;= #{param.flightDateStart}
            AND s.id IN (SELECT ps.segment_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.flightDateEnd != null and param.flightDateEnd != ''">
            AND EXISTS (
            SELECT 1 FROM et_segment s
            WHERE DATE_FORMAT(s.dep_time, '%Y-%m-%d') &lt;= #{param.flightDateEnd}
            AND s.id IN (SELECT ps.segment_id FROM et_passenger_segment ps WHERE ps.id IN (SELECT rps.pax_seg_id FROM et_flt_refund_pax_seg rps WHERE rps.refund_id = r.id))
            )
        </if>
        <if test="param.paymentNo != null and param.paymentNo != ''">
            AND EXISTS (
            SELECT 1 FROM et_payment pay
            WHERE pay.payment_no = #{param.paymentNo}
            AND pay.id IN (SELECT rpay.payment_id FROM et_flt_refund_payment rpay WHERE rpay.flt_refund_id = r.id)
            )
        </if>
        ORDER BY r.create_time DESC
    </select>


    <!-- 按refund_ids查询退票单明细 -->
    <select id="findRefundDetailByIdsForPage" resultMap="searchRefundOrderListResMap">
        SELECT
        r.id,
        rps.id AS rps_id,
        rps.*,
        ps.id AS ps_id,
        ps.*,
        s.id AS s_id,
        s.*,
        audit.id as audit_id,
        audit.refund_id as audit_refund_id,
        audit.submit_username as audit_submit_username,
        audit.submit_time as audit_submit_time,
        audit.audit_username as audit_audit_username,
        audit.audit_time as audit_audit_time,
        audit.action as audit_action,
        audit.notion as audit_notion,
        audit.amountstr as audit_amountstr,
        audit.audit_result as audit_audit_result
        from et_flt_refund r
        LEFT JOIN et_flt_refund_pax_seg rps ON r.id = rps.refund_id
        LEFT JOIN et_passenger_segment ps ON rps.pax_seg_id = ps.id
        LEFT JOIN et_segment s ON ps.segment_id = s.id
        LEFT JOIN et_flt_refund_audit audit on audit.refund_id = r.id
        WHERE r.id IN
        <foreach collection="refundIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>
