<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtRefundGjImgMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtRefundGjImg">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fltRefundId" column="flt_refund_id" jdbcType="BIGINT"/>
            <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
            <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="refundAuditId" column="refund_audit_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,flt_refund_id,img_url,
        refund_no,create_time,refund_audit_id
    </sql>
</mapper>
