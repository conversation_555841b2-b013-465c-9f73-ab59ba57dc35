<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTsRefundMonitorRecordMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtTsRefundMonitorRecord">
        <id property="id" column="id" jdbcType="DECIMAL"/>
        <id property="preMaxPaymentId" column="pre_max_payment_id" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pre_max_payment_id, create_time, update_time
    </sql>

</mapper>
