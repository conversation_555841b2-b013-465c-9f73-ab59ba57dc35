<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtTsMultiRefundTagMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtTsMultiRefundTag">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="onlineRefundStatus" column="online_refund_status" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,online_refund_status,create_time,
        update_time
    </sql>
</mapper>
