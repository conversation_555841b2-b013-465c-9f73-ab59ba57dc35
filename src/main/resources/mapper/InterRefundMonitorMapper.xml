<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.InterRefundMonitorMapper">

    <select id="queryMaxPaymentId" resultType="java.lang.Long">
        select max(id) from et_payment
    </select>

    <!-- 同一个订单产生二次退款（二审通过或已退款） -->
    <select id="queryByMultiPayment" resultType="net.jdair.specialrefund.bizz.domain.InterRefundMonitorRecord">
        select r.refund_no
        from et_flt_refund r,
             (select p.id, p.amount, rp.flt_refund_id
              from et_payment p,
                   et_flt_refund_payment rp
              where p.id > #{preMaxPaymentId}
                and p.id = rp.payment_id
                and p.pay_status = 'PAID') pid_amounts
        where r.id = pid_amounts.flt_refund_id
          and (r.order_source = 'GJ' or r.order_source = 'HWZ')
        GROUP BY r.refund_no
        HAVING COUNT(*) > 1
    </select>

</mapper>
