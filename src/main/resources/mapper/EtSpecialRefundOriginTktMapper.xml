<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtSpecialRefundOriginTktMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtSpecialRefundOriginTkt">
            <id property="id" column="id" jdbcType="DOUBLE"/>
            <result property="paxSegId" column="pax_seg_id" jdbcType="DOUBLE"/>
            <result property="passengerName" column="passenger_name" jdbcType="VARCHAR"/>
            <result property="passengerType" column="passenger_type" jdbcType="VARCHAR"/>
            <result property="certNo" column="cert_no" jdbcType="VARCHAR"/>
            <result property="depCode" column="dep_code" jdbcType="VARCHAR"/>
            <result property="arrCode" column="arr_code" jdbcType="VARCHAR"/>
            <result property="airlineCode" column="airline_code" jdbcType="VARCHAR"/>
            <result property="flightNo" column="flight_no" jdbcType="VARCHAR"/>
            <result property="cabinClass" column="cabin_class" jdbcType="VARCHAR"/>
            <result property="ticketNo" column="ticket_no" jdbcType="VARCHAR"/>
            <result property="depTime" column="dep_time" jdbcType="TIMESTAMP"/>
            <result property="arrTime" column="arr_time" jdbcType="TIMESTAMP"/>
            <result property="marketFare" column="market_fare" jdbcType="DECIMAL"/>
            <result property="netFare" column="net_fare" jdbcType="DECIMAL"/>
            <result property="airportTax" column="airport_tax" jdbcType="DECIMAL"/>
            <result property="fuelTax" column="fuel_tax" jdbcType="DECIMAL"/>
            <result property="otherTaxes" column="other_taxes" jdbcType="DECIMAL"/>
            <result property="tax1" column="tax1" jdbcType="DECIMAL"/>
            <result property="tax2" column="tax2" jdbcType="DECIMAL"/>
            <result property="tax3" column="tax3" jdbcType="DECIMAL"/>
            <result property="tax4" column="tax4" jdbcType="DECIMAL"/>
            <result property="ticketStatus" column="ticket_status" jdbcType="VARCHAR"/>
            <result property="sequence" column="sequence" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pax_seg_id,passenger_name,
        passenger_type,cert_no,dep_code,
        arr_code,airline_code,flight_no,
        cabin_class,ticket_no,dep_time,
        arr_time,market_fare,net_fare,
        airport_tax,fuel_tax,other_taxes,
        tax1,tax2,tax3,
        tax4,ticket_status,sequence
    </sql>
</mapper>
