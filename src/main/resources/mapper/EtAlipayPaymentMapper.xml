<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtAlipayPaymentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtAlipayPayment">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="payerName" column="payer_name" jdbcType="VARCHAR"/>
        <result property="payerContactType" column="payer_contact_type" jdbcType="VARCHAR"/>
        <result property="payerContact" column="payer_contact" jdbcType="VARCHAR"/>
        <result property="dealId" column="deal_id" jdbcType="VARCHAR"/>
        <result property="bankId" column="bank_id" jdbcType="VARCHAR"/>
        <result property="bankDealId" column="bank_deal_id" jdbcType="VARCHAR"/>
        <result property="fee" column="fee" jdbcType="DECIMAL"/>
        <result property="sellerEmail" column="seller_email" jdbcType="VARCHAR"/>
        <result property="buyerEmail" column="buyer_email" jdbcType="VARCHAR"/>
        <result property="sellerId" column="seller_id" jdbcType="VARCHAR"/>
        <result property="buyerId" column="buyer_id" jdbcType="VARCHAR"/>
        <result property="callbackinfo" column="callbackinfo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,payer_name,payer_contact_type,
        payer_contact,deal_id,bank_id,
        bank_deal_id,fee,seller_email,
        buyer_email,seller_id,buyer_id,
        callbackinfo
    </sql>

    <insert id="insert" parameterType="net.jdair.specialrefund.bizz.domain.EtAlipayPayment">
        insert into et_payment_alip(id, payer_name, payer_contact_type,
                                    payer_contact, deal_id, bank_id,
                                    bank_deal_id, fee, seller_email,
                                    buyer_email, seller_id, buyer_id,
                                    callbackinfo)
        values (#{id}, #{payerName}, #{payerContactType},
                #{payerContact}, #{dealId}, #{bankId},
                #{bankDealId}, #{fee}, #{sellerEmail},
                #{buyerEmail}, #{sellerId}, #{buyerId},
                #{callbackinfo})
    </insert>

    <select id="findById" resultType="net.jdair.specialrefund.bizz.domain.EtAlipayPayment">
        select <include refid="Base_Column_List"></include> from et_payment_alip a where a.id = #{paymentId}
    </select>
</mapper>
