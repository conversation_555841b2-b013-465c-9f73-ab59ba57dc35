<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtPassengerMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtPassenger">
            <id property="id" column="id" jdbcType="DECIMAL"/>
            <result property="customerId" column="customer_id" jdbcType="DECIMAL"/>
            <result property="pnrId" column="pnr_id" jdbcType="DECIMAL"/>
            <result property="orderId" column="order_id" jdbcType="DECIMAL"/>
            <result property="passengerType" column="passenger_type" jdbcType="VARCHAR"/>
            <result property="accompany" column="accompany" jdbcType="DECIMAL"/>
            <result property="changeNameTime" column="change_name_time" jdbcType="SMALLINT"/>
            <result property="changeCertiTime" column="change_certi_time" jdbcType="SMALLINT"/>
            <result property="xcdPrintStatus" column="xcd_print_status" jdbcType="VARCHAR"/>
            <result property="mobilePhone" column="mobile_phone" jdbcType="VARCHAR"/>
            <result property="isFlightsms" column="is_flightsms" jdbcType="DECIMAL"/>
            <result property="coPromote" column="co_promote" jdbcType="VARCHAR"/>
            <result property="memberPlusNo" column="member_plus_no" jdbcType="VARCHAR"/>
            <result property="encrypted" column="encrypted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,pnr_id,
        order_id,passenger_type,accompany,
        change_name_time,change_certi_time,xcd_print_status,
        mobile_phone,is_flightsms,co_promote,
        member_plus_no,encrypted
    </sql>
</mapper>
