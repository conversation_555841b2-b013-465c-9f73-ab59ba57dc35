<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.jdair.specialrefund.bizz.mapper.EtWXpayPaymentMapper">

    <resultMap id="BaseResultMap" type="net.jdair.specialrefund.bizz.domain.EtWXpayPayment">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="dealId" column="deal_id" jdbcType="VARCHAR"/>
        <result property="bankId" column="bank_id" jdbcType="VARCHAR"/>
        <result property="fee" column="fee" jdbcType="DECIMAL"/>
        <result property="callbackinfo" column="callbackinfo" jdbcType="VARCHAR"/>
        <result property="transname" column="transname" jdbcType="VARCHAR"/>
        <result property="purchaserId" column="purchaser_id" jdbcType="VARCHAR"/>
        <result property="attach" column="attach" jdbcType="VARCHAR"/>
        <result property="cmdno" column="cmdno" jdbcType="VARCHAR"/>
        <result property="signmsg" column="signmsg" jdbcType="VARCHAR"/>
        <result property="busType" column="bus_type" jdbcType="VARCHAR"/>
        <result property="busArgs" column="bus_args" jdbcType="VARCHAR"/>
        <result property="busDesc" column="bus_desc" jdbcType="VARCHAR"/>
        <result property="payResult" column="pay_result" jdbcType="VARCHAR"/>
        <result property="refundId" column="refund_id" jdbcType="VARCHAR"/>
        <result property="bankDealId" column="bank_deal_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,deal_id,bank_id,
        fee,callbackinfo,transname,
        purchaser_id,attach,cmdno,
        signmsg,bus_type,bus_args,
        bus_desc,pay_result,refund_id,
        bank_deal_id
    </sql>

    <insert id="insert" parameterType="net.jdair.specialrefund.bizz.domain.EtWXpayPayment">
        insert into et_payment_wxpay(id, deal_id, bank_id,
                                    fee, callbackinfo, transname,
                                    purchaser_id, attach, cmdno,
                                    signmsg, bus_type, bus_args,
                                    bus_desc, pay_result, refund_id,
                                    bank_deal_id)
        values (#{id}, #{dealId}, #{bankId},
                #{fee}, #{callbackinfo}, #{transname},
                #{purchaserId}, #{attach}, #{cmdno},
                #{signmsg}, #{busType}, #{busArgs},
                #{busDesc}, #{payResult}, #{refundId},
                #{bankDealId}
                )
    </insert>


    <select id="findById" resultType="net.jdair.specialrefund.bizz.domain.EtWXpayPayment">
        select <include refid="Base_Column_List"></include> from et_payment_wxpay a where a.id = #{paymentId}
    </select>
</mapper>
