package net.jdair.specialrefund;

import net.jdair.specialrefund.Application;
import org.jasypt.encryption.StringEncryptor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TestJasypt {
    @Autowired
    private StringEncryptor stringEncryptor;

    @Test
    public void testContextLoads() {
        // 加密
        String originStr = "J<PERSON>r@123";
        String encodeStr = stringEncryptor.encrypt(originStr);

        System.out.println("originStr: " + originStr);
        System.out.println("encodeStr: " + encodeStr);

        System.out.println("==============================================");
        //解密
        String encodeStr1 = "kCvyOyON0WxD+lIvCImecQ==";
        String originStr1 = stringEncryptor.decrypt(encodeStr1);

        System.out.println("encodeStr1: " + encodeStr1);
        System.out.println("originStr1: " + originStr1);

        //解密
        String encodeStr2 = "XBO3jkATIoApIcp3zAdjZjiaVLfv1OPy";
        String originStr2 = stringEncryptor.decrypt(encodeStr2);

        System.out.println("encodeStr2: " + encodeStr2);
        System.out.println("originStr2: " + originStr2);

    }
}
