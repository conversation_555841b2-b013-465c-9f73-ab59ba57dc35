package net.jdair.specialrefund;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import net.jdair.specialrefund.bizz.controller.backend.domain.SearchRefundOrderListReq;
import net.jdair.specialrefund.bizz.domain.*;
import net.jdair.specialrefund.bizz.mapper.*;
import net.jdair.specialrefund.bizz.utils.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
//@SpringBootTest
@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Rollback(value = false)
public class TestMyBatisPlus {

    @Autowired
    private EtFltRefundMapper etFltRefundMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private EtSegmentMapper etSegmentMapper;
    @Autowired
    private EtPassengerMapper etPassengerMapper;
    @Autowired
    private EtTicketMapper etTicketMapper;
    @Autowired
    private EtPassengerSegmentMapper etPassengerSegmentMapper;
    @Autowired
    private EtFltRefundPaxSegMapper etFltRefundPaxSegMapper;
    @Autowired
    private EtFltRefundAuditMapper etFltRefundAuditMapper;
    @Autowired
    private EtPaymentMapper etPaymentMapper;
    @Autowired
    private EtFltRefundOutsidePaymentMapper etFltRefundOutsidePaymentMapper;
    @Autowired
    private EtTicketAlltaxGjMapper etTicketAlltaxGjMapper;


    @Test
    @Transactional
    public void testHWZInsert() {
/*
insert into ET_FLT_REFUND values(733547,'20200628092045235811',1,'JDADMIN',sysdate,'NEW','测试国际票',null,null,'JD','UNCONSTRAINT','','','',''
,'GJ',sysdate,0,0,null,null,null,null,null,null);
insert into et.ET_FLT_REFUND_AUDIT values(1073017,733547,'TEST',sysdate,'JDADMIN',sysdate,'FIRSTAUDIT','测试国际票',null);
insert into ET_FLT_REFUND_PAX_SEG values(1043026,733547,15376949,0,0,0,0);
insert into ET_PASSENGER_SEGMENT values(15376954,13550975,8492198,12579374,1000,1000,100,0,0,null,null,null,null,0,0,0,0,0,null,null,null,null,'R','XX',221,1,0
,null,null,null,null,null,0,1000,0,0,null,0); --需要维护一个国际虚拟产品 221   旅客表13550975
insert into ET_SEGMENT values(8492198,0,'PEK','PEK','X','JD123',sysdate,sysdate,'320',null,'JD','IBE',20000); --需要检查国际三字码是否都在表里
insert into et.et_ticket values(12579374,'898','*********',null,null,0,0,null,0,null,null,null,sysdate,sysdate);
insert into et.et_payment values(9316767,'2020070622001494311428135534','ALIP','PAY',00.1,'PAID',sysdate,'RMB','','',null,null,null,null,null
,'2088421702658252',null,sysdate,null,null,'GJ',null);
insert into et.ET_PAYMENT_ALIP values(9316767,null,'C',null,'2020070622001494311428135534',null,null,0,null,null,'2088421702658252',null,null);
*/
        /**
         * INSERT INTO et_flt_refund (ID, REFUND_NO, ORDER_ID, USER_NAME, CREATE_TIME, STATUS, REMARK, PAY_TIME, PAY_MODE,
         *                               AIRLINE_CODE, REFUND_TYPE, OFFICE_NO, FT_STATUS, POINT_STATUS, USER_TYPE, ORDER_SOURCE,
         *                               RETRIEVE_TIME, CHANGE_ID, RETRIEVE_TIME_FLAG, COUPON_NO, COUPON_TYPE, CONSTRAINT_REASON,
         *                               FICTITIOUS, RESERVE_REMARK)
         * VALUES (********, '20230105230756564711', 1, '<EMAIL>', '',
         *         '2023-01-05 23:07:52', 'PAID', 'test refund',
         *         '2023-01-05 23:21:46', 'OFFLINE', 'JD', 'UNCONSTRAINT', '00000098', null,
         *         null, null, 'HWZ', null, null, 0, null, null, null, null, 'irH24Kwj9XjTlw8sWvVrzzu9zCfltslO6iuOoG3D');
         */
        EtFltRefund etFltRefund = new EtFltRefund();
        etFltRefund.setRefundNo("20230105230756564711");
        etFltRefund.setOrderId(1L);
        etFltRefund.setUserName("<EMAIL>");
        etFltRefund.setCreateTime(new Date());
        etFltRefund.setStatus("PAID");
        etFltRefund.setRemark(null);
        etFltRefund.setPayTime(new Date());
        etFltRefund.setPayMode("OFFLINE");
        etFltRefund.setAirlineCode("JD");
        etFltRefund.setRefundType("UNCONSTRAINT");
        etFltRefund.setOfficeNo("00000098");
        etFltRefund.setFtStatus(null);
        etFltRefund.setPointStatus(null);
        etFltRefund.setUserType(null);
        etFltRefund.setOrderSource("HWZ");
        etFltRefund.setRetrieveTime(null);
        etFltRefund.setChangeId(null);
        etFltRefund.setRetrieveTimeFlag(null);
        etFltRefund.setCouponNo(null);
        etFltRefund.setCouponType(null);
        etFltRefund.setConstraintReason(null);
        etFltRefund.setFictitious(null);
        etFltRefund.setReserveRemark("irH24Kwj9XjTlw8sWvVrzzu9zCfltslO6iuOoG3D");

        /**
         * INSERT INTO customer (NAME, CERTIFICATENO, CERTIFICATETYPE, FREQUENCENO, GENDER, BIRTHDAY, ID, OTHERNAME,
         *                          FREQUENTFLYERNO, FIRSTNAME, LASTNAME, NATIONALITY, PASSPORTNO, EXPIRED_DATE, BORN_DATE, REMARK,
         *                          ISSUE_COUNTRY, DEPCITY, DEPPROVINCE, DEPCOUNTRY, DEPSTREET, DEPPOST, ARRCITY, ARRPROVINCE,
         *                          ARRCOUNTRY, ARRPOST, ARRSTREET, JJCTYPE, JJCNO, ENCRYPTED, UPDATE_DATE)
         * VALUES ('TEST/LIMINGMING MR', 'eHO/u42f5CP8REHIAjlcSQ==', 'PP', null, 'M', null, 64226240, null, null, null, null, null,
         *         null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1',
         *         null);
         */
        Customer customer = new Customer();
        customer.setName("TEST/LIMINGMING MR");
        customer.setCertificateno("eHO/u42f5CP8REHIAjlcSQ==");
        customer.setCertificatetype("PP");
        customer.setFrequenceno(null);
        customer.setGender("M");
        customer.setEncrypted("1");


        /**
         *INSERT INTO et_segment (ID, ORDER_ID, DEP_CODE, ARR_CODE, CABIN_CLASS, FLIGHT_NO, DEP_TIME, ARR_TIME, PLANE_TYPE,
         *                            GROUP_NO, AIRLINE_CODE, INVENTORY, BASE_FARE, ARR_TERMINAL, DEP_TERMINAL, STOP)
         * VALUES (80188658, 1, 'LHR', 'TAO', 'P', 'JD432', '2023-02-24 22:00:00',
         *         '2023-02-25 16:50:00', null, null, 'JD', 'IBE', 0.00, null, null, null);
         */
        EtSegment etSegment = new EtSegment();
        etSegment.setOrderId(1L);
        etSegment.setDepCode("LHR");
        etSegment.setArrCode("TAO");
        etSegment.setCabinClass("P");
        etSegment.setFlightNo("JD432");
        etSegment.setDepTime(DateUtils.StringToDate("2023-02-24 22:00:00", "yyyy-MM-dd HH:mm:ss"));
        etSegment.setArrTime(DateUtils.StringToDate("2023-02-25 16:50:00", "yyyy-MM-dd HH:mm:ss"));
        etSegment.setAirlineCode("JD");
        etSegment.setInventory("IBE");
        etSegment.setBaseFare(new BigDecimal(0.00));

        /**
         * INSERT INTO et_passenger (ID, CUSTOMER_ID, PNR_ID, ORDER_ID, PASSENGER_TYPE, ACCOMPANY, CHANGE_NAME_TIME,
         *                              CHANGE_CERTI_TIME, XCD_PRINT_STATUS, MOBILE_PHONE, IS_FLIGHTSMS, CO_PROMOTE,
         *                              MEMBER_PLUS_NO, ENCRYPTED)
         * VALUES (*********, 64226240, null, null, 'ADULT', null, null, null, null, '+8615801304693', null, null, null, null);
         */
        EtPassenger etPassenger = new EtPassenger();
        etPassenger.setOrderId(1L);
        etPassenger.setPassengerType("ADULT");
        etPassenger.setMobilePhone("+8615801304693");

        /**
         * INSERT INTO et_ticket (ID, ISS_CODE, TICKET_NO, SERIAL_NO, SERIAL_NO_CK, PRINT_STATUS, BSP_TYPE, ENDORSEMENT, CK,
         *                           CONJUNCTION_TKT, AGENT_CODE, ISSUED_BY, ISSUED_DATE, BOOK_TIME)
         * VALUES (97238345, '898', '2400010341', null, null, '0', '1', null, null, null, null, null,
         *         '2023-01-05 23:04:50',
         *         '2023-01-05 23:04:50');
         */
        EtTicket etTicket = new EtTicket();
        etTicket.setIssCode("898");
        etTicket.setTicketNo("2400010341");
        etTicket.setPrintStatus("0");
        etTicket.setBspType("1");
        etTicket.setIssuedDate(DateUtils.StringToDate("2023-01-05 23:04:50", "yyyy-MM-dd HH:mm:ss"));
        etTicket.setBookTime(DateUtils.StringToDate("2023-01-05 23:04:50", "yyyy-MM-dd HH:mm:ss"));

        /**
         *INSERT INTO et_passenger_segment (ID, PASSENGER_ID, SEGMENT_ID, TICKET_ID, MARKET_FARE, NET_FARE, AIRPORT_TAX,
         *                                      FUEL_TAX, OTHER_TAXES, TAX1, TAX2, TAX3, TAX4, INSURANCE, AGENT_FEE_RATE,
         *                                      SP_FEE_RATE, AGENT_FEE, SP_FEE, FARE_BASIS, NOT_VALID_BEFORE, NOT_VALID_AFTER,
         *                                      ALLOW, TICKET_STATUS, STATUS, PRODUCT_ID, SEQUENCE, POINT, HAS_FREE_TICKET,
         *                                      USED_FREE_TICKET, USED, PAY_FREE_TICKET, CURRENCY, COUPON_PRICE, ORDER_ID,
         *                                      OLD_NET_FARE, POINT_PRICE, REF_CABIN, REF_FARE, ORI_PRODUCT_CODE)
         * VALUES (*********, *********, 80188658, 97238345, 1082.00, 1082.00, 0.00, 110.00, 253.14, 3.30, 139.84, null, null,
         *         null, null, null, null, null, null, null, null, null, 'O', 'RR', 1241, 1, 0, 'NO', 'NO', 'NO', 'NO', 'GBP',
         *         0.00, null, 0.00, 0.00, null, 0.00, null);
         */
        EtPassengerSegment etPassengerSegment = new EtPassengerSegment();
        etPassengerSegment.setMarketFare(new BigDecimal(1082.00));
        etPassengerSegment.setNetFare(new BigDecimal(1082.00));
        etPassengerSegment.setAirportTax(new BigDecimal(0));
        etPassengerSegment.setFuelTax(new BigDecimal(110.00));
        etPassengerSegment.setOtherTaxes(new BigDecimal(253.14));
        etPassengerSegment.setTax1(new BigDecimal(3.30));
        etPassengerSegment.setTax2(new BigDecimal(139.84));
        etPassengerSegment.setTicketStatus("O");
        etPassengerSegment.setStatus("RR");
        etPassengerSegment.setProductId(1L);
        etPassengerSegment.setSequence(1);
        etPassengerSegment.setPoint(0L);
        etPassengerSegment.setHasFreeTicket("NO");
        etPassengerSegment.setUsedFreeTicket("NO");
        etPassengerSegment.setUsed("NO");
        etPassengerSegment.setPayFreeTicket("NO");
        etPassengerSegment.setCurrency("GBP");

        /**
         * INSERT INTO et_flt_refund_pax_seg (ID, REFUND_ID, PAX_SEG_ID, REFUND_AMOUNT, ACTUAL_REFUND_AMOUNT, REFUND_POINT,
         *                                       AMS_REFUND_POINT)
         * VALUES (10029812, ********, *********, 0.00, 1335.14, 0, 0.00);
         */
        EtFltRefundPaxSeg etFltRefundPaxSeg = new EtFltRefundPaxSeg();
        etFltRefundPaxSeg.setRefundAmount(new BigDecimal(0.00));
        etFltRefundPaxSeg.setActualRefundAmount(new BigDecimal(1335.14));

        /**
         *
         * INSERT INTO et_flt_refund_audit (ID, REFUND_ID, SUBMMIT_USERNAME, SUBMMIT_TIME, AUDIT_USERNAME, AUDIT_TIME, ACTION,
         *                                     NOTION, AMOUNTSTR, AUDIT_RESULT)
         * VALUES (59055258, ********, 'laiyuan', '2023-01-05 23:07:56', null,
         *         '2023-01-05 23:07:56', 'FIRSTAUDIT', '海外站退票', null, null);
         */
        EtFltRefundAudit etFltRefundAudit = new EtFltRefundAudit();
        etFltRefundAudit.setSubmitUsername("laiyuan");
        etFltRefundAudit.setSubmitTime(DateUtils.StringToDate("2023-01-05 23:07:56", "yyyy-MM-dd HH:mm:ss"));
        etFltRefundAudit.setAuditTime(DateUtils.StringToDate("2023-01-05 23:07:56", "yyyy-MM-dd HH:mm:ss"));
        etFltRefundAudit.setAction("FIRSTAUDIT");
        etFltRefundAudit.setNotion("海外站退票");

        /**
         * INSERT INTO et_payment (ID, PAYMENT_NO, PAY_TYPE, ACTION, AMOUNT, PAY_STATUS, PAY_TIME, CURRENCY, PAYER, BATCH,
         *                            REMARK, SETT_DATE, FIELD1, FIELD2, FIELD3, MERCHANT_ID, PAY_MODE, RETURN_TIME, SYSTEM_SSN,
         *                            SOURCE, CARD_TYPE, BANK_ID)
         * VALUES (********, '20230105230756576534', 'WORLDPAY', 'PAY', 1335.14, 'PAID',
         *         '2023-01-05 23:07:55', 'RMB', null, null, 'A230105238999862', null, null,
         *         null, null, null, null, null, null, 'HWZ', null, null);
         */
        EtPayment etPayment = new EtPayment();
        etPayment.setPaymentNo("20230105230756576534");
        etPayment.setPayType("WORLDPAY");
        etPayment.setAction("PAY");
        etPayment.setAmount(new BigDecimal(1335.14));
        etPayment.setPayStatus("PAID");
        etPayment.setPayTime(DateUtils.StringToDate("2023-01-05 23:07:55", "yyyy-MM-dd HH:mm:ss"));
        etPayment.setCurrency("RMB");
        etPayment.setRemark("A230105238999862");
        etPayment.setSource("HWZ");

        /**
         * INSERT INTO et_flt_refund_outside_payment (ID, FLT_REFUND_ID, PAYMENT_ID, AMOUNT)
         * VALUES (67870, ********, ********, 0.00);
         */
        EtFltRefundOutsidePayment etFltRefundOutsidePayment = new EtFltRefundOutsidePayment();
        etFltRefundOutsidePayment.setAmount(new BigDecimal(0.00));

        /**
         * INSERT INTO et_ticket_alltax_gj (ID, TICKET_ID, ALL_TAX)
         * VALUES (11653, 97238345, 'YQ:3.3,YR:110.0,XT:139.84');
         */
        EtTicketAlltaxGj etTicketAlltaxGj = new EtTicketAlltaxGj();
        etTicketAlltaxGj.setAllTax("YQ:3.3,YR:110.0,XT:139.84");

        // 1. 插入 et_flt_refund
        etFltRefundMapper.insert(etFltRefund);
        // 2. 插入 customer
        customerMapper.insert(customer);
        // 3. 插入 et_segment
        etSegmentMapper.insert(etSegment);
        // 4. 插入 et_passenger
        etPassenger.setCustomerId(customer.getId());
        etPassengerMapper.insert(etPassenger);
        // 5. 插入 et_ticket
        etTicketMapper.insert(etTicket);
        // 6. 插入 et_passenger_segment
        etPassengerSegment.setPassengerId(etPassenger.getId());
        etPassengerSegment.setTicketId(etTicket.getId());
        etPassengerSegment.setSegmentId(etSegment.getId());
        etPassengerSegmentMapper.insert(etPassengerSegment);
        // 7. 插入 et_flt_refund_pax_seg
        etFltRefundPaxSeg.setPaxSegId(etPassengerSegment.getId());
        etFltRefundPaxSeg.setRefundId(etFltRefund.getId());
        etFltRefundPaxSegMapper.insert(etFltRefundPaxSeg);
        // 8. 插入 et_flt_refund_audit
        etFltRefundAudit.setRefundId(etFltRefund.getId());
        etFltRefundAuditMapper.insert(etFltRefundAudit);
        // 9. 插入 et_payment
        etPaymentMapper.insert(etPayment);
        // 10. 插入 et_flt_refund_outside_payment
        etFltRefundOutsidePayment.setFltRefundId(etFltRefund.getId());
        etFltRefundOutsidePayment.setPaymentId(etPayment.getId());
        etFltRefundOutsidePaymentMapper.insert(etFltRefundOutsidePayment);
        // 11. 插入 et_ticket_alltax_gj
        etTicketAlltaxGj.setTicketId(etTicket.getId());
        etTicketAlltaxGjMapper.insert(etTicketAlltaxGj);

        System.out.println(etFltRefund.getId());
    }


    @Test
    @Transactional
    public void testFindRefundByParams() {
        SearchRefundOrderListReq req = new SearchRefundOrderListReq();
        req.setRefundNo("20230112154025314936");
        List<EtFltRefund> refundList = etFltRefundMapper.findRefundByParams(req);
        System.out.println(JSON.toJSONString(refundList));
    }
}
