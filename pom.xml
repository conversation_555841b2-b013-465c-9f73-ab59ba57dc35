<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>net.jdair.specialrefund</groupId>
	<artifactId>jd-ms-ticket-refund</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<name>JD Microservice Ticket Refund</name>
	<description>JD Microservice Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.6.3</version>
		<relativePath/>
	</parent>

	<properties>
		<common-io>2.6</common-io>
		<commons-fileupload>1.3.3</commons-fileupload>
		<commons-lang3>3.7</commons-lang3>
		<!-- 主要依赖库的版本定义 -->
		<gson.version>2.8.5</gson.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2021.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>


	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<!-- spring cloud >>>-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!--<<< spring cloud-->

		<!-- feign -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>
		<!-- feign -->

		<!--hutool工具类  文档可参考：https://apidoc.gitee.com/loolly/hutool/ -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.5.1</version>
		</dependency>
		<!--mybatisplus相关-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId>
			<version>3.4.0</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.4.0</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter-test</artifactId>
			<version>3.5.1</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.3.1</version>
		</dependency>
		<!--mybatisplus相关-->

		<!-- Oracle jdbc 连接  -->
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ojdbc8.jar</systemPath>
		</dependency>

		<!-- mysql数据库驱动 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!-- druid数据库连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.1.22</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.22</version>
		</dependency>
		<!-- springboot测试 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--日志工具-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.21</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
		</dependency>

		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>7.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.54</version>
		</dependency>
		<!-- JWT -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>0.10.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<!--数据库密码加密工具-->
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.3</version>
		</dependency>

		<!-- 高版本redis的lettuce需要commons-pool2 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<!-- Spring Data Redis的启动器 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!-- swagger2 >>>-->
		<!--swagger 接口文档插件-->
		<!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2 -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<!--swagger UI工具-->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
			<version>1.9.6</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.5.22</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>1.5.22</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.9.2</version>
		</dependency>
		<!--<<< swagger2 -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- commons begin-->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${common-io}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons-fileupload}</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.7.0</version>
		</dependency>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.10.4</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.commons/org.apache.commons.lang -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
		</dependency>

		<!-- pagehelper 分页插件 -->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.4.2</version>
		</dependency>
		<!-- pagehelper 分页插件 -->

		<dependency>
			<groupId>com.travelsky</groupId>
			<artifactId>ebuildapi</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/ebuildapi-enc-2f.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.hnair</groupId>
			<artifactId>alipay-utf-8</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/alipay-utf-8.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.hnair</groupId>
			<artifactId>jbossall-client</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/jbossall-client.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.apache</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/commons-httpclient.jar</systemPath>
		</dependency>

		<!-- esb 接口  -->
		<dependency>
			<groupId>com.hnair</groupId>
			<artifactId>opcnet-api</artifactId>
			<version>2.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/libs/opcnet-api-2.0-SNAPSHOT.jar</systemPath>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.hnair.opcnet</groupId>-->
<!--			<artifactId>opcnet-esb-client</artifactId>-->
<!--			<version>2.0-RELEASE</version>-->
<!--			<scope>system</scope>-->
<!--			<systemPath>${project.basedir}/libs/opcnet-esb-client-2.0-RELEASE.jar</systemPath>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.hnair.opcnet</groupId>
			<artifactId>opcnet-esb-client</artifactId>
			<version>4.4.3-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.hnair.opcnet</groupId>
			<artifactId>opcnet-esb-dubbo-wrapper</artifactId>
			<version>4.4.3-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.hnair.opcnet</groupId>
			<artifactId>opcnet-esb-common</artifactId>
			<version>4.4.3-SNAPSHOT</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>dubbo</artifactId>-->
<!--			<version>2.5.8</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.zookeeper</groupId>-->
<!--			<artifactId>zookeeper</artifactId>-->
<!--			<version>3.4.6</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.101tec</groupId>-->
<!--			<artifactId>zkclient</artifactId>-->
<!--			<version>0.8</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.caucho</groupId>
			<artifactId>hessian</artifactId>
			<version>4.0.7</version>
		</dependency>
		<!-- esb 接口  -->
		<!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>2.1.4</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.alipay.sdk/alipay-sdk-java -->
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
			<version>4.35.59.ALL</version>
		</dependency>

		<!-- minio SDK 客户端依赖  -->
		<dependency>
			<groupId>com.hna.eking</groupId>
			<artifactId>utils-oss</artifactId>
			<version>1.0.2-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>okhttp</artifactId>
					<groupId>com.squareup.okhttp3</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.0.3</version>
		</dependency>

		<!-- minio SDK 客户端依赖  -->
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>
